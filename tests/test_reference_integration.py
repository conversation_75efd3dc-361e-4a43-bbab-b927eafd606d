"""
Test Reference Integration
Tests the integration of best practices from reference projects

Tests patterns from:
- UDS-main: Professional UDS protocol implementation
- DDT4All: ECU communication patterns
- PyRen: Renault-specific patterns
- Toyota sample data: Data processing patterns
- AndrOBD: Android OBD patterns
- EcuBus-Pro: Modular architecture
"""
import pytest
import asyncio
from unittest.mock import Mock, AsyncMock, patch
from datetime import datetime, timedelta

# Test imports for UDS patterns
from app.obd_interface.uds_transport import (
    UDSTransportInterface, CANTransport, TransportConfig, TransportProtocol,
    UDSMessage, AddressingType
)
from app.obd_interface.uds_session import (
    UDSSession, DiagnosticSession, SecurityLevel, UDSService
)

# Test imports for brand-specific patterns
from app.obd_interface.ecu_communicator import (
    ECUCommunicator, RenaultECUCommunicator, ECUManager, ECUType
)
from app.obd_interface.brand_protocols import (
    BrandProtocolFactory, ToyotaECUCommunicator, VAGECUCommunicator
)

# Test imports for data processing patterns
from app.obd_interface.data_processor import (
    DataProcessor, DataStream, OBDDataPoint, DataBuffer, DataValidator
)


class TestUDSTransportPatterns:
    """Test UDS transport patterns inspired by uds-main"""
    
    def test_transport_config_creation(self):
        """Test transport configuration creation"""
        config = TransportConfig(
            protocol=TransportProtocol.CAN,
            interface="socketcan",
            baudrate=500000,
            can_id_request=0x7E0,
            can_id_response=0x7E8
        )
        
        assert config.protocol == TransportProtocol.CAN
        assert config.interface == "socketcan"
        assert config.baudrate == 500000
        assert config.can_id_request == 0x7E0
    
    def test_uds_message_creation(self):
        """Test UDS message structure"""
        message = UDSMessage(
            data=b'\x10\x03',  # Diagnostic session control
            target_address=0x10,
            addressing_type=AddressingType.PHYSICAL
        )
        
        assert message.data == b'\x10\x03'
        assert message.target_address == 0x10
        assert message.addressing_type == AddressingType.PHYSICAL
        assert message.timestamp is not None
    
    @pytest.mark.asyncio
    async def test_can_transport_mock(self):
        """Test CAN transport with mocked interface"""
        config = TransportConfig(
            protocol=TransportProtocol.CAN,
            interface="mock",
            baudrate=500000
        )
        
        transport = CANTransport(config)
        
        # Mock the CAN interface
        with patch('can.interface.Bus') as mock_bus:
            mock_bus.return_value = Mock()
            
            # Test connection
            result = await transport.connect()
            assert result == True
            assert transport.is_connected == True
            
            # Test disconnection
            await transport.disconnect()
            assert transport.is_connected == False


class TestUDSSessionPatterns:
    """Test UDS session patterns inspired by DDT4All"""
    
    @pytest.mark.asyncio
    async def test_uds_session_creation(self):
        """Test UDS session creation"""
        mock_transport = Mock(spec=UDSTransportInterface)
        session = UDSSession(mock_transport, ecu_address=0x10)
        
        assert session.ecu_address == 0x10
        assert session.current_session == DiagnosticSession.DEFAULT
        assert session.security_level == SecurityLevel.LOCKED
        assert session.is_active == False
    
    @pytest.mark.asyncio
    async def test_session_start_mock(self):
        """Test session start with mocked transport"""
        mock_transport = Mock(spec=UDSTransportInterface)
        mock_transport.send_and_receive = AsyncMock()
        
        # Mock positive response
        mock_response = Mock()
        mock_response.data = b'\x50\x03'  # Positive response to session control
        mock_transport.send_and_receive.return_value = mock_response
        
        session = UDSSession(mock_transport, ecu_address=0x10)
        
        result = await session.start_session(DiagnosticSession.EXTENDED)
        
        assert result == True
        assert session.current_session == DiagnosticSession.EXTENDED
        assert session.is_active == True
    
    @pytest.mark.asyncio
    async def test_security_access_mock(self):
        """Test security access with mocked responses"""
        mock_transport = Mock(spec=UDSTransportInterface)
        mock_transport.send_and_receive = AsyncMock()
        
        session = UDSSession(mock_transport, ecu_address=0x10)
        session.is_active = True
        
        # Mock seed response
        seed_response = Mock()
        seed_response.data = b'\x67\x01\x12\x34\x56\x78'  # Seed response
        
        # Mock key response
        key_response = Mock()
        key_response.data = b'\x67\x02'  # Positive key response
        
        mock_transport.send_and_receive.side_effect = [seed_response, key_response]
        
        # Register mock security algorithm
        def mock_algorithm(seed: bytes) -> bytes:
            return b'\x87\x65\x43\x21'  # Mock key calculation
        
        session.register_security_algorithm(SecurityLevel.LEVEL_1, mock_algorithm)
        
        result = await session.request_security_access(SecurityLevel.LEVEL_1)
        
        assert result == True
        assert session.security_level == SecurityLevel.LEVEL_1


class TestBrandSpecificPatterns:
    """Test brand-specific patterns inspired by DDT4All and PyRen"""
    
    def test_brand_protocol_factory(self):
        """Test brand protocol factory"""
        mock_transport = Mock(spec=UDSTransportInterface)
        
        # Test Renault communicator creation
        renault_comm = BrandProtocolFactory.create_communicator("renault", mock_transport)
        assert isinstance(renault_comm, RenaultECUCommunicator)
        
        # Test Toyota communicator creation
        toyota_comm = BrandProtocolFactory.create_communicator("toyota", mock_transport)
        assert isinstance(toyota_comm, ToyotaECUCommunicator)
        
        # Test VAG communicator creation
        vag_comm = BrandProtocolFactory.create_communicator("volkswagen", mock_transport)
        assert isinstance(vag_comm, VAGECUCommunicator)
    
    @pytest.mark.asyncio
    async def test_renault_ecu_communicator(self):
        """Test Renault ECU communicator patterns"""
        mock_transport = Mock(spec=UDSTransportInterface)
        communicator = RenaultECUCommunicator(mock_transport, "Renault")
        
        assert communicator.brand == "Renault"
        assert ECUType.UCE in communicator.supported_ecus
        assert ECUType.UCH in communicator.supported_ecus
        
        # Test DF code patterns
        assert "DF001" in communicator.df_code_patterns
        assert "Injection system fault" in communicator.df_code_patterns["DF001"]
    
    @pytest.mark.asyncio
    async def test_toyota_ecu_communicator(self):
        """Test Toyota ECU communicator patterns"""
        mock_transport = Mock(spec=UDSTransportInterface)
        communicator = ToyotaECUCommunicator(mock_transport, "Toyota")
        
        assert communicator.brand == "Toyota"
        assert ECUType.ECM in communicator.supported_ecus
        assert ECUType.PCM in communicator.supported_ecus
        
        # Test Toyota-specific PIDs
        assert "HYBRID_BATTERY_SOC" in communicator.toyota_pids
        assert communicator.toyota_pids["HYBRID_BATTERY_SOC"] == 0x2101
    
    @pytest.mark.asyncio
    async def test_ecu_manager(self):
        """Test ECU manager functionality"""
        mock_transport = Mock(spec=UDSTransportInterface)
        manager = ECUManager(mock_transport)
        
        # Register communicators
        renault_comm = RenaultECUCommunicator(mock_transport, "Renault")
        toyota_comm = ToyotaECUCommunicator(mock_transport, "Toyota")
        
        manager.register_communicator("Renault", renault_comm)
        manager.register_communicator("Toyota", toyota_comm)
        
        assert "Renault" in manager.communicators
        assert "Toyota" in manager.communicators


class TestDataProcessingPatterns:
    """Test data processing patterns inspired by Toyota sample data"""
    
    def test_obd_data_point_creation(self):
        """Test OBD data point structure"""
        point = OBDDataPoint(
            timestamp=datetime.now(),
            pid="0x0C",
            name="Engine RPM",
            value=2500,
            unit="rpm",
            raw_value=b'\x09\xC4'
        )
        
        assert point.pid == "0x0C"
        assert point.name == "Engine RPM"
        assert point.value == 2500
        assert point.unit == "rpm"
        assert point.quality == 1.0
    
    def test_data_stream_configuration(self):
        """Test data stream configuration"""
        stream = DataStream(
            pid="0x0C",
            name="Engine RPM",
            unit="rpm",
            min_value=0,
            max_value=8000,
            update_rate=10.0,
            buffer_size=100
        )
        
        assert stream.pid == "0x0C"
        assert stream.name == "Engine RPM"
        assert stream.min_value == 0
        assert stream.max_value == 8000
        assert stream.update_rate == 10.0
    
    @pytest.mark.asyncio
    async def test_data_buffer_operations(self):
        """Test data buffer operations"""
        buffer = DataBuffer(max_size=10)
        
        # Add data points
        for i in range(5):
            point = OBDDataPoint(
                timestamp=datetime.now(),
                pid="0x0C",
                name="Engine RPM",
                value=2000 + i * 100,
                unit="rpm"
            )
            await buffer.add_point(point)
        
        # Get latest points
        latest = await buffer.get_latest(3)
        assert len(latest) == 3
        assert latest[-1].value == 2400  # Last added value
        
        # Get statistics
        stats = await buffer.get_statistics()
        assert stats['count'] == 5
        assert stats['min'] == 2000
        assert stats['max'] == 2400
        assert stats['mean'] == 2200
    
    def test_data_validator(self):
        """Test data validation patterns"""
        validator = DataValidator()
        
        # Register validation rule
        validator.register_validation_rule("0x0C", {
            'min_value': 0,
            'max_value': 8000,
            'max_rate_change': 1000
        })
        
        # Test valid data point
        valid_point = OBDDataPoint(
            timestamp=datetime.now(),
            pid="0x0C",
            name="Engine RPM",
            value=2500,
            unit="rpm"
        )
        
        quality = validator.validate_data_point(valid_point)
        assert quality == 1.0
        
        # Test invalid data point (out of range)
        invalid_point = OBDDataPoint(
            timestamp=datetime.now(),
            pid="0x0C",
            name="Engine RPM",
            value=10000,  # Above max
            unit="rpm"
        )
        
        quality = validator.validate_data_point(invalid_point)
        assert quality < 1.0
    
    @pytest.mark.asyncio
    async def test_data_processor(self):
        """Test data processor functionality"""
        processor = DataProcessor(buffer_size=100)
        
        # Register data stream
        stream = DataStream(
            pid="0x0C",
            name="Engine RPM",
            unit="rpm",
            min_value=0,
            max_value=8000,
            update_rate=10.0
        )
        processor.register_stream(stream)
        
        # Process data point
        raw_data = b'\x09\xC4'  # 2500 RPM
        point = await processor.process_data_point("0x0C", raw_data)
        
        assert point is not None
        assert point.pid == "0x0C"
        assert point.value == 2500  # (0x09 * 256 + 0xC4) / 4
        
        # Get latest data
        latest_data = await processor.get_latest_data(["0x0C"])
        assert "0x0C" in latest_data
        assert latest_data["0x0C"].value == 2500


class TestIntegratedPatterns:
    """Test integrated patterns from multiple reference projects"""
    
    @pytest.mark.asyncio
    async def test_complete_diagnostic_flow(self):
        """Test complete diagnostic flow using integrated patterns"""
        # Create mock transport
        mock_transport = Mock(spec=UDSTransportInterface)
        mock_transport.send_and_receive = AsyncMock()
        
        # Create ECU manager
        manager = ECUManager(mock_transport)
        
        # Register brand communicators
        renault_comm = RenaultECUCommunicator(mock_transport, "Renault")
        manager.register_communicator("Renault", renault_comm)
        
        # Create data processor
        processor = DataProcessor()
        
        # Register data streams
        rpm_stream = DataStream(
            pid="0x0C",
            name="Engine RPM",
            unit="rpm",
            min_value=0,
            max_value=8000
        )
        processor.register_stream(rpm_stream)
        
        # Mock ECU identification
        mock_transport.send_and_receive.return_value = Mock(data=b'\x50\x03')
        
        # Test ECU scanning
        ecus = await manager.scan_all_ecus()
        
        # Process some data
        await processor.process_data_point("0x0C", b'\x09\xC4')
        
        # Verify integration
        assert len(manager.communicators) == 1
        assert "0x0C" in processor.streams
        
        # Get performance metrics
        metrics = processor.get_performance_metrics()
        assert 'points_processed' in metrics
        assert metrics['points_processed'] >= 1
    
    def test_reference_patterns_documentation(self):
        """Test that reference patterns are properly documented"""
        # Verify UDS patterns
        assert hasattr(UDSTransportInterface, '__doc__')
        assert "uds-main" in UDSTransportInterface.__doc__.lower()
        
        # Verify brand patterns
        assert hasattr(RenaultECUCommunicator, '__doc__')
        assert "ddt4all" in RenaultECUCommunicator.__doc__.lower()
        
        # Verify data patterns
        assert hasattr(DataProcessor, '__doc__')
        assert "toyota" in DataProcessor.__doc__.lower()


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
