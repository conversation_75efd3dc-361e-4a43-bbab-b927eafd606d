"""
UDS Transport Interface
Professional UDS protocol implementation inspired by uds-main project

Provides abstract transport layer for UDS communication over different buses:
- CAN Bus
- K-Line
- Ethernet (DoIP)
- LIN Bus

Follows SOLID principles with clear separation of concerns.
"""
import asyncio
import logging
from abc import ABC, abstractmethod
from typing import Optional, List, Dict, Any, Union, Callable
from dataclasses import dataclass
from enum import Enum
import time

logger = logging.getLogger(__name__)


class TransportProtocol(Enum):
    """Supported transport protocols"""
    CAN = "CAN"
    KLINE = "K-Line"
    DOIP = "DoIP"
    LIN = "LIN"


class AddressingType(Enum):
    """UDS addressing types"""
    PHYSICAL = "physical"
    FUNCTIONAL = "functional"


@dataclass
class UDSMessage:
    """UDS message structure"""
    data: bytes
    source_address: Optional[int] = None
    target_address: Optional[int] = None
    addressing_type: AddressingType = AddressingType.PHYSICAL
    timestamp: Optional[float] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()


@dataclass
class TransportConfig:
    """Transport configuration"""
    protocol: TransportProtocol
    interface: str
    baudrate: Optional[int] = None
    timeout: float = 5.0
    max_retries: int = 3
    addressing_type: AddressingType = AddressingType.PHYSICAL
    source_address: Optional[int] = None
    target_address: Optional[int] = None
    
    # CAN-specific
    can_id_request: Optional[int] = None
    can_id_response: Optional[int] = None
    
    # K-Line specific
    init_sequence: Optional[str] = None
    
    # DoIP specific
    logical_address: Optional[int] = None
    equipment_address: Optional[int] = None


class UDSTransportInterface(ABC):
    """
    Abstract base class for UDS transport interfaces
    Inspired by uds-main transport interface pattern
    """
    
    def __init__(self, config: TransportConfig):
        self.config = config
        self.is_connected = False
        self._message_handlers: List[Callable[[UDSMessage], None]] = []
        
    @abstractmethod
    async def connect(self) -> bool:
        """Establish connection to the transport interface"""
        pass
    
    @abstractmethod
    async def disconnect(self) -> None:
        """Close connection to the transport interface"""
        pass
    
    @abstractmethod
    async def send_message(self, message: UDSMessage) -> bool:
        """Send UDS message"""
        pass
    
    @abstractmethod
    async def receive_message(self, timeout: Optional[float] = None) -> Optional[UDSMessage]:
        """Receive UDS message"""
        pass
    
    @abstractmethod
    async def send_and_receive(self, request: UDSMessage, timeout: Optional[float] = None) -> Optional[UDSMessage]:
        """Send request and wait for response"""
        pass
    
    def add_message_handler(self, handler: Callable[[UDSMessage], None]) -> None:
        """Add message handler for incoming messages"""
        self._message_handlers.append(handler)
    
    def remove_message_handler(self, handler: Callable[[UDSMessage], None]) -> None:
        """Remove message handler"""
        if handler in self._message_handlers:
            self._message_handlers.remove(handler)
    
    def _notify_handlers(self, message: UDSMessage) -> None:
        """Notify all registered handlers of incoming message"""
        for handler in self._message_handlers:
            try:
                handler(message)
            except Exception as e:
                logger.error(f"Error in message handler: {e}")


class CANTransport(UDSTransportInterface):
    """
    CAN Bus transport implementation for UDS
    Supports ISO-TP (ISO 14229-2) protocol
    """
    
    def __init__(self, config: TransportConfig):
        super().__init__(config)
        self._can_interface = None
        self._receive_task: Optional[asyncio.Task] = None
        
        # ISO-TP specific
        self._sequence_number = 0
        self._pending_frames: Dict[int, List[bytes]] = {}
        
    async def connect(self) -> bool:
        """Connect to CAN interface"""
        try:
            # Import CAN library (python-can)
            import can
            
            # Create CAN bus interface
            self._can_interface = can.interface.Bus(
                interface=self.config.interface,
                channel='can0',  # Default channel
                bitrate=self.config.baudrate or 500000
            )
            
            self.is_connected = True
            
            # Start receive task
            self._receive_task = asyncio.create_task(self._receive_loop())
            
            logger.info(f"Connected to CAN interface: {self.config.interface}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to CAN interface: {e}")
            return False
    
    async def disconnect(self) -> None:
        """Disconnect from CAN interface"""
        self.is_connected = False
        
        if self._receive_task:
            self._receive_task.cancel()
            try:
                await self._receive_task
            except asyncio.CancelledError:
                pass
        
        if self._can_interface:
            self._can_interface.shutdown()
            self._can_interface = None
        
        logger.info("Disconnected from CAN interface")
    
    async def send_message(self, message: UDSMessage) -> bool:
        """Send UDS message over CAN using ISO-TP"""
        if not self.is_connected or not self._can_interface:
            return False
        
        try:
            # Fragment message if needed (ISO-TP)
            frames = self._fragment_message(message)
            
            for frame in frames:
                can_msg = self._create_can_message(frame, message.target_address)
                self._can_interface.send(can_msg)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to send CAN message: {e}")
            return False
    
    async def receive_message(self, timeout: Optional[float] = None) -> Optional[UDSMessage]:
        """Receive UDS message from CAN"""
        # This is handled by the receive loop
        # In a real implementation, you'd use a queue here
        await asyncio.sleep(0.1)  # Placeholder
        return None
    
    async def send_and_receive(self, request: UDSMessage, timeout: Optional[float] = None) -> Optional[UDSMessage]:
        """Send request and wait for response"""
        if not await self.send_message(request):
            return None
        
        # Wait for response with timeout
        start_time = time.time()
        timeout = timeout or self.config.timeout
        
        while time.time() - start_time < timeout:
            response = await self.receive_message(0.1)
            if response:
                return response
            await asyncio.sleep(0.01)
        
        logger.warning("Timeout waiting for UDS response")
        return None
    
    def _fragment_message(self, message: UDSMessage) -> List[bytes]:
        """Fragment UDS message for ISO-TP transmission"""
        data = message.data
        
        if len(data) <= 7:  # Single frame
            return [bytes([len(data)]) + data + b'\x00' * (7 - len(data))]
        
        # Multi-frame (simplified implementation)
        frames = []
        
        # First frame
        first_frame = bytes([0x10 | ((len(data) >> 8) & 0x0F), len(data) & 0xFF])
        first_frame += data[:6]
        frames.append(first_frame)
        
        # Consecutive frames
        remaining_data = data[6:]
        sequence = 1
        
        while remaining_data:
            frame_data = remaining_data[:7]
            frame = bytes([0x20 | (sequence & 0x0F)]) + frame_data
            frame += b'\x00' * (8 - len(frame))
            frames.append(frame)
            
            remaining_data = remaining_data[7:]
            sequence = (sequence + 1) % 16
        
        return frames
    
    def _create_can_message(self, data: bytes, target_address: Optional[int]):
        """Create CAN message from data"""
        import can
        
        can_id = self.config.can_id_request or (0x700 + (target_address or 0))
        return can.Message(arbitration_id=can_id, data=data, is_extended_id=False)
    
    async def _receive_loop(self):
        """Background task to receive CAN messages"""
        while self.is_connected:
            try:
                if self._can_interface:
                    msg = self._can_interface.recv(timeout=0.1)
                    if msg:
                        uds_msg = self._process_can_message(msg)
                        if uds_msg:
                            self._notify_handlers(uds_msg)
                
                await asyncio.sleep(0.001)  # Small delay to prevent busy waiting
                
            except Exception as e:
                logger.error(f"Error in CAN receive loop: {e}")
                await asyncio.sleep(0.1)
    
    def _process_can_message(self, can_msg) -> Optional[UDSMessage]:
        """Process received CAN message and reconstruct UDS message"""
        # Simplified ISO-TP reassembly
        data = can_msg.data
        
        if not data:
            return None
        
        pci = data[0] & 0xF0
        
        if pci == 0x00:  # Single frame
            length = data[0] & 0x0F
            if length > 0 and length <= 7:
                uds_data = data[1:1+length]
                return UDSMessage(
                    data=uds_data,
                    source_address=can_msg.arbitration_id,
                    timestamp=can_msg.timestamp
                )
        
        # Multi-frame handling would go here
        return None


class KLineTransport(UDSTransportInterface):
    """
    K-Line transport implementation for UDS
    Supports KWP2000 protocol
    """
    
    def __init__(self, config: TransportConfig):
        super().__init__(config)
        self._serial_connection = None
    
    async def connect(self) -> bool:
        """Connect to K-Line interface"""
        try:
            import serial
            
            self._serial_connection = serial.Serial(
                port=self.config.interface,
                baudrate=self.config.baudrate or 10400,
                timeout=self.config.timeout
            )
            
            # Perform K-Line initialization
            await self._perform_init_sequence()
            
            self.is_connected = True
            logger.info(f"Connected to K-Line interface: {self.config.interface}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to connect to K-Line interface: {e}")
            return False
    
    async def disconnect(self) -> None:
        """Disconnect from K-Line interface"""
        self.is_connected = False
        
        if self._serial_connection:
            self._serial_connection.close()
            self._serial_connection = None
        
        logger.info("Disconnected from K-Line interface")
    
    async def send_message(self, message: UDSMessage) -> bool:
        """Send UDS message over K-Line"""
        if not self.is_connected or not self._serial_connection:
            return False
        
        try:
            # Add KWP2000 header and checksum
            frame = self._build_kwp_frame(message)
            self._serial_connection.write(frame)
            return True
            
        except Exception as e:
            logger.error(f"Failed to send K-Line message: {e}")
            return False
    
    async def receive_message(self, timeout: Optional[float] = None) -> Optional[UDSMessage]:
        """Receive UDS message from K-Line"""
        if not self.is_connected or not self._serial_connection:
            return None
        
        try:
            # Read KWP2000 frame
            frame = self._read_kwp_frame(timeout)
            if frame:
                return self._parse_kwp_frame(frame)
            
        except Exception as e:
            logger.error(f"Failed to receive K-Line message: {e}")
        
        return None
    
    async def send_and_receive(self, request: UDSMessage, timeout: Optional[float] = None) -> Optional[UDSMessage]:
        """Send request and wait for response"""
        if not await self.send_message(request):
            return None
        
        return await self.receive_message(timeout)
    
    async def _perform_init_sequence(self):
        """Perform K-Line initialization sequence"""
        # Simplified init sequence
        if self._serial_connection:
            # Send init pattern
            self._serial_connection.write(b'\x33')
            await asyncio.sleep(0.025)
            
            # Wait for response
            response = self._serial_connection.read(2)
            if len(response) == 2:
                logger.info("K-Line initialization successful")
            else:
                logger.warning("K-Line initialization may have failed")
    
    def _build_kwp_frame(self, message: UDSMessage) -> bytes:
        """Build KWP2000 frame from UDS message"""
        # Simplified KWP2000 frame format
        header = bytes([0x80, len(message.data) + 1, message.target_address or 0x10])
        frame = header + message.data
        
        # Add checksum
        checksum = sum(frame) & 0xFF
        frame += bytes([checksum])
        
        return frame
    
    def _read_kwp_frame(self, timeout: Optional[float]) -> Optional[bytes]:
        """Read KWP2000 frame from serial connection"""
        if not self._serial_connection:
            return None
        
        try:
            # Read header
            header = self._serial_connection.read(3)
            if len(header) != 3:
                return None
            
            # Get data length
            data_length = header[1] - 1
            
            # Read data and checksum
            data = self._serial_connection.read(data_length + 1)
            if len(data) != data_length + 1:
                return None
            
            return header + data
            
        except Exception as e:
            logger.error(f"Error reading KWP frame: {e}")
            return None
    
    def _parse_kwp_frame(self, frame: bytes) -> Optional[UDSMessage]:
        """Parse KWP2000 frame to UDS message"""
        if len(frame) < 4:
            return None
        
        # Extract data (skip header and checksum)
        data = frame[3:-1]
        source_address = frame[2]
        
        return UDSMessage(
            data=data,
            source_address=source_address,
            timestamp=time.time()
        )


class TransportFactory:
    """Factory for creating transport interfaces"""
    
    @staticmethod
    def create_transport(config: TransportConfig) -> UDSTransportInterface:
        """Create transport interface based on configuration"""
        if config.protocol == TransportProtocol.CAN:
            return CANTransport(config)
        elif config.protocol == TransportProtocol.KLINE:
            return KLineTransport(config)
        else:
            raise ValueError(f"Unsupported transport protocol: {config.protocol}")
