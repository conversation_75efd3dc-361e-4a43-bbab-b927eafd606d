"""
OBD Interface Module
Handles OBD2 and CAN communication with professional patterns

Integrates best practices from:
- UDS-main: Professional UDS protocol implementation
- DDT4All: ECU communication patterns
- AndrOBD: Android OBD patterns
- EcuBus-Pro: Modular architecture
"""

from .obd_reader import <PERSON><PERSON><PERSON><PERSON><PERSON>, DTCResult, OBDParameter
from .can_reader import CANReader, ECUInfo
from .dtc_parser import DTCParser

# Enhanced protocol support
from .protocol_detector import ProtocolManager, StandardProtocolDetector
from .command_database import CommandDatabase, OBDDecoder
from .response_decoder import ResponseProcessor, StandardResponseValidator

# Professional UDS patterns (inspired by uds-main)
from .uds_transport import UDSTransportInterface, CANTransport, TransportConfig, TransportProtocol
from .uds_session import UDSSession, DiagnosticSession, SecurityLevel

# Brand-specific communication patterns (inspired by ddt4all/pyren)
from .ecu_communicator import ECUCommunicator, ECUManager
from .brand_protocols import BrandProtocolFactory, ProtocolVariant

# Data processing patterns (inspired by Toyota sample data)
from .data_processor import DataProcessor, DataStream, OBDDataPoint

__all__ = [
    # Core OBD components
    'OBDReader',
    'DTCResult',
    'OBDParameter',
    'CANReader',
    'ECUInfo',
    'DTCParser',

    # Enhanced protocol support
    'ProtocolManager',
    'StandardProtocolDetector',
    'CommandDatabase',
    'OBDDecoder',
    'ResponseProcessor',
    'StandardResponseValidator',

    # Professional UDS patterns
    'UDSTransportInterface',
    'CANTransport',
    'TransportConfig',
    'TransportProtocol',
    'UDSSession',
    'DiagnosticSession',
    'SecurityLevel',

    # Brand-specific patterns
    'ECUCommunicator',
    'ECUManager',
    'BrandProtocolFactory',
    'ProtocolVariant',

    # Data processing patterns
    'DataProcessor',
    'DataStream',
    'OBDDataPoint'
]
