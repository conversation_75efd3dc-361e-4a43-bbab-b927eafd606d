"""
OBD Data Processor
Real-time data processing patterns inspired by Toyota sample data and AndrOBD

Handles:
- Real-time parameter streaming
- Data validation and filtering
- Performance optimization
- Data format standardization
- Statistical analysis

Follows Single Responsibility Principle with modular data processing.
"""
import asyncio
import logging
from typing import Dict, List, Optional, Any, Callable, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from collections import deque
import statistics
import time
import csv
import json

logger = logging.getLogger(__name__)


@dataclass
class OBDDataPoint:
    """Single OBD data point"""
    timestamp: datetime
    pid: str
    name: str
    value: Union[float, int, str]
    unit: str
    raw_value: Optional[bytes] = None
    quality: float = 1.0  # Data quality score (0-1)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization"""
        return {
            'timestamp': self.timestamp.isoformat(),
            'pid': self.pid,
            'name': self.name,
            'value': self.value,
            'unit': self.unit,
            'raw_value': self.raw_value.hex() if self.raw_value else None,
            'quality': self.quality
        }


@dataclass
class DataStream:
    """Data stream configuration"""
    pid: str
    name: str
    unit: str
    min_value: Optional[float] = None
    max_value: Optional[float] = None
    update_rate: float = 1.0  # Hz
    buffer_size: int = 100
    validation_func: Optional[Callable[[Any], bool]] = None
    conversion_func: Optional[Callable[[bytes], Any]] = None


class DataBuffer:
    """
    Circular buffer for OBD data points
    Inspired by Toyota sample data structure
    """
    
    def __init__(self, max_size: int = 1000):
        self.max_size = max_size
        self.data: deque = deque(maxlen=max_size)
        self._lock = asyncio.Lock()
    
    async def add_point(self, point: OBDDataPoint):
        """Add data point to buffer"""
        async with self._lock:
            self.data.append(point)
    
    async def get_latest(self, count: int = 1) -> List[OBDDataPoint]:
        """Get latest data points"""
        async with self._lock:
            if count == 1:
                return [self.data[-1]] if self.data else []
            else:
                return list(self.data)[-count:] if len(self.data) >= count else list(self.data)
    
    async def get_range(self, start_time: datetime, end_time: datetime) -> List[OBDDataPoint]:
        """Get data points in time range"""
        async with self._lock:
            return [
                point for point in self.data
                if start_time <= point.timestamp <= end_time
            ]
    
    async def get_statistics(self, duration: Optional[timedelta] = None) -> Dict[str, float]:
        """Get statistical analysis of data"""
        async with self._lock:
            if not self.data:
                return {}
            
            # Filter by duration if specified
            if duration:
                cutoff_time = datetime.now() - duration
                filtered_data = [p for p in self.data if p.timestamp >= cutoff_time]
            else:
                filtered_data = list(self.data)
            
            if not filtered_data:
                return {}
            
            # Extract numeric values
            numeric_values = []
            for point in filtered_data:
                if isinstance(point.value, (int, float)):
                    numeric_values.append(float(point.value))
            
            if not numeric_values:
                return {}
            
            return {
                'count': len(numeric_values),
                'min': min(numeric_values),
                'max': max(numeric_values),
                'mean': statistics.mean(numeric_values),
                'median': statistics.median(numeric_values),
                'stdev': statistics.stdev(numeric_values) if len(numeric_values) > 1 else 0.0
            }


class DataValidator:
    """
    Data validation and quality assessment
    Inspired by AndrOBD validation patterns
    """
    
    def __init__(self):
        self.validation_rules: Dict[str, Dict[str, Any]] = {}
        self.quality_thresholds = {
            'range_check': 0.9,
            'rate_check': 0.8,
            'consistency_check': 0.7
        }
    
    def register_validation_rule(self, pid: str, rule: Dict[str, Any]):
        """Register validation rule for PID"""
        self.validation_rules[pid] = rule
    
    def validate_data_point(self, point: OBDDataPoint) -> float:
        """Validate data point and return quality score"""
        if point.pid not in self.validation_rules:
            return 1.0  # No rules, assume valid
        
        rule = self.validation_rules[point.pid]
        quality_score = 1.0
        
        # Range validation
        if 'min_value' in rule and 'max_value' in rule:
            if isinstance(point.value, (int, float)):
                if not (rule['min_value'] <= point.value <= rule['max_value']):
                    quality_score *= self.quality_thresholds['range_check']
        
        # Rate of change validation
        if 'max_rate_change' in rule and hasattr(self, '_last_values'):
            last_value = self._last_values.get(point.pid)
            if last_value and isinstance(point.value, (int, float)):
                rate_change = abs(point.value - last_value.value)
                if rate_change > rule['max_rate_change']:
                    quality_score *= self.quality_thresholds['rate_check']
        
        # Store for next validation
        if not hasattr(self, '_last_values'):
            self._last_values = {}
        self._last_values[point.pid] = point
        
        return quality_score
    
    def validate_consistency(self, points: List[OBDDataPoint]) -> Dict[str, float]:
        """Validate consistency across multiple data points"""
        consistency_scores = {}
        
        # Group by PID
        pid_groups = {}
        for point in points:
            if point.pid not in pid_groups:
                pid_groups[point.pid] = []
            pid_groups[point.pid].append(point)
        
        # Check consistency within each PID group
        for pid, pid_points in pid_groups.items():
            if len(pid_points) < 2:
                consistency_scores[pid] = 1.0
                continue
            
            # Check for outliers
            numeric_values = [
                float(p.value) for p in pid_points
                if isinstance(p.value, (int, float))
            ]
            
            if len(numeric_values) < 2:
                consistency_scores[pid] = 1.0
                continue
            
            mean_val = statistics.mean(numeric_values)
            stdev_val = statistics.stdev(numeric_values) if len(numeric_values) > 1 else 0
            
            # Count outliers (values > 2 standard deviations from mean)
            outliers = sum(1 for val in numeric_values if abs(val - mean_val) > 2 * stdev_val)
            consistency_score = 1.0 - (outliers / len(numeric_values))
            
            consistency_scores[pid] = max(consistency_score, 0.0)
        
        return consistency_scores


class DataProcessor:
    """
    Main data processor for OBD streams
    Handles real-time processing, validation, and analysis
    """
    
    def __init__(self, buffer_size: int = 1000):
        self.buffers: Dict[str, DataBuffer] = {}
        self.streams: Dict[str, DataStream] = {}
        self.validator = DataValidator()
        self.is_processing = False
        self._processing_task: Optional[asyncio.Task] = None
        
        # Performance metrics
        self.metrics = {
            'points_processed': 0,
            'validation_failures': 0,
            'processing_rate': 0.0,
            'last_update': datetime.now()
        }
        
        # Data export
        self.export_handlers: List[Callable[[List[OBDDataPoint]], None]] = []
    
    def register_stream(self, stream: DataStream):
        """Register new data stream"""
        self.streams[stream.pid] = stream
        self.buffers[stream.pid] = DataBuffer(stream.buffer_size)
        
        # Register validation rule if provided
        if stream.min_value is not None and stream.max_value is not None:
            self.validator.register_validation_rule(stream.pid, {
                'min_value': stream.min_value,
                'max_value': stream.max_value
            })
        
        logger.info(f"Registered data stream: {stream.name} ({stream.pid})")
    
    async def process_data_point(self, pid: str, raw_data: bytes, timestamp: Optional[datetime] = None) -> Optional[OBDDataPoint]:
        """Process single data point"""
        if pid not in self.streams:
            logger.warning(f"Unknown PID: {pid}")
            return None
        
        stream = self.streams[pid]
        timestamp = timestamp or datetime.now()
        
        try:
            # Convert raw data to value
            if stream.conversion_func:
                value = stream.conversion_func(raw_data)
            else:
                # Default conversion (assume integer)
                value = int.from_bytes(raw_data, 'big') if raw_data else 0
            
            # Create data point
            point = OBDDataPoint(
                timestamp=timestamp,
                pid=pid,
                name=stream.name,
                value=value,
                unit=stream.unit,
                raw_value=raw_data
            )
            
            # Validate data point
            point.quality = self.validator.validate_data_point(point)
            
            # Additional validation if provided
            if stream.validation_func and not stream.validation_func(value):
                point.quality *= 0.5  # Reduce quality for failed validation
            
            # Add to buffer
            await self.buffers[pid].add_point(point)
            
            # Update metrics
            self.metrics['points_processed'] += 1
            if point.quality < 0.8:
                self.metrics['validation_failures'] += 1
            
            # Notify export handlers
            for handler in self.export_handlers:
                try:
                    handler([point])
                except Exception as e:
                    logger.error(f"Export handler error: {e}")
            
            return point
            
        except Exception as e:
            logger.error(f"Error processing data point for {pid}: {e}")
            return None
    
    async def get_latest_data(self, pids: Optional[List[str]] = None) -> Dict[str, OBDDataPoint]:
        """Get latest data for specified PIDs"""
        result = {}
        target_pids = pids or list(self.streams.keys())
        
        for pid in target_pids:
            if pid in self.buffers:
                latest = await self.buffers[pid].get_latest(1)
                if latest:
                    result[pid] = latest[0]
        
        return result
    
    async def get_historical_data(self, pid: str, duration: timedelta) -> List[OBDDataPoint]:
        """Get historical data for PID within duration"""
        if pid not in self.buffers:
            return []
        
        end_time = datetime.now()
        start_time = end_time - duration
        
        return await self.buffers[pid].get_range(start_time, end_time)
    
    async def get_statistics(self, pid: str, duration: Optional[timedelta] = None) -> Dict[str, float]:
        """Get statistics for PID"""
        if pid not in self.buffers:
            return {}
        
        return await self.buffers[pid].get_statistics(duration)
    
    def add_export_handler(self, handler: Callable[[List[OBDDataPoint]], None]):
        """Add data export handler"""
        self.export_handlers.append(handler)
    
    def remove_export_handler(self, handler: Callable[[List[OBDDataPoint]], None]):
        """Remove data export handler"""
        if handler in self.export_handlers:
            self.export_handlers.remove(handler)
    
    async def export_to_csv(self, filename: str, pid: str, duration: Optional[timedelta] = None):
        """Export data to CSV file"""
        try:
            data = await self.get_historical_data(pid, duration or timedelta(hours=1))
            
            with open(filename, 'w', newline='') as csvfile:
                fieldnames = ['timestamp', 'pid', 'name', 'value', 'unit', 'quality']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for point in data:
                    writer.writerow({
                        'timestamp': point.timestamp.isoformat(),
                        'pid': point.pid,
                        'name': point.name,
                        'value': point.value,
                        'unit': point.unit,
                        'quality': point.quality
                    })
            
            logger.info(f"Exported {len(data)} data points to {filename}")
            
        except Exception as e:
            logger.error(f"Failed to export to CSV: {e}")
    
    async def export_to_json(self, filename: str, pids: Optional[List[str]] = None, duration: Optional[timedelta] = None):
        """Export data to JSON file"""
        try:
            export_data = {}
            target_pids = pids or list(self.streams.keys())
            
            for pid in target_pids:
                data = await self.get_historical_data(pid, duration or timedelta(hours=1))
                export_data[pid] = [point.to_dict() for point in data]
            
            with open(filename, 'w') as jsonfile:
                json.dump(export_data, jsonfile, indent=2)
            
            total_points = sum(len(data) for data in export_data.values())
            logger.info(f"Exported {total_points} data points to {filename}")
            
        except Exception as e:
            logger.error(f"Failed to export to JSON: {e}")
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get processing performance metrics"""
        now = datetime.now()
        time_diff = (now - self.metrics['last_update']).total_seconds()
        
        if time_diff > 0:
            self.metrics['processing_rate'] = self.metrics['points_processed'] / time_diff
        
        self.metrics['last_update'] = now
        
        return self.metrics.copy()
    
    async def start_processing(self):
        """Start background processing"""
        if self.is_processing:
            return
        
        self.is_processing = True
        self._processing_task = asyncio.create_task(self._processing_loop())
        logger.info("Started data processing")
    
    async def stop_processing(self):
        """Stop background processing"""
        self.is_processing = False
        
        if self._processing_task:
            self._processing_task.cancel()
            try:
                await self._processing_task
            except asyncio.CancelledError:
                pass
        
        logger.info("Stopped data processing")
    
    async def _processing_loop(self):
        """Background processing loop"""
        while self.is_processing:
            try:
                # Perform periodic maintenance tasks
                await self._cleanup_old_data()
                await self._update_metrics()
                
                # Sleep for a short interval
                await asyncio.sleep(1.0)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Processing loop error: {e}")
                await asyncio.sleep(1.0)
    
    async def _cleanup_old_data(self):
        """Clean up old data points"""
        # This is handled automatically by the deque maxlen
        # But we could implement more sophisticated cleanup here
        pass
    
    async def _update_metrics(self):
        """Update performance metrics"""
        # Reset counters periodically
        if datetime.now() - self.metrics['last_update'] > timedelta(minutes=5):
            self.metrics['points_processed'] = 0
            self.metrics['validation_failures'] = 0
