"""
UDS Session Management
Professional UDS session handling inspired by DDT4All patterns

Manages diagnostic sessions, security access, and ECU communication
following ISO 14229 (UDS) standard.
"""
import asyncio
import logging
from enum import Enum
from typing import Optional, Dict, Any, List, Callable
from dataclasses import dataclass
import time

from .uds_transport import UDSTransportInterface, UDSMessage, AddressingType

logger = logging.getLogger(__name__)


class DiagnosticSession(Enum):
    """UDS Diagnostic Session Types (ISO 14229-1)"""
    DEFAULT = 0x01
    PROGRAMMING = 0x02
    EXTENDED = 0x03
    SAFETY_SYSTEM = 0x04
    # Manufacturer specific sessions (0x40-0x5F)
    PLANT = 0x40
    AFTERSALES = 0x50
    ENGINEERING = 0x60
    SUPPLIER = 0x70


class SecurityLevel(Enum):
    """UDS Security Access Levels"""
    LOCKED = 0x00
    LEVEL_1 = 0x01  # Basic access
    LEVEL_2 = 0x02  # Extended access
    LEVEL_3 = 0x03  # Programming access
    # Manufacturer specific levels
    PLANT_ACCESS = 0x10
    ENGINEERING_ACCESS = 0x20


class UDSService(Enum):
    """UDS Service Identifiers (ISO 14229-1)"""
    DIAGNOSTIC_SESSION_CONTROL = 0x10
    ECU_RESET = 0x11
    SECURITY_ACCESS = 0x27
    COMMUNICATION_CONTROL = 0x28
    TESTER_PRESENT = 0x3E
    READ_DATA_BY_IDENTIFIER = 0x22
    READ_MEMORY_BY_ADDRESS = 0x23
    READ_SCALING_DATA_BY_IDENTIFIER = 0x24
    READ_DTC_INFORMATION = 0x19
    WRITE_DATA_BY_IDENTIFIER = 0x2E
    CLEAR_DTC_INFORMATION = 0x14
    ROUTINE_CONTROL = 0x31


@dataclass
class UDSResponse:
    """UDS Response structure"""
    service_id: int
    data: bytes
    is_positive: bool
    nrc: Optional[int] = None  # Negative Response Code
    timestamp: Optional[float] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()


class UDSSession:
    """
    UDS Session Manager
    Handles diagnostic sessions, security access, and service communication
    Inspired by DDT4All ECU communication patterns
    """
    
    def __init__(self, transport: UDSTransportInterface, ecu_address: int = 0x10):
        self.transport = transport
        self.ecu_address = ecu_address
        self.current_session = DiagnosticSession.DEFAULT
        self.security_level = SecurityLevel.LOCKED
        self.is_active = False
        
        # Session management
        self._tester_present_task: Optional[asyncio.Task] = None
        self._session_timeout = 5.0  # Default P3 timeout
        self._last_activity = time.time()
        
        # Security access
        self._security_seed: Optional[bytes] = None
        self._security_algorithms: Dict[SecurityLevel, Callable[[bytes], bytes]] = {}
        
        # Service handlers
        self._service_handlers: Dict[int, Callable[[UDSMessage], UDSResponse]] = {}
        
    async def start_session(self, session_type: DiagnosticSession = DiagnosticSession.DEFAULT) -> bool:
        """Start UDS diagnostic session"""
        try:
            # Send Diagnostic Session Control request
            request_data = bytes([UDSService.DIAGNOSTIC_SESSION_CONTROL.value, session_type.value])
            request = UDSMessage(
                data=request_data,
                target_address=self.ecu_address,
                addressing_type=AddressingType.PHYSICAL
            )
            
            response_msg = await self.transport.send_and_receive(request)
            if not response_msg:
                logger.error("No response to session control request")
                return False
            
            response = self._parse_response(response_msg)
            if response.is_positive and response.service_id == 0x50:  # Positive response
                self.current_session = session_type
                self.is_active = True
                self._last_activity = time.time()
                
                # Start tester present if needed
                if session_type != DiagnosticSession.DEFAULT:
                    await self._start_tester_present()
                
                logger.info(f"Started UDS session: {session_type.name}")
                return True
            else:
                logger.error(f"Session control failed: NRC {response.nrc}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to start UDS session: {e}")
            return False
    
    async def end_session(self) -> bool:
        """End UDS diagnostic session"""
        try:
            # Stop tester present
            await self._stop_tester_present()
            
            # Return to default session
            if self.current_session != DiagnosticSession.DEFAULT:
                await self.start_session(DiagnosticSession.DEFAULT)
            
            self.is_active = False
            self.security_level = SecurityLevel.LOCKED
            logger.info("Ended UDS session")
            return True
            
        except Exception as e:
            logger.error(f"Failed to end UDS session: {e}")
            return False
    
    async def request_security_access(self, level: SecurityLevel) -> bool:
        """Request security access to ECU"""
        try:
            # Request seed
            seed_request = bytes([UDSService.SECURITY_ACCESS.value, level.value * 2 - 1])
            request = UDSMessage(
                data=seed_request,
                target_address=self.ecu_address
            )
            
            response_msg = await self.transport.send_and_receive(request)
            if not response_msg:
                return False
            
            response = self._parse_response(response_msg)
            if not response.is_positive:
                logger.error(f"Security access seed request failed: NRC {response.nrc}")
                return False
            
            # Extract seed
            if len(response.data) < 2:
                logger.error("Invalid seed response")
                return False
            
            seed = response.data[1:]  # Skip service ID
            self._security_seed = seed
            
            # Calculate key
            if level not in self._security_algorithms:
                logger.error(f"No security algorithm for level {level}")
                return False
            
            key = self._security_algorithms[level](seed)
            
            # Send key
            key_request = bytes([UDSService.SECURITY_ACCESS.value, level.value * 2]) + key
            request = UDSMessage(
                data=key_request,
                target_address=self.ecu_address
            )
            
            response_msg = await self.transport.send_and_receive(request)
            if not response_msg:
                return False
            
            response = self._parse_response(response_msg)
            if response.is_positive:
                self.security_level = level
                logger.info(f"Security access granted: {level.name}")
                return True
            else:
                logger.error(f"Security access key failed: NRC {response.nrc}")
                return False
                
        except Exception as e:
            logger.error(f"Security access failed: {e}")
            return False
    
    async def read_data_by_identifier(self, did: int) -> Optional[bytes]:
        """Read data by identifier (DID)"""
        try:
            request_data = bytes([UDSService.READ_DATA_BY_IDENTIFIER.value, 
                                (did >> 8) & 0xFF, did & 0xFF])
            request = UDSMessage(
                data=request_data,
                target_address=self.ecu_address
            )
            
            response_msg = await self.transport.send_and_receive(request)
            if not response_msg:
                return None
            
            response = self._parse_response(response_msg)
            if response.is_positive and len(response.data) > 3:
                # Return data (skip service ID and DID)
                return response.data[3:]
            
            return None
            
        except Exception as e:
            logger.error(f"Read DID {did:04X} failed: {e}")
            return None
    
    async def read_dtc_information(self, sub_function: int = 0x02) -> Optional[List[Dict[str, Any]]]:
        """Read DTC information"""
        try:
            request_data = bytes([UDSService.READ_DTC_INFORMATION.value, sub_function])
            request = UDSMessage(
                data=request_data,
                target_address=self.ecu_address
            )
            
            response_msg = await self.transport.send_and_receive(request)
            if not response_msg:
                return None
            
            response = self._parse_response(response_msg)
            if response.is_positive:
                return self._parse_dtc_response(response.data)
            
            return None
            
        except Exception as e:
            logger.error(f"Read DTC information failed: {e}")
            return None
    
    async def clear_dtc_information(self, group: int = 0xFFFFFF) -> bool:
        """Clear DTC information"""
        try:
            request_data = bytes([UDSService.CLEAR_DTC_INFORMATION.value,
                                (group >> 16) & 0xFF,
                                (group >> 8) & 0xFF,
                                group & 0xFF])
            request = UDSMessage(
                data=request_data,
                target_address=self.ecu_address
            )
            
            response_msg = await self.transport.send_and_receive(request)
            if not response_msg:
                return False
            
            response = self._parse_response(response_msg)
            return response.is_positive
            
        except Exception as e:
            logger.error(f"Clear DTC failed: {e}")
            return False
    
    async def send_tester_present(self) -> bool:
        """Send tester present to keep session alive"""
        try:
            request_data = bytes([UDSService.TESTER_PRESENT.value, 0x00])
            request = UDSMessage(
                data=request_data,
                target_address=self.ecu_address
            )
            
            response_msg = await self.transport.send_and_receive(request, timeout=1.0)
            if response_msg:
                response = self._parse_response(response_msg)
                if response.is_positive:
                    self._last_activity = time.time()
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Tester present failed: {e}")
            return False
    
    def register_security_algorithm(self, level: SecurityLevel, algorithm: Callable[[bytes], bytes]):
        """Register security access algorithm for a specific level"""
        self._security_algorithms[level] = algorithm
    
    def _parse_response(self, message: UDSMessage) -> UDSResponse:
        """Parse UDS response message"""
        data = message.data
        
        if len(data) == 0:
            return UDSResponse(0, b'', False)
        
        if data[0] == 0x7F:  # Negative response
            service_id = data[1] if len(data) > 1 else 0
            nrc = data[2] if len(data) > 2 else 0
            return UDSResponse(service_id, data, False, nrc)
        else:  # Positive response
            service_id = data[0] - 0x40  # Remove positive response bit
            return UDSResponse(service_id, data, True)
    
    def _parse_dtc_response(self, data: bytes) -> List[Dict[str, Any]]:
        """Parse DTC information response"""
        dtcs = []
        
        if len(data) < 3:
            return dtcs
        
        # Skip service ID and sub-function
        dtc_data = data[2:]
        
        # Parse DTCs (3 bytes per DTC + 1 byte status)
        i = 0
        while i + 3 < len(dtc_data):
            dtc_bytes = dtc_data[i:i+3]
            status = dtc_data[i+3] if i+3 < len(dtc_data) else 0
            
            # Convert to DTC code
            dtc_code = f"{dtc_bytes[0]:02X}{dtc_bytes[1]:02X}{dtc_bytes[2]:02X}"
            
            dtcs.append({
                'code': dtc_code,
                'status': status,
                'raw_bytes': dtc_bytes
            })
            
            i += 4
        
        return dtcs
    
    async def _start_tester_present(self):
        """Start tester present background task"""
        if self._tester_present_task:
            return
        
        self._tester_present_task = asyncio.create_task(self._tester_present_loop())
    
    async def _stop_tester_present(self):
        """Stop tester present background task"""
        if self._tester_present_task:
            self._tester_present_task.cancel()
            try:
                await self._tester_present_task
            except asyncio.CancelledError:
                pass
            self._tester_present_task = None
    
    async def _tester_present_loop(self):
        """Background tester present loop"""
        while self.is_active:
            try:
                # Send tester present every 2 seconds
                await asyncio.sleep(2.0)
                
                if time.time() - self._last_activity > self._session_timeout:
                    await self.send_tester_present()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Tester present loop error: {e}")
                await asyncio.sleep(1.0)
