"""
ECU Communicator
Brand-specific ECU communication patterns inspired by DDT4All and PyRen

Provides specialized communication for different vehicle brands:
- Renault: DDT4All patterns with DF codes and UCH communication
- Toyota: Techstream-like communication patterns
- VAG: UDS with brand-specific adaptations
- BMW: EDIABAS-style communication

Follows Single Responsibility Principle with modular design.
"""
import asyncio
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Union, Tuple
from dataclasses import dataclass
from enum import Enum
import time

from .uds_session import UDSSession, DiagnosticSession, SecurityLevel
from .uds_transport import UDSTransportInterface, UDSMessage

logger = logging.getLogger(__name__)


class ECUType(Enum):
    """ECU Types across different brands"""
    ENGINE = "engine"
    TRANSMISSION = "transmission"
    ABS_ESP = "abs_esp"
    AIRBAG = "airbag"
    BODY_CONTROL = "body_control"
    INSTRUMENT_CLUSTER = "instrument_cluster"
    RADIO_NAVIGATION = "radio_navigation"
    AIR_CONDITIONING = "air_conditioning"
    PARKING_ASSISTANCE = "parking_assistance"
    
    # Renault specific
    UCH = "uch"  # Unified Control Housing
    UCE = "uce"  # Engine Control Unit
    
    # BMW specific
    DME = "dme"  # Digital Motor Electronics
    DDE = "dde"  # Digital Diesel Electronics
    CAS = "cas"  # Car Access System
    
    # Toyota specific
    ECM = "ecm"  # Engine Control Module
    PCM = "pcm"  # Powertrain Control Module


@dataclass
class ECUInfo:
    """ECU Information structure"""
    ecu_type: ECUType
    address: int
    name: str
    brand: str
    supported_services: List[int]
    security_levels: List[SecurityLevel]
    diagnostic_sessions: List[DiagnosticSession]
    part_number: Optional[str] = None
    software_version: Optional[str] = None
    hardware_version: Optional[str] = None


@dataclass
class BrandSpecificRequest:
    """Brand-specific request structure"""
    service_id: int
    data: bytes
    ecu_address: int
    requires_security: bool = False
    security_level: Optional[SecurityLevel] = None
    session_type: Optional[DiagnosticSession] = None
    description: str = ""


class ECUCommunicator(ABC):
    """
    Abstract base class for brand-specific ECU communication
    Inspired by DDT4All ECU communication patterns
    """
    
    def __init__(self, transport: UDSTransportInterface, brand: str):
        self.transport = transport
        self.brand = brand
        self.sessions: Dict[int, UDSSession] = {}  # ECU address -> session
        self.ecu_info: Dict[int, ECUInfo] = {}
        
        # Brand-specific configurations
        self.default_session_type = DiagnosticSession.DEFAULT
        self.supported_ecus: Dict[ECUType, int] = {}
        
        self._initialize_brand_config()
    
    @abstractmethod
    def _initialize_brand_config(self):
        """Initialize brand-specific configuration"""
        pass
    
    @abstractmethod
    async def identify_ecus(self) -> List[ECUInfo]:
        """Identify available ECUs"""
        pass
    
    @abstractmethod
    async def read_brand_specific_dtcs(self, ecu_address: int) -> List[Dict[str, Any]]:
        """Read brand-specific DTCs"""
        pass
    
    @abstractmethod
    async def perform_brand_specific_test(self, ecu_address: int, test_name: str, **kwargs) -> Dict[str, Any]:
        """Perform brand-specific diagnostic test"""
        pass
    
    async def connect_to_ecu(self, ecu_address: int, session_type: Optional[DiagnosticSession] = None) -> bool:
        """Connect to specific ECU"""
        try:
            if ecu_address in self.sessions:
                return True  # Already connected
            
            session = UDSSession(self.transport, ecu_address)
            session_type = session_type or self.default_session_type
            
            if await session.start_session(session_type):
                self.sessions[ecu_address] = session
                logger.info(f"Connected to ECU at address 0x{ecu_address:02X}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to connect to ECU 0x{ecu_address:02X}: {e}")
            return False
    
    async def disconnect_from_ecu(self, ecu_address: int) -> bool:
        """Disconnect from specific ECU"""
        try:
            if ecu_address in self.sessions:
                session = self.sessions[ecu_address]
                await session.end_session()
                del self.sessions[ecu_address]
                logger.info(f"Disconnected from ECU at address 0x{ecu_address:02X}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to disconnect from ECU 0x{ecu_address:02X}: {e}")
            return False
    
    async def send_brand_request(self, request: BrandSpecificRequest) -> Optional[bytes]:
        """Send brand-specific request"""
        try:
            # Ensure connection to ECU
            if not await self.connect_to_ecu(request.ecu_address, request.session_type):
                return None
            
            session = self.sessions[request.ecu_address]
            
            # Handle security access if required
            if request.requires_security and request.security_level:
                if session.security_level != request.security_level:
                    if not await session.request_security_access(request.security_level):
                        logger.error(f"Security access failed for {request.description}")
                        return None
            
            # Send request
            uds_message = UDSMessage(
                data=bytes([request.service_id]) + request.data,
                target_address=request.ecu_address
            )
            
            response_msg = await self.transport.send_and_receive(uds_message)
            if response_msg:
                return response_msg.data
            
            return None
            
        except Exception as e:
            logger.error(f"Brand request failed: {e}")
            return None


class RenaultECUCommunicator(ECUCommunicator):
    """
    Renault ECU Communicator
    Implements DDT4All-style communication patterns
    """
    
    def _initialize_brand_config(self):
        """Initialize Renault-specific configuration"""
        self.supported_ecus = {
            ECUType.UCE: 0x10,      # Engine ECU
            ECUType.UCH: 0x30,      # Body Control
            ECUType.ABS_ESP: 0x20,  # ABS/ESP
            ECUType.AIRBAG: 0x15,   # Airbag
            ECUType.INSTRUMENT_CLUSTER: 0x50,
            ECUType.RADIO_NAVIGATION: 0x36,
            ECUType.AIR_CONDITIONING: 0x40
        }
        
        self.default_session_type = DiagnosticSession.EXTENDED
        
        # Renault-specific DF code patterns
        self.df_code_patterns = {
            "DF001": "Injection system fault",
            "DF002": "Turbocharger pressure fault",
            "DF080": "EGR valve position fault",
            "DF100": "UCH communication fault"
        }
    
    async def identify_ecus(self) -> List[ECUInfo]:
        """Identify available Renault ECUs"""
        ecus = []
        
        for ecu_type, address in self.supported_ecus.items():
            try:
                if await self.connect_to_ecu(address):
                    session = self.sessions[address]
                    
                    # Read ECU identification
                    part_number = await session.read_data_by_identifier(0xF187)  # Part number
                    software_version = await session.read_data_by_identifier(0xF189)  # Software version
                    
                    ecu_info = ECUInfo(
                        ecu_type=ecu_type,
                        address=address,
                        name=f"Renault {ecu_type.value.upper()}",
                        brand="Renault",
                        supported_services=[0x10, 0x22, 0x19, 0x14, 0x27],
                        security_levels=[SecurityLevel.LEVEL_1, SecurityLevel.LEVEL_2],
                        diagnostic_sessions=[DiagnosticSession.DEFAULT, DiagnosticSession.EXTENDED],
                        part_number=part_number.hex() if part_number else None,
                        software_version=software_version.hex() if software_version else None
                    )
                    
                    ecus.append(ecu_info)
                    self.ecu_info[address] = ecu_info
                    
            except Exception as e:
                logger.debug(f"ECU at 0x{address:02X} not available: {e}")
        
        return ecus
    
    async def read_brand_specific_dtcs(self, ecu_address: int) -> List[Dict[str, Any]]:
        """Read Renault-specific DTCs including DF codes"""
        dtcs = []
        
        try:
            session = self.sessions.get(ecu_address)
            if not session:
                return dtcs
            
            # Read standard DTCs
            standard_dtcs = await session.read_dtc_information(0x02)  # Stored DTCs
            if standard_dtcs:
                dtcs.extend(standard_dtcs)
            
            # Read Renault DF codes (manufacturer specific)
            df_dtcs = await session.read_dtc_information(0x03)  # Snapshot DTCs
            if df_dtcs:
                for dtc in df_dtcs:
                    # Convert to DF format if applicable
                    code = dtc.get('code', '')
                    if code.startswith('DF'):
                        dtc['description'] = self.df_code_patterns.get(code, f"Renault specific fault: {code}")
                        dtc['brand_specific'] = True
                
                dtcs.extend(df_dtcs)
            
            return dtcs
            
        except Exception as e:
            logger.error(f"Failed to read Renault DTCs: {e}")
            return dtcs
    
    async def perform_brand_specific_test(self, ecu_address: int, test_name: str, **kwargs) -> Dict[str, Any]:
        """Perform Renault-specific diagnostic tests"""
        result = {"test_name": test_name, "success": False, "data": {}}
        
        try:
            session = self.sessions.get(ecu_address)
            if not session:
                return result
            
            if test_name == "injector_coding":
                # Renault injector coding procedure
                result = await self._perform_injector_coding(session, **kwargs)
            
            elif test_name == "uch_reset":
                # UCH reset procedure
                result = await self._perform_uch_reset(session, **kwargs)
            
            elif test_name == "egr_adaptation":
                # EGR valve adaptation
                result = await self._perform_egr_adaptation(session, **kwargs)
            
            else:
                result["error"] = f"Unknown test: {test_name}"
            
            return result
            
        except Exception as e:
            logger.error(f"Renault test {test_name} failed: {e}")
            result["error"] = str(e)
            return result
    
    async def _perform_injector_coding(self, session: UDSSession, **kwargs) -> Dict[str, Any]:
        """Perform Renault injector coding"""
        result = {"test_name": "injector_coding", "success": False, "data": {}}
        
        try:
            # Read current injector codes
            injector_data = await session.read_data_by_identifier(0x2100)  # Example DID
            if injector_data:
                result["data"]["current_codes"] = injector_data.hex()
            
            # Write new injector codes if provided
            new_codes = kwargs.get('injector_codes')
            if new_codes:
                # This would require security access
                if await session.request_security_access(SecurityLevel.LEVEL_2):
                    # Write new codes (simplified)
                    result["success"] = True
                    result["data"]["new_codes"] = new_codes
            
            return result
            
        except Exception as e:
            result["error"] = str(e)
            return result
    
    async def _perform_uch_reset(self, session: UDSSession, **kwargs) -> Dict[str, Any]:
        """Perform UCH reset procedure"""
        result = {"test_name": "uch_reset", "success": False, "data": {}}
        
        try:
            # UCH reset requires specific sequence
            if await session.request_security_access(SecurityLevel.LEVEL_1):
                # Send reset command (example)
                reset_data = bytes([0x31, 0x01, 0xFF, 0x00])  # Routine control
                request = UDSMessage(data=reset_data, target_address=session.ecu_address)
                
                response = await session.transport.send_and_receive(request)
                if response:
                    result["success"] = True
                    result["data"]["response"] = response.data.hex()
            
            return result
            
        except Exception as e:
            result["error"] = str(e)
            return result
    
    async def _perform_egr_adaptation(self, session: UDSSession, **kwargs) -> Dict[str, Any]:
        """Perform EGR valve adaptation"""
        result = {"test_name": "egr_adaptation", "success": False, "data": {}}
        
        try:
            # Read current EGR position
            egr_position = await session.read_data_by_identifier(0x2110)  # Example DID
            if egr_position:
                result["data"]["current_position"] = egr_position.hex()
            
            # Perform adaptation routine
            adaptation_data = bytes([0x31, 0x01, 0x20, 0x10])  # Example routine
            request = UDSMessage(data=adaptation_data, target_address=session.ecu_address)
            
            response = await session.transport.send_and_receive(request)
            if response:
                result["success"] = True
                result["data"]["adaptation_result"] = response.data.hex()
            
            return result
            
        except Exception as e:
            result["error"] = str(e)
            return result


class ECUManager:
    """
    ECU Manager
    Manages multiple ECU communicators for different brands
    """
    
    def __init__(self, transport: UDSTransportInterface):
        self.transport = transport
        self.communicators: Dict[str, ECUCommunicator] = {}
        self.detected_ecus: List[ECUInfo] = []
    
    def register_communicator(self, brand: str, communicator: ECUCommunicator):
        """Register brand-specific communicator"""
        self.communicators[brand] = communicator
    
    async def scan_all_ecus(self) -> List[ECUInfo]:
        """Scan for ECUs using all registered communicators"""
        all_ecus = []
        
        for brand, communicator in self.communicators.items():
            try:
                ecus = await communicator.identify_ecus()
                all_ecus.extend(ecus)
                logger.info(f"Found {len(ecus)} {brand} ECUs")
                
            except Exception as e:
                logger.error(f"Failed to scan {brand} ECUs: {e}")
        
        self.detected_ecus = all_ecus
        return all_ecus
    
    async def get_communicator_for_ecu(self, ecu_address: int) -> Optional[ECUCommunicator]:
        """Get appropriate communicator for ECU address"""
        for ecu in self.detected_ecus:
            if ecu.address == ecu_address:
                return self.communicators.get(ecu.brand)
        
        return None
    
    async def read_all_dtcs(self) -> Dict[str, List[Dict[str, Any]]]:
        """Read DTCs from all detected ECUs"""
        all_dtcs = {}
        
        for ecu in self.detected_ecus:
            communicator = self.communicators.get(ecu.brand)
            if communicator:
                try:
                    dtcs = await communicator.read_brand_specific_dtcs(ecu.address)
                    all_dtcs[f"{ecu.brand}_0x{ecu.address:02X}"] = dtcs
                    
                except Exception as e:
                    logger.error(f"Failed to read DTCs from {ecu.brand} ECU 0x{ecu.address:02X}: {e}")
        
        return all_dtcs
