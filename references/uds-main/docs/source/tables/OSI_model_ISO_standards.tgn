{"rows_views": [[{"style": {"borders": "", "font_style": {}, "text_color": "", "bg_color": "", "halign": "center", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "", "font_style": {}, "text_color": "", "bg_color": "", "halign": "center", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "", "font_style": {}, "text_color": "", "bg_color": "", "halign": "center", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "", "font_style": {}, "text_color": "", "bg_color": "", "halign": "center", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "lrtb", "font_style": {}, "text_color": "", "bg_color": "", "halign": "center", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "lrtb", "font_style": {}, "text_color": "", "bg_color": "", "halign": "center", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "lrtb", "font_style": {}, "text_color": "", "bg_color": "", "halign": "center", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}], [{"style": {"borders": "", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "lrtb", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "lrtb", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "lrtb", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}], [{"style": {"borders": "", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "lrtb", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "lrtb", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "lrtb", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}], [{"style": {"borders": "", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "lrtb", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "lrtb", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "lrtb", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}], [{"style": {"borders": "", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "lrtb", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "lrtb", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "lrtb", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}], [{"style": {"borders": "", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "lrtb", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "lrtb", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "lrtb", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}], [{"style": {"borders": "", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "lrtb", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "lrtb", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "lrtb", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}], [{"style": {"borders": "", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "lrtb", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "lrtb", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}, {"style": {"borders": "lrtb", "font_style": {}, "text_color": "", "bg_color": "", "halign": "left", "valign": "top", "padding": {"top": 10, "right": 5, "bottom": 10, "left": 5}, "border_color": ""}}]], "model": {"rows": [[{"value": "OSI Layer", "cspan": 1, "rspan": 1, "markup": [1, 9]}, {"value": "Common", "cspan": 1, "rspan": 1, "markup": [1, 6]}, {"value": "CAN", "cspan": 1, "rspan": 1, "markup": [1, 3]}, {"value": "FlexRay", "cspan": 1, "rspan": 1, "markup": [1, 7]}, {"value": "Ethernet", "cspan": 1, "rspan": 1, "markup": [1, 8]}, {"value": "K-Line", "cspan": 1, "rspan": 1, "markup": [1, 6]}, {"value": "LIN", "cspan": 1, "rspan": 1, "markup": [1, 3]}], [{"value": "Layer 7\nApplication", "cspan": 1, "rspan": 1, "markup": [1, 19]}, {"value": "ISO 14229-1\n\nISO 27145-3", "cspan": 1, "rspan": 1, "markup": [1, 24]}, {"value": "ISO 14229-3", "cspan": 1, "rspan": 1, "markup": [1, 11]}, {"value": "ISO 14229-4", "cspan": 1, "rspan": 1, "markup": [1, 11]}, {"value": "ISO 14229-5", "cspan": 1, "rspan": 1, "markup": [1, 11]}, {"value": "ISO 14229-6", "cspan": 1, "rspan": 1, "markup": [1, 11]}, {"value": "ISO 14229-7", "cspan": 1, "rspan": 1, "markup": [1, 11]}], [{"value": "Layer 6\nPresentation", "cspan": 1, "rspan": 1, "markup": [1, 20]}, {"value": "ISO 27145-2", "cspan": 1, "rspan": 1, "markup": [1, 11]}, {"value": "", "cspan": 1, "rspan": 1, "markup": []}, {"value": "", "cspan": 1, "rspan": 1, "markup": []}, {"value": "", "cspan": 1, "rspan": 1, "markup": []}, {"value": "", "cspan": 1, "rspan": 1, "markup": []}, {"value": "", "cspan": 1, "rspan": 1, "markup": []}], [{"value": "Layer 5\nSession", "cspan": 1, "rspan": 1, "markup": [1, 15]}, {"value": "ISO 14229-2", "cspan": 1, "rspan": 1, "markup": [1, 11]}, {"value": "", "cspan": 1, "rspan": 1, "markup": []}, {"value": "", "cspan": 1, "rspan": 1, "markup": []}, {"value": "", "cspan": 1, "rspan": 1, "markup": []}, {"value": "", "cspan": 1, "rspan": 1, "markup": []}, {"value": "", "cspan": 1, "rspan": 1, "markup": []}], [{"value": "Layer 4\nTransport", "cspan": 1, "rspan": 1, "markup": [1, 17]}, {"value": "ISO 27145-4", "cspan": 1, "rspan": 4, "markup": [1, 11]}, {"value": "ISO 15765-2", "cspan": 1, "rspan": 2, "markup": [1, 11]}, {"value": "ISO 10681-2", "cspan": 1, "rspan": 2, "markup": [1, 11]}, {"value": "ISO 13400-2", "cspan": 1, "rspan": 2, "markup": [1, 11]}, {"value": "Not applicable", "cspan": 1, "rspan": 2, "markup": [1, 14]}, {"value": "ISO 17987-2", "cspan": 1, "rspan": 2, "markup": [1, 11]}], [{"value": "Layer 3\nNetwork", "cspan": 1, "rspan": 1, "markup": [1, 15]}, {"value": "", "cspan": 1, "rspan": -1, "markup": []}, {"value": "", "cspan": 1, "rspan": -1, "markup": []}, {"value": "", "cspan": 1, "rspan": -1, "markup": []}, {"value": "", "cspan": 1, "rspan": -1, "markup": []}, {"value": "", "cspan": 1, "rspan": -1, "markup": []}, {"value": "", "cspan": 1, "rspan": -1, "markup": []}], [{"value": "Layer 2\nData", "cspan": 1, "rspan": 1, "markup": [1, 12]}, {"value": "", "cspan": 1, "rspan": -2, "markup": []}, {"value": "ISO 11898-1", "cspan": 1, "rspan": 1, "markup": [1, 11]}, {"value": "ISO 17458-2", "cspan": 1, "rspan": 1, "markup": [1, 11]}, {"value": "ISO 13400-3", "cspan": 1, "rspan": 2, "markup": [1, 11]}, {"value": "ISO 14230-2", "cspan": 1, "rspan": 1, "markup": [1, 11]}, {"value": "ISO 17987-3", "cspan": 1, "rspan": 1, "markup": [1, 11]}], [{"value": "Layer 1\nPhysical", "cspan": 1, "rspan": 1, "markup": [1, 16]}, {"value": "", "cspan": 1, "rspan": -3, "markup": []}, {"value": "ISO 11898-2\n\nISO 11898-3", "cspan": 1, "rspan": 1, "markup": [1, 24]}, {"value": "ISO 17458-4", "cspan": 1, "rspan": 1, "markup": [1, 11]}, {"value": "", "cspan": 1, "rspan": -1, "markup": []}, {"value": "ISO 14230-1", "cspan": 1, "rspan": 1, "markup": [1, 11]}, {"value": "ISO 17987-4", "cspan": 1, "rspan": 1, "markup": [1, 11]}]]}, "theme": null, "fixed_layout": false, "markup": {"instances": [{}, {"style": {"fontWeight": "", "fontStyle": "", "textDecoration": "", "color": "", "backgroundColor": ""}}, null]}, "options": {}}