{"data": {"devices": {"7ce31dc7-5c89-4205-ae1b-599c4604f973": {"type": "can", "canDevice": {"id": "7ce31dc7-5c89-4205-ae1b-599c4604f973", "name": "PEAK_1", "handle": 81, "vendor": "peak", "canfd": false, "database": "", "bitrate": {"sjw": 1, "timeSeg1": 13, "timeSeg2": 2, "preScaler": 10, "freq": 500000, "clock": "80", "_X_ROW_KEY": "row_51"}}}}, "ia": {"7125c939-dcda-47cc-8fda-c6709a6ff01f": {"name": "Can IA", "type": "can", "id": "7125c939-dcda-47cc-8fda-c6709a6ff01f", "devices": ["7ce31dc7-5c89-4205-ae1b-599c4604f973"], "action": [{"trigger": {"type": "periodic", "period": 1}, "name": "", "id": "1", "channel": "", "type": "can", "dlc": 8, "data": [], "_X_ROW_KEY": "row_39"}, {"trigger": {"type": "periodic", "period": 1}, "name": "", "id": "11", "channel": "", "type": "canfd", "dlc": 15, "data": [null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, null, "11"], "_X_ROW_KEY": "row_40", "brs": false}, {"trigger": {"type": "manual", "period": 2}, "name": "", "id": "2", "channel": "7ce31dc7-5c89-4205-ae1b-599c4604f973", "type": "can", "dlc": 8, "data": [], "_X_ROW_KEY": "row_46"}]}}, "tester": {"f304259b-dc7d-460b-9729-4eee2d1156de": {"id": "f304259b-dc7d-460b-9729-4eee2d1156de", "name": "Tester", "type": "can", "script": "tester.ts", "targetDeviceId": "8e79b126-cf28-4ee7-97a8-c1ab51ebb27f", "seqList": [{"name": "Seq0", "services": [{"enable": true, "checkResp": false, "retryNum": 0, "addressIndex": 0, "failBehavior": "stop", "serviceId": "9df86c3a-6a6e-40f5-bea7-195286cbd3b7", "delay": 0, "_X_ROW_KEY": "row_82"}, {"enable": true, "checkResp": false, "retryNum": 0, "addressIndex": 0, "failBehavior": "stop", "serviceId": "3521f481-98a1-4012-9060-4f10c4880196", "delay": 0, "_X_ROW_KEY": "row_53"}]}], "address": [{"type": "can", "canAddr": {"name": "Addr0", "addrFormat": "NORMAL", "addrType": "PHYSICAL", "SA": "0x1", "TA": "0x2", "AE": "", "canIdTx": "0x55", "canIdRx": "0x56", "nAs": 1000, "nAr": 1000, "nBs": 1000, "nCr": 1000, "nBr": 0, "nCs": 0, "idType": "STANDARD", "brs": false, "canfd": false, "remote": false, "stMin": 10, "bs": 10, "maxWTF": 0, "dlc": 8, "padding": false, "paddingValue": "0x00"}}], "udsTime": {"pTime": 2000, "pExtTime": 5000, "s3Time": 5000, "testerPresentEnable": false}, "allServiceList": {"0x10": [], "Job": [{"id": "9df86c3a-6a6e-40f5-bea7-195286cbd3b7", "name": "JobFunction0", "serviceId": "Job", "params": [], "respParams": [], "suppress": false, "autoSubfunc": true}, {"id": "3521f481-98a1-4012-9060-4f10c4880196", "name": "JobFunction1", "serviceId": "Job", "params": [], "respParams": [], "suppress": false, "autoSubfunc": true}], "0x34": [{"id": "6ebe102f-df6c-4149-9d2d-ee731a819764", "name": "RequestDownload520", "serviceId": "0x34", "params": [{"id": "2502ed20-5785-4771-8829-cc9261116f74", "name": "dataFormatIdentifier", "bitLen": 8, "deletable": true, "editable": true, "type": "NUM", "value": {"type": "<PERSON><PERSON><PERSON>", "data": [0]}, "phyValue": "00"}, {"id": "3ecff8da-b452-440d-b386-20491611456a", "name": "addressAndLengthFormatIdentifier", "bitLen": 8, "deletable": true, "editable": true, "type": "NUM", "value": {"type": "<PERSON><PERSON><PERSON>", "data": [0]}, "phyValue": "00"}, {"id": "29d799fc-8dbe-49e4-b64e-f282a9e3c774", "name": "memoryAddress", "bitLen": 32, "deletable": true, "editable": true, "type": "NUM", "value": {"type": "<PERSON><PERSON><PERSON>", "data": [0, 0, 0, 0]}, "phyValue": "00"}, {"id": "27be151c-a2a0-4dd1-9c29-2e03bf1c8ef6", "name": "memorySize", "bitLen": 32, "deletable": true, "editable": true, "type": "NUM", "value": {"type": "<PERSON><PERSON><PERSON>", "data": [0, 0, 0, 0]}, "phyValue": "00"}], "respParams": [{"id": "9b51cc0d-0b1e-4c58-bd55-2539843f114d", "name": "lengthFormatIdentifier", "bitLen": 8, "deletable": true, "editable": true, "type": "NUM", "value": {"type": "<PERSON><PERSON><PERSON>", "data": [0]}, "phyValue": "00"}, {"id": "ec250091-e065-4980-a53e-1d7ed07e2a60", "name": "maxNumberOfBlockLength", "bitLen": 32, "deletable": true, "editable": true, "type": "ARRAY", "value": {"type": "<PERSON><PERSON><PERSON>", "data": [0]}, "phyValue": "00"}], "suppress": false, "autoSubfunc": true}], "0x36": [{"id": "cc8c0b79-7325-476a-a1c0-5d0d7feeebbc", "name": "TransferData540", "serviceId": "0x36", "params": [{"id": "267ed773-b8aa-4cc0-a506-82baa9f5ba6d", "name": "blockSequenceCounter", "bitLen": 8, "deletable": true, "editable": true, "type": "NUM", "value": {"type": "<PERSON><PERSON><PERSON>", "data": [0]}, "phyValue": "00"}, {"id": "356f97fb-5643-4200-9c02-5618b375a70d", "name": "transferRequestParameterRecord", "bitLen": 8, "deletable": true, "editable": true, "type": "ARRAY", "value": {"type": "<PERSON><PERSON><PERSON>", "data": [0]}, "phyValue": "00"}], "respParams": [{"id": "ac6c5899-42bb-4c70-9c6c-663f13f03814", "name": "blockSequenceCounter", "bitLen": 8, "deletable": true, "editable": true, "type": "NUM", "value": {"type": "<PERSON><PERSON><PERSON>", "data": [0]}, "phyValue": "00"}, {"id": "3d05a721-ed1c-4ce5-aa86-77e2cb04af12", "name": "transferResponseParameterRecord", "bitLen": 8, "deletable": true, "editable": true, "type": "ARRAY", "value": {"type": "<PERSON><PERSON><PERSON>", "data": [0]}, "phyValue": "00"}], "suppress": false, "autoSubfunc": true}], "0x37": [{"id": "47b9ceb6-281c-49ab-ba71-dca44b9e252a", "name": "RequestTransferExit550", "serviceId": "0x37", "params": [{"id": "81640011-bacc-47b6-8068-4310ce4421bd", "name": "transferRequestParameterRecord", "bitLen": 8, "deletable": true, "editable": true, "type": "ARRAY", "value": {"type": "<PERSON><PERSON><PERSON>", "data": [0]}, "phyValue": "00"}], "respParams": [{"id": "69026a45-5bcd-4ee4-9efb-8c948ea6bf7b", "name": "transferResponseParameterRecord", "bitLen": 8, "deletable": true, "editable": true, "type": "ARRAY", "value": {"type": "<PERSON><PERSON><PERSON>", "data": [0]}, "phyValue": "00"}], "suppress": false, "autoSubfunc": true}]}, "simulateBy": "5c043bd6-ddca-449a-b535-1ae865085123"}}, "subFunction": {}, "nodes": {"5c043bd6-ddca-449a-b535-1ae865085123": {"name": "Node 1", "type": "can", "id": "5c043bd6-ddca-449a-b535-1ae865085123", "channel": ["7ce31dc7-5c89-4205-ae1b-599c4604f973"], "workNode": "", "attachTester": "f304259b-dc7d-460b-9729-4eee2d1156de", "script": "ecu.ts"}}, "database": {"lin": {}, "can": {}}, "graphs": {"Statistics.7ce31dc7-5c89-4205-ae1b-599c4604f973.BusLoad": {"type": "variable", "enable": true, "id": "Statistics.7ce31dc7-5c89-4205-ae1b-599c4604f973.BusLoad", "name": "BusLoad", "color": "#86a0a1", "yAxis": {"min": 0, "max": 100, "unit": "%"}, "bindValue": {"variableId": "Statistics.7ce31dc7-5c89-4205-ae1b-599c4604f973.BusLoad", "variableType": "system", "variableName": "BusLoad"}, "graph": {"id": "graph"}}, "Statistics.7ce31dc7-5c89-4205-ae1b-599c4604f973.FrameFreq": {"type": "variable", "enable": true, "id": "Statistics.7ce31dc7-5c89-4205-ae1b-599c4604f973.FrameFreq", "name": "FrameFreq", "color": "rgba(78, 98, 246, 1)", "yAxis": {"min": 0, "max": 10000, "unit": "f/s", "splitLine": {"show": false}}, "bindValue": {"variableId": "Statistics.7ce31dc7-5c89-4205-ae1b-599c4604f973.FrameFreq", "variableType": "system", "variableName": "FrameFreq"}, "graph": {"id": "graph"}, "xAxis": {"splitLine": {"show": false, "lineStyle": {"type": "dashed"}}, "axisPointer": {"show": false}}, "disZoom": false, "series": {"showSymbol": false, "symbolSize": 6, "symbol": "circle"}}}, "tests": {}, "guages": {}, "vars": {}, "datas": {}}, "project": {"wins": {"message": {"pos": {"x": 383, "y": 59, "w": 2562, "h": 332}, "options": {"params": {}}, "title": "message", "label": "Message", "id": "message", "layoutType": "bottom", "hide": true}, "tester": {"pos": {"x": 327, "y": 26, "w": 906, "h": 400}, "title": "tester", "label": "<PERSON><PERSON> Tester", "id": "tester", "options": {}, "hide": false}, "graph": {"pos": {"x": 226, "y": 8, "w": 822, "h": 525}, "title": "graph", "label": "Line", "id": "graph", "options": {"params": {"edit-index": "graph"}}, "hide": false}, "network": {"pos": {"x": 952, "y": 65, "w": 600, "h": 400}, "title": "network", "label": "Network", "id": "network", "options": {}, "hide": false}, "7125c939-dcda-47cc-8fda-c6709a6ff01f_ia": {"pos": {"x": 952, "y": 65, "w": 824, "h": 400}, "title": "cani", "label": "IA", "id": "7125c939-dcda-47cc-8fda-c6709a6ff01f_ia", "options": {"params": {"edit-index": "7125c939-dcda-47cc-8fda-c6709a6ff01f"}, "name": "Can IA"}, "hide": false}, "trace": {"pos": {"x": 281.5, "y": 65, "w": 752, "h": 368}, "title": "trace", "label": "Trace", "id": "trace", "options": {}}, "variable": {"pos": {"x": 632.5, "y": 0, "w": 600, "h": 400}, "title": "variable", "label": "Variable", "id": "variable", "options": {}}, "hardware": {"pos": {"x": 392.5, "y": 65, "w": 1292, "h": 611}, "title": "hardware", "label": "Devices", "id": "hardware", "options": {}}, "datas": {"pos": {"x": 346.5, "y": 215, "w": 1116, "h": 400}, "title": "datas", "label": "Datas", "id": "datas", "options": {"params": {"edit-index": "datas"}}}}, "example": {"catalog": "UDS"}}}