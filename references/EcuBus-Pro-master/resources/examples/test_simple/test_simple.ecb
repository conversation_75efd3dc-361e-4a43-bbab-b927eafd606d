{"data": {"devices": {"b283da7b-1165-4e62-a609-b0eac40cb8a8": {"type": "can", "canDevice": {"id": "b283da7b-1165-4e62-a609-b0eac40cb8a8", "name": "SIMULATE_0", "handle": 0, "vendor": "simulate", "canfd": false, "database": "", "bitrate": {"sjw": 1, "timeSeg1": 13, "timeSeg2": 2, "preScaler": 10, "freq": 500000, "clock": "80", "_X_ROW_KEY": "row_16"}}}}, "ia": {"1e1f0b3f-a46b-45e9-848f-ed899671cd5e": {"name": "Can IA", "type": "can", "id": "1e1f0b3f-a46b-45e9-848f-ed899671cd5e", "devices": ["b283da7b-1165-4e62-a609-b0eac40cb8a8"], "action": [{"trigger": {"type": "manual", "onKey": "a"}, "name": "", "id": "1", "channel": "b283da7b-1165-4e62-a609-b0eac40cb8a8", "type": "can", "dlc": 8, "data": [], "_X_ROW_KEY": "row_24"}, {"trigger": {"type": "manual", "onKey": "b"}, "name": "", "id": "2", "channel": "b283da7b-1165-4e62-a609-b0eac40cb8a8", "type": "can", "dlc": 8, "data": [], "_X_ROW_KEY": "row_41"}]}}, "tester": {"45b91fa7-b667-4b71-a476-633769a6c78a": {"id": "45b91fa7-b667-4b71-a476-633769a6c78a", "name": "Tester_can_0", "type": "can", "script": "", "targetDeviceId": "", "seqList": [], "address": [{"type": "can", "canAddr": {"name": "Addr0", "addrFormat": "NORMAL", "addrType": "PHYSICAL", "SA": "0x1", "TA": "0x2", "AE": "", "canIdTx": "0x55", "canIdRx": "0x56", "nAs": 1000, "nAr": 1000, "nBs": 1000, "nCr": 1000, "nBr": 0, "nCs": 0, "idType": "STANDARD", "brs": false, "canfd": false, "remote": false, "stMin": 10, "bs": 10, "maxWTF": 0, "dlc": 8, "padding": false, "paddingValue": "0x00"}}], "udsTime": {"pTime": 2000, "pExtTime": 5000, "s3Time": 5000, "testerPresentEnable": false}, "allServiceList": {"0x10": [{"id": "72c5c28e-2113-41ab-8bbb-b1f941b16787", "name": "DiagnosticSessionControl160", "serviceId": "0x10", "params": [{"id": "87715468-9558-4167-8908-9b8541236e08", "name": "diagnosticSessionType", "bitLen": 8, "deletable": false, "editable": true, "type": "NUM", "value": {"type": "<PERSON><PERSON><PERSON>", "data": [1]}, "phyValue": 1}], "respParams": [{"id": "0b6cc109-3ea5-4246-986d-f9209fb82ce2", "name": "diagnosticSessionType", "bitLen": 8, "deletable": false, "editable": true, "type": "NUM", "value": {"type": "<PERSON><PERSON><PERSON>", "data": [1]}, "phyValue": 1}, {"id": "9d1ec003-e788-4f77-bf2f-15337ca15090", "name": "sessionParameterRecord", "bitLen": 8, "deletable": true, "editable": true, "type": "ARRAY", "value": {"type": "<PERSON><PERSON><PERSON>", "data": [0]}, "phyValue": "00"}], "suppress": false, "autoSubfunc": true}]}, "simulateBy": "8e639030-23ea-45f1-a906-82c4bb4fb192"}}, "subFunction": {}, "nodes": {"8e639030-23ea-45f1-a906-82c4bb4fb192": {"id": "8e639030-23ea-45f1-a906-82c4bb4fb192", "name": "Test Config 01", "script": "test.ts", "reportPath": "./report", "channel": ["b283da7b-1165-4e62-a609-b0eac40cb8a8"], "isTest": true, "workNode": ""}}, "database": {"lin": {}, "can": {}}, "graphs": {}, "guages": {}, "vars": {}, "datas": {}, "panels": {}}, "project": {"wins": {"message": {"pos": {"x": 383, "y": 219, "w": 2562, "h": 356}, "options": {"params": {}}, "title": "message", "label": "Message", "id": "message", "layoutType": "bottom", "hide": true}, "1e1f0b3f-a46b-45e9-848f-ed899671cd5e_ia": {"pos": {"x": 1356, "y": 61, "w": 700, "h": 400}, "title": "cani", "label": "IA", "id": "1e1f0b3f-a46b-45e9-848f-ed899671cd5e_ia", "options": {"params": {"edit-index": "1e1f0b3f-a46b-45e9-848f-ed899671cd5e"}, "name": "Can IA"}, "hide": false}, "trace": {"pos": {"x": 170, "y": 0, "w": 700, "h": 400}, "title": "trace", "label": "Trace", "id": "trace", "options": {}, "hide": true}, "test": {"pos": {"x": 82, "y": 50.5, "w": 1274, "h": 487}, "title": "test", "label": "Test", "id": "test", "options": {}, "hide": false}, "tester": {"pos": {"x": 1173, "y": 295.5, "w": 1174, "h": 400}, "title": "tester", "label": "<PERSON><PERSON> Tester", "id": "tester", "options": {}}}, "example": {"catalog": "TEST"}}}