{"data": {"devices": {"9a159bbd-c196-4a83-b866-45fa1ac1aed4": {"type": "eth", "ethDevice": {"device": {"label": "", "handle": "127.0.0.1", "id": "", "detail": {"address": "127.0.0.1", "netmask": "*********", "family": "IPv4", "mac": "00:00:00:00:00:00", "internal": true, "cidr": "127.0.0.1/8"}}, "name": "SIMULATE_0", "id": "9a159bbd-c196-4a83-b866-45fa1ac1aed4", "vendor": "simulate"}}}, "ia": {}, "tester": {"4c92bfff-29ef-424c-93f0-f8d2224668f3": {"id": "4c92bfff-29ef-424c-93f0-f8d2224668f3", "name": "Tester_eth_1", "type": "eth", "script": "", "targetDeviceId": "9a159bbd-c196-4a83-b866-45fa1ac1aed4", "seqList": [{"name": "Seq0", "services": [{"enable": true, "checkResp": true, "retryNum": 0, "addressIndex": 0, "failBehavior": "stop", "serviceId": "e8f35a74-33e6-4114-a258-e7ef67ce2382", "delay": 50, "_X_ROW_KEY": "row_45"}]}], "address": [{"type": "eth", "ethAddr": {"name": "Addr0", "entity": {"vin": "ecubus-pro eth000", "eid": "00-00-00-00-00-00", "gid": "00-00-00-00-00-00", "logicalAddr": 100, "taType": "physical", "virReqType": "broadcast", "virReqAddr": "", "entityNotFoundBehavior": "normal", "nodeType": "node"}, "tester": {"testerLogicalAddr": 200, "routeActiveTime": 0, "createConnectDelay": "1000"}, "virReqType": "broadcast", "entityNotFoundBehavior": "normal", "taType": "physical"}}], "udsTime": {"pTime": 2000, "pExtTime": 5000, "s3Time": 5000, "testerPresentEnable": false}, "allServiceList": {"0x10": [{"id": "e8f35a74-33e6-4114-a258-e7ef67ce2382", "name": "DiagnosticSessionControl160", "serviceId": "0x10", "params": [{"id": "3cee0a56-ad97-4daa-8984-0ea29d5c7187", "name": "diagnosticSessionType", "bitLen": 8, "deletable": false, "editable": true, "type": "NUM", "value": {"type": "<PERSON><PERSON><PERSON>", "data": [1]}, "phyValue": 1}], "respParams": [{"id": "2498fd9e-5f66-4c20-b22c-7a0cee1b8324", "name": "diagnosticSessionType", "bitLen": 8, "deletable": false, "editable": true, "type": "NUM", "value": {"type": "<PERSON><PERSON><PERSON>", "data": [1]}, "phyValue": 1}, {"id": "d7a08c1f-ddd0-444d-bff7-8e665df5b7c2", "name": "sessionParameterRecord", "bitLen": 8, "deletable": true, "editable": true, "type": "ARRAY", "value": {"type": "<PERSON><PERSON><PERSON>", "data": [0]}, "phyValue": "00"}], "suppress": false, "autoSubfunc": true}]}, "simulateBy": "aacaa592-6ccf-48cd-a8dc-a591bc89a7be", "enableCodeGen": true, "generateConfigs": [{"tempaltePath": "qqq", "generatePath": ""}]}}, "subFunction": {}, "nodes": {"aacaa592-6ccf-48cd-a8dc-a591bc89a7be": {"name": "sim_entity", "type": "eth", "id": "aacaa592-6ccf-48cd-a8dc-a591bc89a7be", "channel": ["9a159bbd-c196-4a83-b866-45fa1ac1aed4"], "attachTester": "4c92bfff-29ef-424c-93f0-f8d2224668f3", "script": "node.ts"}}, "database": {"lin": {}, "can": {}}, "graphs": {}, "guages": {}, "vars": {}, "datas": {}, "panels": {}}, "project": {"wins": {"message": {"pos": {"x": 383, "y": 191, "w": 1280, "h": 200}, "options": {"params": {}}, "title": "message", "label": "Message", "id": "message", "layoutType": "bottom", "hide": true}, "trace": {"pos": {"x": 723, "y": 150, "w": 1120, "h": 400}, "title": "trace", "label": "Trace", "id": "trace", "options": {}, "hide": false}, "network": {"pos": {"x": 56, "y": 1, "w": 600, "h": 400}, "title": "network", "label": "Network", "id": "network", "options": {}, "hide": false}, "4c92bfff-29ef-424c-93f0-f8d2224668f3_sequence": {"pos": {"x": -63, "y": 0, "w": 700, "h": 400}, "title": "testerSequence", "label": "Sequence", "id": "4c92bfff-29ef-424c-93f0-f8d2224668f3_sequence", "options": {"params": {"edit-index": "4c92bfff-29ef-424c-93f0-f8d2224668f3"}, "name": "Tester_eth_1"}, "hide": false}, "tester": {"pos": {"x": 337, "y": 130, "w": 1069, "h": 400}, "title": "tester", "label": "<PERSON><PERSON> Tester", "id": "tester", "options": {}, "hide": false, "backupPos": {"x": 1180, "y": 440, "w": 600, "h": 400}, "isMax": false}}, "example": {"catalog": "Ethernet"}}}