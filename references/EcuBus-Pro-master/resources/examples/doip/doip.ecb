{"data": {"devices": {"9a159bbd-c196-4a83-b866-45fa1ac1aed4": {"type": "eth", "ethDevice": {"device": {"label": "", "handle": "127.0.0.1", "id": "", "detail": {"address": "127.0.0.1", "netmask": "*********", "family": "IPv4", "mac": "00:00:00:00:00:00", "internal": true, "cidr": "127.0.0.1/8"}}, "name": "SIMULATE_0", "id": "9a159bbd-c196-4a83-b866-45fa1ac1aed4", "vendor": "simulate"}}}, "ia": {}, "tester": {"4c92bfff-29ef-424c-93f0-f8d2224668f3": {"id": "4c92bfff-29ef-424c-93f0-f8d2224668f3", "name": "Tester_eth_1", "type": "eth", "script": "", "targetDeviceId": "9a159bbd-c196-4a83-b866-45fa1ac1aed4", "seqList": [{"name": "Seq0", "services": [{"enable": true, "checkResp": true, "retryNum": 0, "addressIndex": 0, "failBehavior": "stop", "serviceId": "e8f35a74-33e6-4114-a258-e7ef67ce2382", "delay": 50, "_X_ROW_KEY": "row_45"}]}], "address": [{"type": "eth", "ethAddr": {"name": "Addr0", "entity": {"vin": "12345678901234567", "eid": "e1-e2-e3-e4-e5-e0", "gid": "a1-a2-a3-a4-a5-a6", "logicalAddr": 57344, "taType": "physical", "virReqType": "broadcast", "virReqAddr": "", "entityNotFoundBehavior": "normal", "nodeType": "gateway", "nodeAddr": 57345}, "tester": {"testerLogicalAddr": 20000, "routeActiveTime": 0, "createConnectDelay": "1000"}, "virReqType": "broadcast", "entityNotFoundBehavior": "normal", "taType": "physical"}}], "udsTime": {"pTime": 2000, "pExtTime": 5000, "s3Time": 5000, "testerPresentEnable": false}, "allServiceList": {"0x10": [{"id": "e8f35a74-33e6-4114-a258-e7ef67ce2382", "name": "DiagnosticSessionControl160", "serviceId": "0x10", "params": [{"id": "3cee0a56-ad97-4daa-8984-0ea29d5c7187", "name": "diagnosticSessionType", "bitLen": 8, "deletable": false, "editable": true, "type": "NUM", "value": {"type": "<PERSON><PERSON><PERSON>", "data": [1]}, "phyValue": 1}], "respParams": [{"id": "2498fd9e-5f66-4c20-b22c-7a0cee1b8324", "name": "diagnosticSessionType", "bitLen": 8, "deletable": false, "editable": true, "type": "NUM", "value": {"type": "<PERSON><PERSON><PERSON>", "data": [1]}, "phyValue": 1}, {"id": "d7a08c1f-ddd0-444d-bff7-8e665df5b7c2", "name": "sessionParameterRecord", "bitLen": 8, "deletable": true, "editable": true, "type": "ARRAY", "value": {"type": "<PERSON><PERSON><PERSON>", "data": [0]}, "phyValue": "00"}], "suppress": false, "autoSubfunc": true}]}}}, "subFunction": {}, "nodes": {}}, "project": {"wins": {"message": {"pos": {"x": 383, "y": 219, "w": 1280, "h": 200}, "options": {"params": {}}, "title": "message", "label": "Message", "id": "message", "layoutType": "bottom", "hide": true}, "trace": {"pos": {"x": 140, "y": 300, "w": 1120, "h": 400}, "title": "trace", "label": "Trace", "id": "trace", "options": {}, "hide": true}, "network": {"pos": {"x": 351.5, "y": 130, "w": 600, "h": 400}, "title": "network", "label": "Network", "id": "network", "options": {}, "hide": true}, "4c92bfff-29ef-424c-93f0-f8d2224668f3_sequence": {"pos": {"x": 260, "y": 20, "w": 940, "h": 400}, "title": "testerSequence", "label": "Sequence", "id": "4c92bfff-29ef-424c-93f0-f8d2224668f3_sequence", "options": {"params": {"edit-index": "4c92bfff-29ef-424c-93f0-f8d2224668f3"}, "name": "Tester_eth_1"}, "hide": true}, "hardware": {"pos": {"x": 340, "y": 140, "w": 600, "h": 400}, "title": "hardware", "label": "Devices", "id": "hardware", "options": {}, "hide": true}, "tester": {"pos": {"x": 0, "y": -28, "w": 2560, "h": 1061}, "title": "tester", "label": "<PERSON><PERSON> Tester", "id": "tester", "options": {}, "hide": false, "backupPos": {"x": 351.5, "y": 130, "w": 600, "h": 400}, "isMax": true}}, "example": {"catalog": "Ethernet"}}}