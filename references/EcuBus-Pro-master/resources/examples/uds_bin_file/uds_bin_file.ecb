{"data": {"devices": {"8e79b126-cf28-4ee7-97a8-c1ab51ebb27f": {"type": "can", "canDevice": {"id": "8e79b126-cf28-4ee7-97a8-c1ab51ebb27f", "name": "SIMULATE_0", "handle": 0, "vendor": "simulate", "canfd": false, "database": "", "bitrate": {"sjw": 1, "timeSeg1": 13, "timeSeg2": 2, "preScaler": 10, "freq": 500000, "clock": "80", "_X_ROW_KEY": "row_58"}}}}, "ia": {}, "tester": {"f304259b-dc7d-460b-9729-4eee2d1156de": {"id": "f304259b-dc7d-460b-9729-4eee2d1156de", "name": "Tester", "type": "can", "script": "", "targetDeviceId": "8e79b126-cf28-4ee7-97a8-c1ab51ebb27f", "seqList": [{"name": "Seq0", "services": [{"enable": true, "checkResp": false, "retryNum": 0, "addressIndex": 0, "failBehavior": "stop", "serviceId": "88b26388-ea29-44be-9e51-cb2590e5a541", "delay": 0, "_X_ROW_KEY": "row_82"}]}], "address": [{"type": "can", "canAddr": {"name": "Addr0", "addrFormat": "NORMAL", "addrType": "PHYSICAL", "SA": "0x1", "TA": "0x2", "AE": "", "canIdTx": "0x55", "canIdRx": "0x56", "nAs": 1000, "nAr": 1000, "nBs": 1000, "nCr": 1000, "nBr": 0, "nCs": 0, "idType": "STANDARD", "brs": false, "canfd": false, "remote": false, "stMin": 10, "bs": 10, "maxWTF": 0, "dlc": 8, "padding": false, "paddingValue": "0x00"}}], "udsTime": {"pTime": 2000, "pExtTime": 5000, "s3Time": 5000, "testerPresentEnable": false}, "allServiceList": {"0x10": [], "Job": [], "0x34": [{"id": "6ebe102f-df6c-4149-9d2d-ee731a819764", "name": "RequestDownload520", "serviceId": "0x34", "params": [{"id": "2502ed20-5785-4771-8829-cc9261116f74", "name": "dataFormatIdentifier", "bitLen": 8, "deletable": true, "editable": true, "type": "NUM", "value": {"type": "<PERSON><PERSON><PERSON>", "data": [0]}, "phyValue": "00"}, {"id": "3ecff8da-b452-440d-b386-20491611456a", "name": "addressAndLengthFormatIdentifier", "bitLen": 8, "deletable": true, "editable": true, "type": "NUM", "value": {"type": "<PERSON><PERSON><PERSON>", "data": [0]}, "phyValue": "00"}, {"id": "29d799fc-8dbe-49e4-b64e-f282a9e3c774", "name": "memoryAddress", "bitLen": 32, "deletable": true, "editable": true, "type": "NUM", "value": {"type": "<PERSON><PERSON><PERSON>", "data": [0, 0, 0, 0]}, "phyValue": "00"}, {"id": "27be151c-a2a0-4dd1-9c29-2e03bf1c8ef6", "name": "memorySize", "bitLen": 32, "deletable": true, "editable": true, "type": "NUM", "value": {"type": "<PERSON><PERSON><PERSON>", "data": [0, 0, 0, 0]}, "phyValue": "00"}], "respParams": [{"id": "9b51cc0d-0b1e-4c58-bd55-2539843f114d", "name": "lengthFormatIdentifier", "bitLen": 8, "deletable": true, "editable": true, "type": "NUM", "value": {"type": "<PERSON><PERSON><PERSON>", "data": [0]}, "phyValue": "00"}, {"id": "ec250091-e065-4980-a53e-1d7ed07e2a60", "name": "maxNumberOfBlockLength", "bitLen": 32, "deletable": true, "editable": true, "type": "ARRAY", "value": {"type": "<PERSON><PERSON><PERSON>", "data": [0]}, "phyValue": "00"}], "suppress": false, "autoSubfunc": true}], "0x36": [{"id": "cc8c0b79-7325-476a-a1c0-5d0d7feeebbc", "name": "TransferData540", "serviceId": "0x36", "params": [{"id": "267ed773-b8aa-4cc0-a506-82baa9f5ba6d", "name": "blockSequenceCounter", "bitLen": 8, "deletable": true, "editable": true, "type": "NUM", "value": {"type": "<PERSON><PERSON><PERSON>", "data": [0]}, "phyValue": "00"}, {"id": "356f97fb-5643-4200-9c02-5618b375a70d", "name": "transferRequestParameterRecord", "bitLen": 8, "deletable": true, "editable": true, "type": "ARRAY", "value": {"type": "<PERSON><PERSON><PERSON>", "data": [0]}, "phyValue": "00"}], "respParams": [{"id": "ac6c5899-42bb-4c70-9c6c-663f13f03814", "name": "blockSequenceCounter", "bitLen": 8, "deletable": true, "editable": true, "type": "NUM", "value": {"type": "<PERSON><PERSON><PERSON>", "data": [0]}, "phyValue": "00"}, {"id": "3d05a721-ed1c-4ce5-aa86-77e2cb04af12", "name": "transferResponseParameterRecord", "bitLen": 8, "deletable": true, "editable": true, "type": "ARRAY", "value": {"type": "<PERSON><PERSON><PERSON>", "data": [0]}, "phyValue": "00"}], "suppress": false, "autoSubfunc": true}], "0x37": [{"id": "47b9ceb6-281c-49ab-ba71-dca44b9e252a", "name": "RequestTransferExit550", "serviceId": "0x37", "params": [{"id": "81640011-bacc-47b6-8068-4310ce4421bd", "name": "transferRequestParameterRecord", "bitLen": 8, "deletable": true, "editable": true, "type": "ARRAY", "value": {"type": "<PERSON><PERSON><PERSON>", "data": [0]}, "phyValue": "00"}], "respParams": [{"id": "69026a45-5bcd-4ee4-9efb-8c948ea6bf7b", "name": "transferResponseParameterRecord", "bitLen": 8, "deletable": true, "editable": true, "type": "ARRAY", "value": {"type": "<PERSON><PERSON><PERSON>", "data": [0]}, "phyValue": "00"}], "suppress": false, "autoSubfunc": true}], "RequestDownloadBin": [{"id": "88b26388-ea29-44be-9e51-cb2590e5a541", "name": "RequestDownloadBin0", "serviceId": "RequestDownloadBin", "params": [{"id": "274c964c-67aa-41a9-8e6f-559b972b8189", "name": "dataFormatIdentifier", "bitLen": 8, "deletable": false, "editable": true, "type": "NUM", "phyValue": "00", "value": {"type": "<PERSON><PERSON><PERSON>", "data": [0]}}, {"id": "fd679aee-6681-4807-92a3-f2c8a532d7ea", "name": "addressAndLengthFormatIdentifier", "bitLen": 8, "deletable": false, "editable": true, "type": "NUM", "phyValue": "0x44", "value": {"type": "<PERSON><PERSON><PERSON>", "data": [68]}}, {"id": "e7a73620-d435-48ee-a44a-bd256c7cde7f", "name": "memoryAddress", "bitLen": 32, "deletable": false, "editable": true, "type": "NUM", "phyValue": "00", "value": {"type": "<PERSON><PERSON><PERSON>", "data": [0, 0, 0, 0]}}, {"id": "a22e0cf9-0e1a-4cb9-891c-53be3fd4c2b1", "name": "binFile", "bitLen": 48, "deletable": false, "editable": true, "type": "FILE", "phyValue": "ecu.ts", "value": {"type": "<PERSON><PERSON><PERSON>", "data": [101, 99, 117, 46, 116, 115]}}], "respParams": [], "suppress": false, "autoSubfunc": true}]}, "simulateBy": "5c043bd6-ddca-449a-b535-1ae865085123"}}, "subFunction": {}, "nodes": {"5c043bd6-ddca-449a-b535-1ae865085123": {"name": "Node 1", "type": "can", "id": "5c043bd6-ddca-449a-b535-1ae865085123", "channel": ["8e79b126-cf28-4ee7-97a8-c1ab51ebb27f"], "workNode": "", "attachTester": "f304259b-dc7d-460b-9729-4eee2d1156de", "script": "ecu.ts"}}, "database": {"lin": {}, "can": {}}, "graphs": {}, "tests": {}, "guages": {}, "vars": {"af7af829-5aea-4f27-93c7-1f2e34a35ada": {"type": "user", "id": "af7af829-5aea-4f27-93c7-1f2e34a35ada", "name": "UDS"}, "16fb6eca-aa6a-4b37-beb0-a3429f4f4b4c": {"name": "start", "desc": "", "type": "user", "value": {"type": "number", "initValue": 0, "min": 0, "max": 1}, "id": "16fb6eca-aa6a-4b37-beb0-a3429f4f4b4c", "parentId": "af7af829-5aea-4f27-93c7-1f2e34a35ada"}}, "datas": {}, "panels": {"5913dcc1-8356-415f-88ef-384f32f480d9": {"name": "UDS Donwload", "id": "5913dcc1-8356-415f-88ef-384f32f480d9", "rule": [{"type": "grid", "props": {"rule": {"layout": [{"i": "Ftesmac6skjzaec", "x": 0, "y": 0, "w": 9, "h": 2}, {"i": "F1kumac6skjzafc", "x": 9, "y": 0, "w": 14, "h": 2}, {"i": "F0y5mac6skjzagc", "x": 0, "y": 2, "w": 24, "h": 1}], "row": 24, "rowHeight": 80, "margin": 10, "style": {}, "class": {}, "col": 1}}, "children": [{"type": "LocalImage", "title": "", "style": {"width": "100px", "height": "100px"}, "props": {"src": "https://ecubus.oss-cn-chengdu.aliyuncs.com/img/logo256.png"}, "_fc_id": "id_F8hbmac6sys9aoc", "name": "ref_Frhimac6sys9apc", "display": true, "hidden": false, "_fc_drag_tag": "LocalImage", "slot": "Ftesmac6skjzaec"}, {"type": "BButton", "props": {"releaseValue": 0, "pressValue": 1, "variable": {"type": "variable", "enable": true, "id": "16fb6eca-aa6a-4b37-beb0-a3429f4f4b4c", "name": "start", "color": "#c57c6e", "yAxis": {"min": 0, "max": 1}, "bindValue": {"variableId": "16fb6eca-aa6a-4b37-beb0-a3429f4f4b4c", "variableType": "user", "variableName": "start", "variableFullName": "UDS.start"}}, "toggleMode": true, "type": "primary", "plain": false}, "children": ["<PERSON><PERSON>"], "_fc_id": "id_F8iymac6t9foauc", "name": "ref_Ffydmac6t9foavc", "field": "Fiyamac6t9foawc", "display": true, "hidden": false, "_fc_drag_tag": "BButton", "slot": "F1kumac6skjzafc"}, {"type": "xProgress", "field": "Fapsmabx5vf9aec", "style": {"width": "100%"}, "props": {"strokeWidth": 20, "textInside": true, "showText": true, "color": "#409EFF", "variable": {"type": "variable", "enable": true, "id": "Statistics.f304259b-dc7d-460b-9729-4eee2d1156de.0", "name": "Seq #0", "color": "#7e02e3", "yAxis": {"min": 0, "max": 100, "unit": "%"}, "bindValue": {"variableId": "Statistics.f304259b-dc7d-460b-9729-4eee2d1156de.0", "variableType": "system", "variableName": "Seq #0", "variableFullName": "Statistics.Tester.Seq #0"}}}, "_fc_id": "id_Ffh9mabx5vf9afc", "name": "ref_F2hzmabx5vf9agc", "display": true, "hidden": false, "_fc_drag_tag": "xProgress", "slot": "F0y5mac6skjzagc"}], "_fc_id": "id_Fsxfmac6skjnabc", "name": "ref_Fagemac6skjnacc", "display": true, "hidden": false, "_fc_drag_tag": "grid"}], "options": {"submitBtn": {"show": false, "innerText": "Submit"}, "form": {"inline": false, "hideRequiredAsterisk": false, "labelPosition": "right", "size": "default", "labelWidth": "125px"}, "resetBtn": {"show": false, "innerText": "Reset"}, "formName": "UDS Donwload"}}}}, "project": {"wins": {"message": {"pos": {"x": 383, "y": 105, "w": 2562, "h": 261}, "options": {"params": {}}, "title": "message", "label": "Message", "id": "message", "layoutType": "bottom"}, "trace": {"pos": {"x": 14, "y": 0, "w": 1195, "h": 448}, "title": "trace", "label": "Trace", "id": "trace", "options": {}}, "p5913dcc1-8356-415f-88ef-384f32f480d9": {"pos": {"x": 32.5, "y": 463, "w": 716, "h": 400}, "title": "panelPreview", "label": "PanelPreview", "id": "p5913dcc1-8356-415f-88ef-384f32f480d9", "options": {"params": {"edit-index": "p5913dcc1-8356-415f-88ef-384f32f480d9"}, "name": "UDS Donwload"}}, "variable": {"pos": {"x": 347.5, "y": 106, "w": 840, "h": 400}, "title": "variable", "label": "Variable", "id": "variable", "options": {}}, "tester": {"pos": {"x": 46, "y": 284, "w": 1067, "h": 444}, "title": "tester", "label": "<PERSON><PERSON> Tester", "id": "tester", "options": {}, "hide": false}, "network": {"pos": {"x": 673.5, "y": 232, "w": 600, "h": 400}, "title": "network", "label": "Network", "id": "network", "options": {}}, "f304259b-dc7d-460b-9729-4eee2d1156de_sequence": {"pos": {"x": 1089.5, "y": 144, "w": 700, "h": 400}, "title": "testerSequence", "label": "Sequence", "id": "f304259b-dc7d-460b-9729-4eee2d1156de_sequence", "options": {"params": {"edit-index": "f304259b-dc7d-460b-9729-4eee2d1156de"}, "name": "Tester"}}}, "example": {"catalog": "UDS"}}}