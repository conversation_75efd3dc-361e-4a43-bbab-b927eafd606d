{"data": {"devices": {"07ab9e32-4503-4d74-8362-92f6c785a82a": {"type": "lin", "linDevice": {"device": {"label": "", "handle": 1, "id": ""}, "name": "PEAK_0", "id": "07ab9e32-4503-4d74-8362-92f6c785a82a", "vendor": "peak", "baudRate": 19200, "mode": "MASTER", "database": "655bf90d-b010-49b5-9cf8-a9787319b5b4"}}}, "ia": {"25cec12a-572d-4221-b85e-2728628134f7": {"name": "Lin IA", "type": "lin", "id": "25cec12a-572d-4221-b85e-2728628134f7", "devices": ["07ab9e32-4503-4d74-8362-92f6c785a82a"], "action": []}}, "tester": {"5459ce47-1751-4a82-94a0-9bd33ae289ab": {"id": "5459ce47-1751-4a82-94a0-9bd33ae289ab", "name": "Tester_lin_1", "type": "lin", "script": "", "targetDeviceId": "07ab9e32-4503-4d74-8362-92f6c785a82a", "seqList": [{"name": "Seq0", "services": [{"enable": true, "checkResp": true, "retryNum": 0, "addressIndex": 0, "failBehavior": "stop", "serviceId": "59210a0a-b52b-4a6f-bdd2-b867694551f4", "delay": 50, "_X_ROW_KEY": "row_194"}]}], "address": [{"type": "lin", "linAddr": {"name": "Motor1_addr", "addrType": "PHYSICAL", "nad": 2, "stMin": 20, "nAs": 1000, "nCr": 1000, "schType": "DIAG_ONLY"}}], "udsTime": {"pTime": 500, "pExtTime": 5000, "s3Time": 5000, "testerPresentEnable": false}, "allServiceList": {"0x10": [{"id": "59210a0a-b52b-4a6f-bdd2-b867694551f4", "name": "DiagnosticSessionControl160", "serviceId": "0x10", "params": [{"id": "eebb0212-96da-404e-bdc7-0a22dc19f94e", "name": "diagnosticSessionType", "bitLen": 8, "deletable": false, "editable": true, "type": "NUM", "value": {"type": "<PERSON><PERSON><PERSON>", "data": [1]}, "phyValue": 1}, {"id": "b3d3774a-bb3e-4f80-9304-b53728f7ded0", "name": "param2", "desc": "", "phyValue": "00 00 00 00", "value": {"type": "<PERSON><PERSON><PERSON>", "data": [0, 0, 0, 0]}, "bitLen": 64, "type": "ARRAY", "deletable": true, "editable": true}], "respParams": [{"id": "1cef1b8f-c47e-4006-a787-b80c28b46b87", "name": "diagnosticSessionType", "bitLen": 8, "deletable": false, "editable": true, "type": "NUM", "value": {"type": "<PERSON><PERSON><PERSON>", "data": [1]}, "phyValue": 1}, {"id": "f5bec8cc-1a1d-40c2-984a-632ec3d956d6", "name": "sessionParameterRecord", "bitLen": 8, "deletable": true, "editable": true, "type": "ARRAY", "value": {"type": "<PERSON><PERSON><PERSON>", "data": [0]}, "phyValue": "00"}], "suppress": false, "autoSubfunc": true}]}, "simulateBy": "9505c05a-4fe5-42e1-a45d-b1cbc14854ba"}}, "subFunction": {}, "nodes": {"9505c05a-4fe5-42e1-a45d-b1cbc14854ba": {"name": "Node 1", "type": "lin", "id": "9505c05a-4fe5-42e1-a45d-b1cbc14854ba", "channel": ["07ab9e32-4503-4d74-8362-92f6c785a82a"], "workNode": "lin:Motor1", "attachTester": "5459ce47-1751-4a82-94a0-9bd33ae289ab", "script": "node.ts"}}, "database": {"lin": {"655bf90d-b010-49b5-9cf8-a9787319b5b4": {"eventTriggeredFrames": {"ETF_MotorStates": {"name": "ETF_MotorStates", "schTableName": "ETF_CollisionResolving", "frameId": 58, "frameNames": ["Motor1State_Event", "Motor2State_Event"]}}, "sporadicFrames": {}, "signalRep": {"MotorSpeed": ["MotorSpeed"], "encTemperature": ["Motor1Temp", "Motor2Temp"], "test": ["MotorDirection"]}, "global": {"LIN_protocol_version": "2.2", "LIN_language_version": "2.2", "LIN_speed": 19.2}, "node": {"master": {"nodeName": "SeatECU", "timeBase": 5, "jitter": 0.1}, "salveNode": ["Motor1", "Motor2"]}, "signals": {"Motor1_Dynamic_Sig": {"signalName": "Motor1_Dynamic_Sig", "signalSizeBits": 8, "initValue": 7, "punishedBy": "Motor1", "subscribedBy": ["SeatECU"], "singleType": "<PERSON><PERSON><PERSON>"}, "Motor1ErrorCode": {"signalName": "Motor1ErrorCode", "signalSizeBits": 8, "initValue": 5, "punishedBy": "Motor1", "subscribedBy": ["SeatECU"], "singleType": "<PERSON><PERSON><PERSON>"}, "Motor1ErrorValue": {"signalName": "Motor1ErrorValue", "signalSizeBits": 8, "initValue": 1, "punishedBy": "Motor1", "subscribedBy": ["SeatECU"], "singleType": "<PERSON><PERSON><PERSON>"}, "Motor1LinError": {"signalName": "Motor1LinError", "signalSizeBits": 1, "initValue": 0, "punishedBy": "Motor1", "subscribedBy": ["SeatECU"], "singleType": "<PERSON><PERSON><PERSON>"}, "Motor1Position": {"signalName": "Motor1Position", "signalSizeBits": 32, "initValue": [0, 0, 0, 0], "punishedBy": "Motor1", "subscribedBy": ["SeatECU"], "singleType": "ByteArray"}, "Motor1Temp": {"signalName": "Motor1Temp", "signalSizeBits": 7, "initValue": 5, "punishedBy": "Motor1", "subscribedBy": ["SeatECU"], "singleType": "<PERSON><PERSON><PERSON>", "value": 20}, "Motor2_Dynamic_Sig": {"signalName": "Motor2_Dynamic_Sig", "signalSizeBits": 8, "initValue": 8, "punishedBy": "Motor2", "subscribedBy": ["SeatECU"], "singleType": "<PERSON><PERSON><PERSON>"}, "Motor2ErrorCode": {"signalName": "Motor2ErrorCode", "signalSizeBits": 8, "initValue": 2, "punishedBy": "Motor2", "subscribedBy": ["SeatECU"], "singleType": "<PERSON><PERSON><PERSON>"}, "Motor2ErrorValue": {"signalName": "Motor2ErrorValue", "signalSizeBits": 8, "initValue": 4, "punishedBy": "Motor2", "subscribedBy": ["SeatECU"], "singleType": "<PERSON><PERSON><PERSON>"}, "Motor2LinError": {"signalName": "Motor2LinError", "signalSizeBits": 1, "initValue": 0, "punishedBy": "Motor2", "subscribedBy": ["SeatECU"], "singleType": "<PERSON><PERSON><PERSON>"}, "Motor2Position": {"signalName": "Motor2Position", "signalSizeBits": 32, "initValue": [0, 0, 0, 0], "punishedBy": "Motor2", "subscribedBy": ["SeatECU"], "singleType": "ByteArray"}, "Motor2Temp": {"signalName": "Motor2Temp", "signalSizeBits": 8, "initValue": 0, "punishedBy": "Motor2", "subscribedBy": ["SeatECU"], "singleType": "<PERSON><PERSON><PERSON>", "value": 1}, "MotorDirection": {"signalName": "MotorDirection", "signalSizeBits": 2, "initValue": 0, "punishedBy": "SeatECU", "subscribedBy": ["Motor1", "Motor2"], "singleType": "<PERSON><PERSON><PERSON>", "value": 2}, "MotorSelection": {"signalName": "MotorSelection", "signalSizeBits": 4, "initValue": 0, "punishedBy": "SeatECU", "subscribedBy": ["Motor1", "Motor2"], "singleType": "<PERSON><PERSON><PERSON>"}, "MotorSpeed": {"signalName": "MotorSpeed", "signalSizeBits": 10, "initValue": 0, "punishedBy": "SeatECU", "subscribedBy": ["Motor1", "Motor2"], "singleType": "<PERSON><PERSON><PERSON>"}}, "frames": {"Motor1_Dynamic": {"name": "Motor1_Dynamic", "id": 53, "publishedBy": "Motor1", "frameSize": 1, "signals": [{"name": "Motor1_Dynamic_Sig", "offset": 0}]}, "Motor1State_Cycl": {"name": "Motor1State_Cycl", "id": 51, "publishedBy": "Motor1", "frameSize": 6, "signals": [{"name": "Motor1Temp", "offset": 0}, {"name": "Motor1Position", "offset": 7}, {"name": "Motor1LinError", "offset": 40}]}, "Motor1State_Event": {"name": "Motor1State_Event", "id": 54, "publishedBy": "Motor1", "frameSize": 3, "signals": [{"name": "Motor1ErrorCode", "offset": 8}, {"name": "Motor1ErrorValue", "offset": 16}]}, "Motor2_Dynamic": {"name": "Motor2_Dynamic", "id": 44, "publishedBy": "Motor2", "frameSize": 1, "signals": [{"name": "Motor2_Dynamic_Sig", "offset": 0}]}, "Motor2State_Cycl": {"name": "Motor2State_Cycl", "id": 52, "publishedBy": "Motor2", "frameSize": 6, "signals": [{"name": "Motor2Temp", "offset": 0}, {"name": "Motor2Position", "offset": 8}, {"name": "Motor2LinError", "offset": 40}]}, "Motor2State_Event": {"name": "Motor2State_Event", "id": 55, "publishedBy": "Motor2", "frameSize": 3, "signals": [{"name": "Motor2ErrorCode", "offset": 8}, {"name": "Motor2ErrorValue", "offset": 16}]}, "MotorControl": {"name": "MotorControl", "id": 45, "publishedBy": "SeatECU", "frameSize": 2, "signals": [{"name": "MotorDirection", "offset": 0}, {"name": "MotorSpeed", "offset": 2}, {"name": "MotorSelection", "offset": 12}]}}, "nodeAttrs": {"Motor1": {"LIN_protocol": "2.2", "configured_NAD": 2, "initial_NAD": 2, "supplier_id": 30, "function_id": 1, "variant": 0, "response_error": "Motor1LinError", "fault_state_signals": [], "P2_min": 100, "ST_min": 20, "N_As_timeout": 1000, "N_Cr_timeout": 1000, "configFrames": ["MotorControl", "Motor1State_Cycl", "Motor1State_Event", "ETF_MotorStates", "Motor1_Dynamic"]}, "Motor2": {"LIN_protocol": "2.2", "configured_NAD": 3, "initial_NAD": 3, "supplier_id": 30, "function_id": 1, "variant": 0, "response_error": "Motor2LinError", "fault_state_signals": [], "P2_min": 100, "ST_min": 20, "N_As_timeout": 1000, "N_Cr_timeout": 1000, "configFrames": ["MotorControl", "Motor2State_Cycl", "Motor2State_Event", "ETF_MotorStates", "Motor2_Dynamic"]}}, "schTables": [{"name": "NormalTable", "entries": [{"name": "MotorControl", "delay": 50, "isCommand": false}, {"name": "Motor1State_Cycl", "delay": 50, "isCommand": false}, {"name": "Motor2State_Cycl", "delay": 50, "isCommand": false}], "_X_ROW_KEY": "row_86"}, {"name": "DynamicTable", "entries": [{"name": "Motor1_Dynamic", "delay": 100, "isCommand": false}, {"name": "Motor2_Dynamic", "delay": 5, "isCommand": false}], "_X_ROW_KEY": "row_87"}, {"name": "NormalTableEvent", "entries": [{"name": "MotorControl", "delay": 50, "isCommand": false}, {"name": "Motor1State_Cycl", "delay": 50, "isCommand": false}, {"name": "Motor2State_Cycl", "delay": 50, "isCommand": false}, {"name": "ETF_MotorStates", "delay": 50, "isCommand": false}], "_X_ROW_KEY": "row_88"}, {"name": "InitTable", "entries": [{"name": "AssignFrameId", "delay": 10, "isCommand": true, "AssignFrameId": {"nodeName": "Motor1", "frameName": "Motor1State_Cycl"}}, {"name": "AssignFrameId", "delay": 10, "isCommand": true, "AssignFrameId": {"nodeName": "Motor2", "frameName": "Motor2State_Cycl"}}, {"name": "AssignFrameId", "delay": 10, "isCommand": true, "AssignFrameId": {"nodeName": "Motor1", "frameName": "Motor1State_Event"}}, {"name": "AssignFrameId", "delay": 10, "isCommand": true, "AssignFrameId": {"nodeName": "Motor2", "frameName": "Motor2State_Event"}}, {"name": "AssignFrameId", "delay": 10, "isCommand": true, "AssignFrameId": {"nodeName": "Motor1", "frameName": "Motor1_Dynamic"}}, {"name": "AssignFrameId", "delay": 10, "isCommand": true, "AssignFrameId": {"nodeName": "Motor1", "frameName": "ETF_MotorStates"}}, {"name": "AssignFrameId", "delay": 10, "isCommand": true, "AssignFrameId": {"nodeName": "Motor2", "frameName": "ETF_MotorStates"}}, {"name": "AssignFrameId", "delay": 10, "isCommand": true, "AssignFrameId": {"nodeName": "Motor1", "frameName": "MotorControl"}}, {"name": "AssignFrameId", "delay": 10, "isCommand": true, "AssignFrameId": {"nodeName": "Motor2", "frameName": "MotorControl"}}], "_X_ROW_KEY": "row_89"}, {"name": "ETF_CollisionResolving", "entries": [{"name": "Motor2State_Event", "delay": 10, "isCommand": false}, {"name": "Motor1State_Event", "delay": 10, "isCommand": false}], "_X_ROW_KEY": "row_90"}], "signalEncodeTypes": {"MotorSpeed": {"name": "MotorSpeed", "encodingTypes": [{"type": "physicalValue", "physicalValue": {"minValue": 0, "maxValue": 0, "scale": 1, "offset": 0, "textInfo": "rpm"}}], "_X_ROW_KEY": "row_95"}, "encTemperature": {"name": "encTemperature", "encodingTypes": [{"type": "physicalValue", "physicalValue": {"minValue": 0, "maxValue": 80, "scale": 0.5, "offset": -20, "textInfo": "Degree"}}], "_X_ROW_KEY": "row_96"}, "test": {"name": "test", "encodingTypes": [{"type": "logicalValue", "logicalValue": {"signalValue": 0, "textInfo": "open"}, "_X_ROW_KEY": "row_331"}, {"type": "logicalValue", "logicalValue": {"signalValue": 1, "textInfo": "close"}, "_X_ROW_KEY": "row_332"}], "_X_ROW_KEY": "row_294"}}, "name": "lin", "id": "655bf90d-b010-49b5-9cf8-a9787319b5b4"}}, "can": {}}, "graphs": {"lin.lin.signals.MotorDirection": {"type": "signal", "enable": true, "id": "lin.lin.signals.MotorDirection", "name": "MotorDirection", "color": "rgba(247, 36, 12, 1)", "yAxis": {"min": 0.43999999999999984, "max": 3.440000000000002, "splitLine": {"show": false}}, "bindValue": {"signalName": "MotorDirection", "startBit": 0, "bitLength": 2, "dbKey": "655bf90d-b010-49b5-9cf8-a9787319b5b4", "dbName": "lin", "frameId": 45}, "xAxis": {"splitLine": {"show": false, "lineStyle": {"type": "dashed"}}, "axisPointer": {"show": false}}, "disZoom": false, "series": {"showSymbol": false, "symbolSize": 6, "symbol": "circle"}}}, "tests": {}, "guages": {"lin.lin.signals.Motor1Temp": {"type": "signal", "enable": true, "id": "lin.lin.signals.Motor1Temp", "name": "Motor1Temp", "color": "#5059c3", "yAxis": {"min": -20, "max": 20, "unit": "Degree"}, "bindValue": {"signalName": "Motor1Temp", "startBit": 0, "bitLength": 7, "dbKey": "655bf90d-b010-49b5-9cf8-a9787319b5b4", "dbName": "lin", "frameId": 51}, "graph": {"id": "gauge"}}, "lin.lin.signals.MotorDirection": {"type": "signal", "enable": true, "id": "lin.lin.signals.MotorDirection", "name": "MotorDirection", "color": "#82fa6d", "yAxis": {"min": 0, "max": 3, "enums": [{"label": "open", "value": 0}, {"label": "close", "value": 1}]}, "bindValue": {"signalName": "MotorDirection", "startBit": 0, "bitLength": 2, "dbKey": "655bf90d-b010-49b5-9cf8-a9787319b5b4", "dbName": "lin", "frameId": 45}, "graph": {"id": "gauge"}}}, "vars": {}, "datas": {}, "panels": {}}, "project": {"wins": {"message": {"pos": {"x": 383, "y": 166, "w": 2560, "h": 220}, "options": {"params": {}}, "title": "message", "label": "Message", "id": "message", "layoutType": "bottom", "hide": true}, "network": {"pos": {"x": 607, "y": 29, "w": 600, "h": 400}, "title": "network", "label": "Network", "id": "network", "options": {}, "hide": false}, "25cec12a-572d-4221-b85e-2728628134f7_ia": {"pos": {"x": 546, "y": 101, "w": 931, "h": 400}, "title": "lini", "label": "IA", "id": "25cec12a-572d-4221-b85e-2728628134f7_ia", "options": {"params": {"edit-index": "25cec12a-572d-4221-b85e-2728628134f7"}, "name": "Lin IA"}, "hide": false}, "5459ce47-1751-4a82-94a0-9bd33ae289ab_sequence": {"pos": {"x": 950, "y": 51, "w": 700, "h": 400}, "title": "testerSequence", "label": "Sequence", "id": "5459ce47-1751-4a82-94a0-9bd33ae289ab_sequence", "options": {"params": {"edit-index": "5459ce47-1751-4a82-94a0-9bd33ae289ab"}, "name": "Tester_lin_1"}, "hide": true}, "graph": {"pos": {"x": 22, "y": 23, "w": 600, "h": 400}, "title": "graph", "label": "Graph", "id": "graph", "options": {}, "hide": true}, "tester": {"pos": {"x": 381, "y": 337, "w": 1095, "h": 400}, "title": "tester", "label": "<PERSON><PERSON> Tester", "id": "tester", "options": {}, "hide": false}, "hardware": {"pos": {"x": 950, "y": 81, "w": 600, "h": 400}, "title": "hardware", "label": "Devices", "id": "hardware", "options": {}, "hide": true}, "trace": {"pos": {"x": 1238.5, "y": 25, "w": 700, "h": 400}, "title": "trace", "label": "Trace", "id": "trace", "options": {}}, "655bf90d-b010-49b5-9cf8-a9787319b5b4": {"pos": {"x": 837.5, "y": 48, "w": 700, "h": 400}, "title": "ldf", "label": "LDF", "id": "655bf90d-b010-49b5-9cf8-a9787319b5b4", "options": {"params": {"edit-index": "655bf90d-b010-49b5-9cf8-a9787319b5b4"}, "name": "lin"}, "hide": true}, "gauge": {"pos": {"x": 1134.5, "y": 325, "w": 924, "h": 400}, "title": "gauge", "label": "Gauge", "id": "gauge", "options": {"params": {"edit-index": "gauge"}}}}, "example": {"catalog": "LIN"}}}