{"data": {"devices": {"d3b915eb-e026-471c-af50-49555b2ea36c": {"type": "can", "canDevice": {"id": "d3b915eb-e026-471c-af50-49555b2ea36c", "name": "SIMULATE_0", "handle": 0, "vendor": "simulate", "canfd": false, "bitrate": {"sjw": 1, "timeSeg1": 13, "timeSeg2": 2, "preScaler": 10, "freq": 500000, "clock": "80", "_X_ROW_KEY": "row_61"}}}}, "ia": {}, "tester": {}, "subFunction": {}, "nodes": {"b6bddb4c-d892-4e38-8361-02f0e35ce46f": {"name": "Node 1", "type": "can", "id": "b6bddb4c-d892-4e38-8361-02f0e35ce46f", "channel": ["d3b915eb-e026-471c-af50-49555b2ea36c"], "script": "node.ts"}}}, "project": {"wins": {"message": {"pos": {"x": 383, "y": 64, "w": 2560, "h": 300}, "options": {"params": {}}, "title": "message", "label": "Message", "id": "message", "layoutType": "bottom", "hide": true}, "network": {"pos": {"x": 640, "y": 60, "w": 600, "h": 400}, "title": "network", "label": "Network", "id": "network", "options": {}, "hide": false, "backupPos": {"x": 1340, "y": 220, "w": 600, "h": 400}, "isMax": false}, "device": {"pos": {"x": 0, "y": 0, "w": 600, "h": 400}, "title": "hardware", "label": "Devices", "id": "device", "options": {"params": {"deviceId": "d3b915eb-e026-471c-af50-49555b2ea36c"}}}}, "example": {"catalog": "UDS"}}}