(()=>{"use strict";var e={d:(r,t)=>{for(var o in t)e.o(t,o)&&!e.o(r,o)&&Object.defineProperty(r,o,{enumerable:!0,get:t[o]})},o:(e,r)=>Object.prototype.hasOwnProperty.call(e,r),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},r={};e.r(r),e.d(r,{HexMemoryMap:()=>s});const t=/:([0-9A-Fa-f]{8,})([0-9A-Fa-f]{2})(?:\r\n|\r|\n|)/g;function o(e){return 255&-e.reduce(((e,r)=>e+r),0)}function n(e){return e.toString(16).toUpperCase().padStart(2,"0")}Number.isInteger=Number.isInteger||function(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e};class s{_blocks;constructor(e){if(this._blocks=new Map,e&&Symbol.iterator in e)for(const r of e){if(!Array.isArray(r)||2!==r.length)throw new Error("First parameter to HexMemoryMap constructor must be an iterable of [addr, bytes] or undefined");this.set(r[0],r[1])}else if("object"==typeof e){if(e){const r=Object.keys(e);for(const t of r)this.set(parseInt(t),e[t])}}else if(null!=e)throw new Error("First parameter to HexMemoryMap constructor must be an iterable of [addr, bytes] or undefined")}set(e,r){if(!Number.isInteger(e))throw new Error("Address passed to HexMemoryMap is not an integer");if(e<0)throw new Error("Address passed to HexMemoryMap is negative");if(!(r instanceof Buffer))throw new Error("Bytes passed to HexMemoryMap are not an Buffer");return this._blocks.set(e,r)}get(e){return this._blocks.get(e)}clear(){return this._blocks.clear()}delete(e){return this._blocks.delete(e)}entries(){return this._blocks.entries()}forEach(e,r){return this._blocks.forEach(e,r)}has(e){return this._blocks.has(e)}keys(){return this._blocks.keys()}values(){return this._blocks.values()}get size(){return this._blocks.size}[Symbol.iterator](){return this._blocks[Symbol.iterator]()}static fromHex(e,r=1/0){const a=new s;let i,f=0,c=0,l=0;for(t.lastIndex=0;null!==(i=t.exec(e));){if(c++,f!==i.index)throw new Error("Malformed hex file: Could not parse between characters "+f+" and "+i.index+' ("'+e.substring(f,Math.min(i.index,f+16)).trim()+'")');f=t.lastIndex;const[,s,h]=i,u=Buffer.from(s.match(/[\da-f]{2}/gi)?.map((e=>parseInt(e,16)))||[]),d=u[0];if(d+4!==u.length)throw new Error("Mismatched record length at record "+c+" ("+i[0].trim()+"), expected "+d+" data bytes but actual length is "+(u.length-4));const g=o(u);if(parseInt(h,16)!==g)throw new Error("Checksum failed at record "+c+" ("+i[0].trim()+"), should be "+g.toString(16));const w=(u[1]<<8)+u[2],b=u[3],p=u.subarray(4);if(0===b){if(a.has(l+w))throw new Error("Duplicated data at record "+c+" ("+i[0].trim()+")");if(w+p.length>65536)throw new Error("Data at record "+c+" ("+i[0].trim()+") wraps over 0xFFFF. This would trigger ambiguous behaviour. Please restructure your data so that for every record the data offset plus the data length do not exceed 0xFFFF.");a.set(l+w,p)}else{if(0!==w)throw new Error("Record "+c+" ("+i[0].trim()+") must have 0000 as data offset.");switch(b){case 1:if(f!==e.length)throw new Error("There is data after an EOF record at record "+c);return a.join(r);case 2:l=(p[0]<<8)+p[1]<<4;break;case 3:case 5:break;case 4:l=(p[0]<<8)+p[1]<<16;break;default:throw new Error("Invalid record type 0x"+n(b)+" at record "+c+" (should be between 0x00 and 0x05)")}}}throw c?new Error("No EOF record at end of file"):new Error("Malformed .hex file, could not parse any registers")}join(e=1/0){const r=Array.from(this.keys()).sort(((e,r)=>e-r)),t=new Map;let o=-1,n=-1;for(let s=0,a=r.length;s<a;s++){const a=r[s],i=this.get(r[s]).length;if(n===a&&n-o<e)t.set(o,t.get(o)+i),n+=i;else{if(!(n<=a))throw new Error("Overlapping data around address 0x"+a.toString(16));t.set(a,i),o=a,n=a+i}}const a=new s;let i,f=-1;for(let e=0,o=r.length;e<o;e++){const o=r[e];t.has(o)&&(i=Buffer.alloc(t.get(o)),a.set(o,i),f=o),i.set(this.get(o),o-f)}return a}static overlapHexMemoryMaps(e){const r=new Set;for(const[,t]of e)for(const[e,o]of t)r.add(e),r.add(e+o.length);const t=Array.from(r.values()).sort(((e,r)=>e-r)),o=new Map;for(let r=0,n=t.length-1;r<n;r++){const n=t[r],s=t[r+1],a=[];for(const[r,t]of e){const e=Array.from(t.keys()).reduce(((e,r)=>r>n?e:Math.max(e,r)),-1);if(-1!==e){const o=t.get(e),i=n-e,f=s-e;i<o.length&&a.push([r,o.subarray(i,f)])}}a.length&&o.set(n,a)}return o}static flattenOverlaps(e){return new s(Array.from(e.entries()).map((([e,r])=>[e,r[r.length-1][1]])))}paginate(e=1024,r=255){if(e<=0)throw new Error("Page size must be greater than zero");const t=new s;let o;const n=Array.from(this.keys()).sort(((e,r)=>e-r));for(let s=0,a=n.length;s<a;s++){const a=n[s],i=this.get(a),f=i.length,c=a+f;for(let n=a-a%e;n<c;n+=e){o=t.get(n),o||(o=Buffer.alloc(e),o.fill(r),t.set(n,o));const s=n-a;let c;s<=0?(c=i.subarray(0,Math.min(e+s,f)),o.set(c,-s)):(c=i.subarray(s,s+Math.min(e,f-s)),o.set(c,0))}}return t}getUint32(e,r){const t=Array.from(this.keys());for(let o=0,n=t.length;o<n;o++){const n=t[o],s=this.get(n),a=s.length;if(n<=e&&e+4<=n+a)return new DataView(s.buffer,e-n,4).getUint32(0,r)}}asHexString(e=16){let r=0,t=-65536;const s=[];if(e<=0)throw new Error("Size of record must be greater than zero");if(e>255)throw new Error("Size of record must be less than 256");const a=Buffer.alloc(6),i=Buffer.alloc(4),f=Array.from(this.keys()).sort(((e,r)=>e-r));for(let l=0,h=f.length;l<h;l++){const h=f[l],u=this.get(h);if(!(u instanceof Buffer))throw new Error("Block at offset "+h+" is not an Buffer");if(h<0)throw new Error("Block at offset "+h+" has a negative thus invalid address");const d=u.length;if(!d)continue;if(h>t+65535&&(t=h-h%65536,r=0,a[0]=2,a[1]=0,a[2]=0,a[3]=4,a[4]=t>>24,a[5]=t>>16,s.push(":"+Array.prototype.map.call(a,n).join("")+n(o(a)))),h<t+r)throw new Error("Block starting at 0x"+h.toString(16)+" overlaps with a previous block.");r=h%65536;let g=0;const w=h+d;if(w>4294967295)throw new Error("Data cannot be over 0xFFFFFFFF");for(;t+r<w;){r>65535&&(t+=65536,r=0,a[0]=2,a[1]=0,a[2]=0,a[3]=4,a[4]=t>>24,a[5]=t>>16,s.push(":"+Array.prototype.map.call(a,n).join("")+n(o(a))));let f=-1;for(;r<65536&&f;)if(f=Math.min(e,w-t-r,65536-r),f){i[0]=f,i[1]=r>>8,i[2]=r,i[3]=0;const e=u.subarray(g,g+f);s.push(":"+Array.prototype.map.call(i,n).join("")+Array.prototype.map.call(e,n).join("")+n((c=e,255&-(i.reduce(((e,r)=>e+r),0)+c.reduce(((e,r)=>e+r),0))))),g+=f,r+=f}}}var c;return s.push(":00000001FF"),s.join("\n")}clone(){const e=new s;for(const[r,t]of this)e.set(r,Buffer.from(t));return e}static fromPaddedBuffer(e,r=255,t=64){if(!(e instanceof Buffer))throw new Error("Bytes passed to fromPaddedBuffer are not an Buffer");const o=new s;let n=0,a=-1,i=0,f=!1;const c=e.length;for(let s=0;s<c;s++)e[s]===r?(n++,n>=t&&(-1!==a&&o.set(i,e.subarray(i,a+1)),f=!0)):(f&&(f=!1,i=s),a=s,n=0);return f||-1===a||o.set(i,e.subarray(i,c)),o}slice(e,r=1/0){if(r<0)throw new Error("Length of the slice cannot be negative");const t=new s;for(const[o,n]of this){const s=n.length;if(o+s>=e&&o<e+r){const a=Math.max(e,o),i=Math.min(e+r,o+s)-a,f=a-o;i>0&&t.set(a,n.subarray(f,f+i))}}return t}slicePad(e,r,t=255){if(r<0)throw new Error("Length of the slice cannot be negative");const o=Buffer.alloc(r,t);for(const[t,n]of this){const s=n.length;if(t+s>=e&&t<e+r){const a=Math.max(e,t),i=Math.min(e+r,t+s)-a,f=a-t;i>0&&o.set(n.subarray(f,f+i),a-e)}}return o}contains(e){for(const[r,t]of e){const e=t.length,o=this.slice(r,e).join().get(r);if(!o||o.length!==e)return!1;for(const e in t)if(t[e]!==o[e])return!1}return!0}}var a=exports;for(var i in r)a[i]=r[i];r.__esModule&&Object.defineProperty(a,"__esModule",{value:!0})})();
//# sourceMappingURL=utli.js.map