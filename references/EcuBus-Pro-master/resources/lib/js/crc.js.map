{"version": 3, "file": "crc.js", "mappings": "mBACA,IAAIA,EAAsB,CCA1BA,EAAwB,CAACC,EAASC,KACjC,IAAI,IAAIC,KAAOD,EACXF,EAAoBI,EAAEF,EAAYC,KAASH,EAAoBI,EAAEH,EAASE,IAC5EE,OAAOC,eAAeL,EAASE,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,IAE1E,ECNDH,EAAwB,CAACS,EAAKC,IAAUL,OAAOM,UAAUC,eAAeC,KAAKJ,EAAKC,GCClFV,EAAyBC,IACH,oBAAXa,QAA0BA,OAAOC,aAC1CV,OAAOC,eAAeL,EAASa,OAAOC,YAAa,CAAEC,MAAO,WAE7DX,OAAOC,eAAeL,EAAS,aAAc,CAAEe,OAAO,GAAO,G,+BC2C9D,QA/CA,MACS,eAAOC,CAASC,GAGrB,IAFA,IAAIC,EAAU,EAELC,EAAI,EAAGA,EAAI,EAAGA,IAChBF,EAAO,GAAKE,IACfD,GAAY,GAAM,EAAIC,EAAM,KAGhC,OAAOD,CACT,CAEO,gBAAOE,CAAUH,GAGtB,IAFA,IAAIC,EAAU,EAELC,EAAI,EAAGA,EAAI,GAAIA,IACjBF,EAAO,GAAKE,IACfD,GAAY,GAAM,GAAKC,EAAM,OAIjC,OAAOD,CACT,CAEO,gBAAOG,CAAUJ,GAGtB,IAFA,IAAIC,EAAU,EAELC,EAAI,EAAGA,EAAI,GAAIA,IACjBF,EAAO,GAAKE,IACfD,GAAY,GAAM,GAAKC,EAAM,YAIjC,OAAOD,CACT,CAEO,qBAAOI,CAAeL,EAAaM,GAExC,IADA,IAAIL,EAAU,EACLC,EAAI,EAAGA,EAAII,EAAOJ,IACpBF,EAAO,GAAKE,IACfD,GAAW,GAAMK,EAAQ,EAAIJ,GAGjC,OAAOD,CACT,GCtCK,MAAMM,EACHC,OACAC,MACAC,YACAC,YACAC,aACAC,gBACAC,iBACAC,aAEAC,UACAC,UACAC,SAER,SAAWZ,GACT,OAAOa,KAAKX,MACd,CACA,SAAWF,CAAMc,GAEf,OADAD,KAAKX,OAASY,EACNA,GACN,KAAK,EACHD,KAAKF,UAAY,IACjB,MACF,KAAK,GACHE,KAAKF,UAAY,MACjB,MACF,KAAK,GACHE,KAAKF,UAAY,WACjB,MACF,QACE,KAAM,oBAEVE,KAAKD,SAAW,GAASE,EAAI,CAC/B,CAEA,QAAWC,GACT,OAAOF,KAAKV,KACd,CACA,QAAWY,CAAKD,GACdD,KAAKV,MAAQW,CACf,CAEA,cAAWE,GACT,OAAOH,KAAKT,WACd,CACA,cAAWY,CAAWF,GACpBD,KAAKT,YAAcU,CACrB,CAEA,WAAWG,GACT,OAAOJ,KAAKR,WACd,CACA,WAAWY,CAAQH,GACjBD,KAAKR,YAAcS,CACrB,CAEA,YAAWI,GACT,OAAOL,KAAKP,YACd,CACA,YAAWY,CAASJ,GAClBD,KAAKP,aAAeQ,CACtB,CAEA,kBAAWK,GACT,OAAON,KAAKN,eACd,CACA,kBAAWY,CAAeL,GACxBD,KAAKN,gBAAkBO,CACzB,CAEA,mBAAWM,GACT,OAAOP,KAAKL,gBACd,CACA,mBAAWY,CAAgBN,GACzBD,KAAKL,iBAAmBM,CAC1B,CAaA,WAAAO,CACEN,EACAf,EACAgB,EACAC,EACAC,EACAC,EACAC,GAEAP,KAAKb,MAAQA,EACba,KAAKE,KAAOA,EACZF,KAAKG,WAAaA,EAClBH,KAAKI,QAAUA,EACfJ,KAAKK,SAAWA,EAChBL,KAAKM,eAAiBA,EACtBN,KAAKO,gBAAkBA,CACzB,CAgBO,mBAAWE,GAmDhB,OAlDKT,KAAKU,QACRV,KAAKU,MAAQ,CACX,IAAItB,EAAI,OAAQ,EAAG,EAAM,EAAM,GAAM,GAAO,GAC5C,IAAIA,EAAI,iBAAkB,EAAG,GAAM,IAAM,KAAM,GAAO,GACtD,IAAIA,EAAI,sBAAuB,EAAG,GAAM,EAAM,GAAM,GAAO,GAC3D,IAAIA,EAAI,YAAa,EAAG,GAAM,IAAM,KAAM,GAAO,GACjD,IAAIA,EAAI,gBAAiB,EAAG,IAAM,IAAM,GAAM,GAAO,GACrD,IAAIA,EAAI,YAAa,EAAG,GAAM,EAAM,GAAM,GAAM,GAChD,IAAIA,EAAI,cAAe,EAAG,IAAM,EAAM,GAAM,GAAO,GACnD,IAAIA,EAAI,WAAY,EAAG,GAAM,IAAM,GAAM,GAAM,GAC/C,IAAIA,EAAI,aAAc,EAAG,GAAM,IAAM,GAAM,GAAO,GAClD,IAAIA,EAAI,WAAY,EAAG,EAAM,EAAM,IAAM,GAAO,GAChD,IAAIA,EAAI,aAAc,EAAG,GAAM,EAAM,GAAM,GAAM,GACjD,IAAIA,EAAI,YAAa,EAAG,EAAM,IAAM,GAAM,GAAM,GAChD,IAAIA,EAAI,aAAc,EAAG,IAAM,EAAM,GAAM,GAAM,GACjD,IAAIA,EAAI,kBAAmB,GAAI,KAAQ,EAAQ,GAAQ,GAAO,GAC9D,IAAIA,EAAI,YAAa,GAAI,MAAQ,EAAQ,GAAQ,GAAM,GACvD,IAAIA,EAAI,kBAAmB,GAAI,KAAQ,KAAQ,GAAQ,GAAO,GAC9D,IAAIA,EAAI,gBAAiB,GAAI,MAAQ,EAAQ,GAAQ,GAAO,GAC5D,IAAIA,EAAI,oBAAqB,GAAI,KAAQ,MAAQ,GAAQ,GAAO,GAChE,IAAIA,EAAI,iBAAkB,GAAI,MAAQ,MAAQ,GAAQ,GAAO,GAC7D,IAAIA,EAAI,gBAAiB,GAAI,MAAQ,MAAQ,GAAQ,GAAO,GAC5D,IAAIA,EAAI,eAAgB,GAAI,KAAQ,EAAQ,GAAQ,GAAO,GAC3D,IAAIA,EAAI,eAAgB,GAAI,KAAQ,EAAQ,GAAQ,GAAO,GAC3D,IAAIA,EAAI,YAAa,GAAI,MAAQ,EAAQ,OAAQ,GAAM,GACvD,IAAIA,EAAI,iBAAkB,GAAI,MAAQ,EAAQ,OAAQ,GAAO,GAC7D,IAAIA,EAAI,gBAAiB,GAAI,KAAQ,MAAQ,OAAQ,GAAO,GAC5D,IAAIA,EAAI,cAAe,GAAI,MAAQ,EAAQ,OAAQ,GAAM,GACzD,IAAIA,EAAI,gBAAiB,GAAI,KAAQ,MAAQ,GAAQ,GAAM,GAC3D,IAAIA,EAAI,eAAgB,GAAI,KAAQ,MAAQ,GAAQ,GAAM,GAC1D,IAAIA,EAAI,gBAAiB,GAAI,MAAQ,EAAQ,GAAQ,GAAO,GAC5D,IAAIA,EAAI,iBAAkB,GAAI,MAAQ,EAAQ,GAAQ,GAAO,GAC7D,IAAIA,EAAI,iBAAkB,GAAI,KAAQ,MAAQ,GAAQ,GAAM,GAC5D,IAAIA,EAAI,YAAa,GAAI,MAAQ,MAAQ,OAAQ,GAAM,GACvD,IAAIA,EAAI,UAAW,GAAI,KAAQ,MAAQ,GAAQ,GAAM,GACrD,IAAIA,EAAI,eAAgB,GAAI,KAAQ,EAAQ,GAAQ,GAAM,GAC1D,IAAIA,EAAI,eAAgB,GAAI,MAAQ,MAAQ,GAAQ,GAAM,GAC1D,IAAIA,EAAI,aAAc,GAAI,KAAQ,MAAQ,OAAQ,GAAM,GACxD,IAAIA,EAAI,eAAgB,GAAI,KAAQ,EAAQ,GAAQ,GAAO,GAC3D,IAAIA,EAAI,QAAS,GAAI,SAAY,WAAY,YAAY,GAAM,GAC/D,IAAIA,EAAI,cAAe,GAAI,SAAY,WAAY,YAAY,GAAO,GACtE,IAAIA,EAAI,UAAW,GAAI,UAAY,WAAY,YAAY,GAAM,GACjE,IAAIA,EAAI,UAAW,GAAI,WAAY,WAAY,YAAY,GAAM,GACjE,IAAIA,EAAI,cAAe,GAAI,SAAY,WAAY,GAAY,GAAO,GACtE,IAAIA,EAAI,cAAe,GAAI,SAAY,EAAY,YAAY,GAAO,GACtE,IAAIA,EAAI,UAAW,GAAI,WAAY,EAAY,GAAY,GAAO,GAClE,IAAIA,EAAI,eAAgB,GAAI,SAAY,WAAY,GAAY,GAAM,GACtE,IAAIA,EAAI,aAAc,GAAI,IAAY,EAAY,GAAY,GAAO,KAGlEY,KAAKU,KACd,CA+BQ,YAAAC,GACNX,KAAKH,UAAY,IAAIe,MAAM,KAE3B,IAAK,IAAIC,EAAW,EAAGA,EAAW,IAAKA,IAAY,CAEjD,IADA,IAAIC,EAAYD,GAAab,KAAKX,OAAS,EAAMW,KAAKF,UAC7CiB,EAAM,EAAGA,EAAM,EAAGA,IACpBD,EAAWd,KAAKD,UACnBe,IAAa,EACbA,GAAYd,KAAKT,aAEjBuB,IAAa,EAGjBd,KAAKH,UAAUgB,GAAYC,EAAWd,KAAKF,SAC7C,CACF,CAEQ,oBAAAkB,GACNhB,KAAKH,UAAY,IAAIe,MAAM,KAE3B,IAAK,IAAIC,EAAW,EAAGA,EAAW,IAAKA,IAAY,CAKjD,IAJA,IAEIC,EAFoB,EAAQlC,SAASiC,IAEHb,KAAKX,OAAS,EAAMW,KAAKF,UAEtDiB,EAAM,EAAGA,EAAM,EAAGA,IACpBD,EAAWd,KAAKD,UACnBe,IAAa,EACbA,GAAYd,KAAKT,aAEjBuB,IAAa,EAIjBA,EAAW,EAAQ5B,eAAe4B,EAAUd,KAAKb,OAEjDa,KAAKH,UAAUgB,GAAYC,EAAWd,KAAKF,SAC7C,CACF,CAQO,OAAAmB,CAAQC,GACRlB,KAAKH,WAAWG,KAAKW,eAE1B,IADA,IAAIQ,EAAMnB,KAAKR,YACNT,EAAI,EAAGA,EAAImC,EAAME,OAAQrC,IAAK,CACrC,IAAIsC,EAAqB,IAAXH,EAAMnC,GAEhBiB,KAAKM,iBACPe,EAAU,EAAQzC,SAASyC,IAM7B,IAAIC,GAFJH,GAAOA,EAAOE,GAAYrB,KAAKX,OAAS,GAAOW,KAAKF,YAEjCE,KAAKb,MAAQ,EAAM,IAItCgC,IAFAA,EAAOA,GAAO,EAAKnB,KAAKF,WAEXE,KAAKH,UAAUyB,IAAQtB,KAAKF,SAC3C,CAKA,OAHIE,KAAKO,kBACPY,EAAM,EAAQjC,eAAeiC,EAAKnB,KAAKb,SAEjCgC,EAAMnB,KAAKP,cAAgBO,KAAKF,SAC1C,CAEO,aAAAyB,CAAcL,GACnB,IAAIrC,EAAMmB,KAAKiB,QAAQC,GACvB,GAAmB,IAAflB,KAAKb,MACP,OAAOqC,OAAOC,KAAK,CAAC5C,IACf,GAAmB,KAAfmB,KAAKb,MAAc,CAC5B,IAAIuC,EAAIF,OAAOG,MAAM,GAErB,OADAD,EAAEE,cAAc/C,EAAK,GACd6C,CACT,CAAO,GAAmB,KAAf1B,KAAKb,MAAc,CAC5B,IAAIuC,EAAIF,OAAOG,MAAM,GAErB,OADAD,EAAEG,cAAchD,EAAK,GACd6C,CACT,CACE,MAAM,IAAII,MAAM,qBAEpB,CAEA,SAAWC,GACT,OAAO/B,KAAKH,SACd,CAQO,cAAO,CAAQK,GACpB,OAAOd,EAAIqB,SAASuB,MAAMjE,GAAoBA,EAAEmC,OAASA,GAC3D,E", "sources": ["webpack://ecubuspro/webpack/bootstrap", "webpack://ecubuspro/webpack/runtime/define property getters", "webpack://ecubuspro/webpack/runtime/hasOwnProperty shorthand", "webpack://ecubuspro/webpack/runtime/make namespace object", "webpack://ecubuspro/./src/main/worker/crcUtil.ts", "webpack://ecubuspro/./src/main/worker/crc.ts"], "sourcesContent": ["// The require scope\nvar __webpack_require__ = {};\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "/* eslint-disable no-var */\r\nclass CrcUtil {\r\n  public static Reflect8(val: number) {\r\n    var resByte = 0\r\n\r\n    for (var i = 0; i < 8; i++) {\r\n      if ((val & (1 << i)) != 0) {\r\n        resByte |= (1 << (7 - i)) & 0xff\r\n      }\r\n    }\r\n    return resByte\r\n  }\r\n\r\n  public static Reflect16(val: number) {\r\n    var resByte = 0\r\n\r\n    for (var i = 0; i < 16; i++) {\r\n      if ((val & (1 << i)) != 0) {\r\n        resByte |= (1 << (15 - i)) & 0xffff\r\n      }\r\n    }\r\n\r\n    return resByte\r\n  }\r\n\r\n  public static Reflect32(val: number) {\r\n    var resByte = 0\r\n\r\n    for (var i = 0; i < 32; i++) {\r\n      if ((val & (1 << i)) != 0) {\r\n        resByte |= (1 << (31 - i)) & 0xffffffff\r\n      }\r\n    }\r\n\r\n    return resByte\r\n  }\r\n\r\n  public static ReflectGeneric(val: number, width: number) {\r\n    var resByte = 0\r\n    for (var i = 0; i < width; i++) {\r\n      if ((val & (1 << i)) != 0) {\r\n        resByte |= 1 << (width - 1 - i)\r\n      }\r\n    }\r\n    return resByte\r\n  }\r\n}\r\n\r\nexport default CrcUtil\r\n", "/* eslint-disable prefer-const */\r\n/* eslint-disable no-var */\r\nimport CrcUtil from './crcUtil'\r\n\r\n/**\r\n * @category Util\r\n */\r\nexport class CRC {\r\n  private _width!: number\r\n  private _name!: string\r\n  private _polynomial!: number\r\n  private _initialVal!: number\r\n  private _finalXorVal!: number\r\n  private _inputReflected!: boolean\r\n  private _resultReflected!: boolean\r\n  private static _list: CRC[]\r\n\r\n  private _crcTable!: number[]\r\n  private _castMask!: number\r\n  private _msbMask!: number\r\n\r\n  public get width(): number {\r\n    return this._width\r\n  }\r\n  public set width(v: number) {\r\n    this._width = v\r\n    switch (v) {\r\n      case 8:\r\n        this._castMask = 0xff\r\n        break\r\n      case 16:\r\n        this._castMask = 0xffff\r\n        break\r\n      case 32:\r\n        this._castMask = 0xffffffff\r\n        break\r\n      default:\r\n        throw 'Invalid CRC width'\r\n    }\r\n    this._msbMask = 0x01 << (v - 1)\r\n  }\r\n\r\n  public get name(): string {\r\n    return this._name\r\n  }\r\n  public set name(v: string) {\r\n    this._name = v\r\n  }\r\n\r\n  public get polynomial(): number {\r\n    return this._polynomial\r\n  }\r\n  public set polynomial(v: number) {\r\n    this._polynomial = v\r\n  }\r\n\r\n  public get initial(): number {\r\n    return this._initialVal\r\n  }\r\n  public set initial(v: number) {\r\n    this._initialVal = v\r\n  }\r\n\r\n  public get finalXor(): number {\r\n    return this._finalXorVal\r\n  }\r\n  public set finalXor(v: number) {\r\n    this._finalXorVal = v\r\n  }\r\n\r\n  public get inputReflected(): boolean {\r\n    return this._inputReflected\r\n  }\r\n  public set inputReflected(v: boolean) {\r\n    this._inputReflected = v\r\n  }\r\n\r\n  public get resultReflected(): boolean {\r\n    return this._resultReflected\r\n  }\r\n  public set resultReflected(v: boolean) {\r\n    this._resultReflected = v\r\n  }\r\n\r\n  /**\r\n   * Creates an instance of the CRC (Cyclic Redundancy Check) class.\r\n   *\r\n   * @param name - The name of the CRC algorithm.\r\n   * @param width - The width of the CRC in bits.\r\n   * @param polynomial - The polynomial used for the CRC calculation.\r\n   * @param initial - The initial value for the CRC calculation.\r\n   * @param finalXor - The value to XOR with the final CRC value.\r\n   * @param inputReflected - Whether the input bytes should be reflected.\r\n   * @param resultReflected - Whether the result should be reflected.\r\n   */\r\n  constructor(\r\n    name: string,\r\n    width: number,\r\n    polynomial: number,\r\n    initial: number,\r\n    finalXor: number,\r\n    inputReflected: boolean,\r\n    resultReflected: boolean\r\n  ) {\r\n    this.width = width\r\n    this.name = name\r\n    this.polynomial = polynomial\r\n    this.initial = initial\r\n    this.finalXor = finalXor\r\n    this.inputReflected = inputReflected\r\n    this.resultReflected = resultReflected\r\n  }\r\n\r\n  /**\r\n   * Returns a list of default CRC configurations.\r\n   *\r\n   * The list includes various CRC algorithms with their respective parameters:\r\n   * - Name: The name of the CRC algorithm.\r\n   * - Width: The width of the CRC (number of bits).\r\n   * - Polynomial: The polynomial used for the CRC calculation.\r\n   * - Initial Value: The initial value for the CRC calculation.\r\n   * - Final XOR Value: The value to XOR with the final CRC value.\r\n   * - Reflect Input: Whether to reflect the input bytes.\r\n   * - Reflect Output: Whether to reflect the output CRC value.\r\n   *\r\n   * @returns {CRC[]} An array of CRC configurations.\r\n   */\r\n  public static get defaults(): CRC[] {\r\n    if (!this._list) {\r\n      this._list = [\r\n        new CRC('CRC8', 8, 0x07, 0x00, 0x00, false, false),\r\n        new CRC('CRC8_SAE_J1850', 8, 0x1d, 0xff, 0xff, false, false),\r\n        new CRC('CRC8_SAE_J1850_ZERO', 8, 0x1d, 0x00, 0x00, false, false),\r\n        new CRC('CRC8_8H2F', 8, 0x2f, 0xff, 0xff, false, false),\r\n        new CRC('CRC8_CDMA2000', 8, 0x9b, 0xff, 0x00, false, false),\r\n        new CRC('CRC8_DARC', 8, 0x39, 0x00, 0x00, true, true),\r\n        new CRC('CRC8_DVB_S2', 8, 0xd5, 0x00, 0x00, false, false),\r\n        new CRC('CRC8_EBU', 8, 0x1d, 0xff, 0x00, true, true),\r\n        new CRC('CRC8_ICODE', 8, 0x1d, 0xfd, 0x00, false, false),\r\n        new CRC('CRC8_ITU', 8, 0x07, 0x00, 0x55, false, false),\r\n        new CRC('CRC8_MAXIM', 8, 0x31, 0x00, 0x00, true, true),\r\n        new CRC('CRC8_ROHC', 8, 0x07, 0xff, 0x00, true, true),\r\n        new CRC('CRC8_WCDMA', 8, 0x9b, 0x00, 0x00, true, true),\r\n        new CRC('CRC16_CCIT_ZERO', 16, 0x1021, 0x0000, 0x0000, false, false),\r\n        new CRC('CRC16_ARC', 16, 0x8005, 0x0000, 0x0000, true, true),\r\n        new CRC('CRC16_AUG_CCITT', 16, 0x1021, 0x1d0f, 0x0000, false, false),\r\n        new CRC('CRC16_BUYPASS', 16, 0x8005, 0x0000, 0x0000, false, false),\r\n        new CRC('CRC16_CCITT_FALSE', 16, 0x1021, 0xffff, 0x0000, false, false),\r\n        new CRC('CRC16_CDMA2000', 16, 0xc867, 0xffff, 0x0000, false, false),\r\n        new CRC('CRC16_DDS_110', 16, 0x8005, 0x800d, 0x0000, false, false),\r\n        new CRC('CRC16_DECT_R', 16, 0x0589, 0x0000, 0x0001, false, false),\r\n        new CRC('CRC16_DECT_X', 16, 0x0589, 0x0000, 0x0000, false, false),\r\n        new CRC('CRC16_DNP', 16, 0x3d65, 0x0000, 0xffff, true, true),\r\n        new CRC('CRC16_EN_13757', 16, 0x3d65, 0x0000, 0xffff, false, false),\r\n        new CRC('CRC16_GENIBUS', 16, 0x1021, 0xffff, 0xffff, false, false),\r\n        new CRC('CRC16_MAXIM', 16, 0x8005, 0x0000, 0xffff, true, true),\r\n        new CRC('CRC16_MCRF4XX', 16, 0x1021, 0xffff, 0x0000, true, true),\r\n        new CRC('CRC16_RIELLO', 16, 0x1021, 0xb2aa, 0x0000, true, true),\r\n        new CRC('CRC16_T10_DIF', 16, 0x8bb7, 0x0000, 0x0000, false, false),\r\n        new CRC('CRC16_TELEDISK', 16, 0xa097, 0x0000, 0x0000, false, false),\r\n        new CRC('CRC16_TMS37157', 16, 0x1021, 0x89ec, 0x0000, true, true),\r\n        new CRC('CRC16_USB', 16, 0x8005, 0xffff, 0xffff, true, true),\r\n        new CRC('CRC16_A', 16, 0x1021, 0xc6c6, 0x0000, true, true),\r\n        new CRC('CRC16_KERMIT', 16, 0x1021, 0x0000, 0x0000, true, true),\r\n        new CRC('CRC16_MODBUS', 16, 0x8005, 0xffff, 0x0000, true, true),\r\n        new CRC('CRC16_X_25', 16, 0x1021, 0xffff, 0xffff, true, true),\r\n        new CRC('CRC16_XMODEM', 16, 0x1021, 0x0000, 0x0000, false, false),\r\n        new CRC('CRC32', 32, 0x04c11db7, 0xffffffff, 0xffffffff, true, true),\r\n        new CRC('CRC32_BZIP2', 32, 0x04c11db7, 0xffffffff, 0xffffffff, false, false),\r\n        new CRC('CRC32_C', 32, 0x1edc6f41, 0xffffffff, 0xffffffff, true, true),\r\n        new CRC('CRC32_D', 32, 0xa833982b, 0xffffffff, 0xffffffff, true, true),\r\n        new CRC('CRC32_MPEG2', 32, 0x04c11db7, 0xffffffff, 0x00000000, false, false),\r\n        new CRC('CRC32_POSIX', 32, 0x04c11db7, 0x00000000, 0xffffffff, false, false),\r\n        new CRC('CRC32_Q', 32, 0x814141ab, 0x00000000, 0x00000000, false, false),\r\n        new CRC('CRC32_JAMCRC', 32, 0x04c11db7, 0xffffffff, 0x00000000, true, true),\r\n        new CRC('CRC32_XFER', 32, 0x000000af, 0x00000000, 0x00000000, false, false)\r\n      ]\r\n    }\r\n    return this._list\r\n  }\r\n\r\n  /**\r\n   * Generates the CRC table used for calculating the CRC checksum.\r\n   *\r\n   * This method initializes the `_crcTable` array with 256 entries, each representing\r\n   * a precomputed CRC value for a given byte. The table is generated based on the\r\n   * polynomial and width specified by the instance's `_polynomial` and `_width` properties.\r\n   *\r\n   * The algorithm iterates over each possible byte value (0-255) and calculates the\r\n   * corresponding CRC value by shifting and XORing with the polynomial. The result is\r\n   * stored in the `_crcTable` array.\r\n   *\r\n   * @remarks\r\n   * This method assumes that the instance has the following properties defined:\r\n   * - `_width`: The width of the CRC (number of bits).\r\n   * - `_castMask`: A mask used to cast the CRC value to the correct width.\r\n   * - `_msbMask`: A mask representing the most significant bit of the CRC.\r\n   * - `_polynomial`: The polynomial used for CRC calculation.\r\n   *\r\n   * @example\r\n   * ```typescript\r\n   * const crcInstance = new CrcClass();\r\n   * crcInstance._width = 32;\r\n   * crcInstance._castMask = 0xFFFFFFFF;\r\n   * crcInstance._msbMask = 0x80000000;\r\n   * crcInstance._polynomial = 0x04C11DB7;\r\n   * crcInstance.makeCrcTable();\r\n   * console.log(crcInstance._crcTable);\r\n   * ```\r\n   */\r\n  private makeCrcTable() {\r\n    this._crcTable = new Array(256)\r\n\r\n    for (var divident = 0; divident < 256; divident++) {\r\n      var currByte = (divident << (this._width - 8)) & this._castMask\r\n      for (var bit = 0; bit < 8; bit++) {\r\n        if ((currByte & this._msbMask) != 0) {\r\n          currByte <<= 1\r\n          currByte ^= this._polynomial\r\n        } else {\r\n          currByte <<= 1\r\n        }\r\n      }\r\n      this._crcTable[divident] = currByte & this._castMask\r\n    }\r\n  }\r\n\r\n  private makeCrcTableReversed() {\r\n    this._crcTable = new Array(256)\r\n\r\n    for (var divident = 0; divident < 256; divident++) {\r\n      var reflectedDivident = CrcUtil.Reflect8(divident)\r\n\r\n      var currByte = (reflectedDivident << (this._width - 8)) & this._castMask\r\n\r\n      for (var bit = 0; bit < 8; bit++) {\r\n        if ((currByte & this._msbMask) != 0) {\r\n          currByte <<= 1\r\n          currByte ^= this._polynomial\r\n        } else {\r\n          currByte <<= 1\r\n        }\r\n      }\r\n\r\n      currByte = CrcUtil.ReflectGeneric(currByte, this.width)\r\n\r\n      this._crcTable[divident] = currByte & this._castMask\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Computes the CRC (Cyclic Redundancy Check) value for the given input bytes.\r\n   *\r\n   * @param {number[] | Buffer} bytes - The input data as an array of numbers or a Buffer.\r\n   * @returns {number} - The computed CRC value.\r\n   */\r\n  public compute(bytes: number[] | Buffer) {\r\n    if (!this._crcTable) this.makeCrcTable()\r\n    var crc = this._initialVal\r\n    for (var i = 0; i < bytes.length; i++) {\r\n      var curByte = bytes[i] & 0xff\r\n\r\n      if (this.inputReflected) {\r\n        curByte = CrcUtil.Reflect8(curByte)\r\n      }\r\n\r\n      /* update the MSB of crc value with next input byte */\r\n      crc = (crc ^ (curByte << (this._width - 8))) & this._castMask\r\n      /* this MSB byte value is the index into the lookup table */\r\n      var pos = (crc >> (this.width - 8)) & 0xff\r\n      /* shift out this index */\r\n      crc = (crc << 8) & this._castMask\r\n      /* XOR-in remainder from lookup table using the calculated index */\r\n      crc = (crc ^ this._crcTable[pos]) & this._castMask\r\n    }\r\n\r\n    if (this.resultReflected) {\r\n      crc = CrcUtil.ReflectGeneric(crc, this.width)\r\n    }\r\n    return (crc ^ this._finalXorVal) & this._castMask\r\n  }\r\n\r\n  public computeBuffer(bytes: number[] | Buffer) {\r\n    let val = this.compute(bytes)\r\n    if (this.width === 8) {\r\n      return Buffer.from([val])\r\n    } else if (this.width === 16) {\r\n      let b = Buffer.alloc(2)\r\n      b.writeUInt16BE(val, 0)\r\n      return b\r\n    } else if (this.width === 32) {\r\n      let b = Buffer.alloc(4)\r\n      b.writeUInt32BE(val, 0)\r\n      return b\r\n    } else {\r\n      throw new Error('Unsupported length')\r\n    }\r\n  }\r\n\r\n  public get table() {\r\n    return this._crcTable\r\n  }\r\n\r\n  /**\r\n   * Retrieves a CRC object from the defaults list by its name.\r\n   *\r\n   * @param name - The name of the CRC object to find.\r\n   * @returns The CRC object with the specified name, or `undefined` if not found.\r\n   */\r\n  public static default(name: string): CRC | undefined {\r\n    return CRC.defaults.find((o: CRC): boolean => o.name === name)\r\n  }\r\n}\r\n"], "names": ["__webpack_require__", "exports", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "Symbol", "toStringTag", "value", "Reflect8", "val", "resByte", "i", "Reflect16", "Reflect32", "ReflectGeneric", "width", "CRC", "_width", "_name", "_polynomial", "_initialVal", "_finalXorVal", "_inputReflected", "_resultReflected", "static", "_crcTable", "_castMask", "_msbMask", "this", "v", "name", "polynomial", "initial", "finalXor", "inputReflected", "resultReflected", "constructor", "defaults", "_list", "makeCrcTable", "Array", "divident", "currByte", "bit", "makeCrcTableReversed", "compute", "bytes", "crc", "length", "curByte", "pos", "computeBuffer", "<PERSON><PERSON><PERSON>", "from", "b", "alloc", "writeUInt16BE", "writeUInt32BE", "Error", "table", "find"], "sourceRoot": ""}