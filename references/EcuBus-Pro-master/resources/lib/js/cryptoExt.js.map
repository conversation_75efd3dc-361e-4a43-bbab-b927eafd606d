{"version": 3, "file": "cryptoExt.js", "mappings": "mBACA,IAAIA,EAAsB,CCA1BA,EAAyBC,IACxB,IAAIC,EAASD,GAAUA,EAAOE,WAC7B,IAAOF,EAAiB,QACxB,IAAM,EAEP,OADAD,EAAoBI,EAAEF,EAAQ,CAAEG,EAAGH,IAC5BA,CAAM,ECLdF,EAAwB,CAACM,EAASC,KACjC,IAAI,IAAIC,KAAOD,EACXP,EAAoBS,EAAEF,EAAYC,KAASR,EAAoBS,EAAEH,EAASE,IAC5EE,OAAOC,eAAeL,EAASE,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,IAE1E,ECNDR,EAAwB,CAACc,EAAKC,IAAUL,OAAOM,UAAUC,eAAeC,KAAKJ,EAAKC,GCClFf,EAAyBM,IACH,oBAAXa,QAA0BA,OAAOC,aAC1CV,OAAOC,eAAeL,EAASa,OAAOC,YAAa,CAAEC,MAAO,WAE7DX,OAAOC,eAAeL,EAAS,aAAc,CAAEe,OAAO,GAAO,G,gCCL9D,MAAM,EAA+BC,QAAQ,U,aCE7C,MAAMC,EAAaC,OAAOC,KAAK,mCAAoC,OAC7DC,EAAWF,OAAOC,KAAK,mCAAoC,OAC3DE,EAAkB,GAExB,SAASC,EAAaC,GACpB,MAAMC,EAAUN,OAAOO,MAAMF,EAAOG,QAC9BC,EAAOJ,EAAOG,OAAS,EAC7B,IAAK,IAAIE,EAAQ,EAAGA,EAAQD,EAAMC,IAChCJ,EAAQI,GAASL,EAAOK,IAAU,EACV,IAApBL,EAAOK,EAAQ,KACjBJ,EAAQI,IAAU,GAItB,OADAJ,EAAQG,GAAQJ,EAAOI,IAAS,EACzBH,CACT,CACA,SAASK,EAAIC,EAAiBC,GAC5B,MAAML,EAASM,KAAKC,IAAIH,EAAQJ,OAAQK,EAAQL,QAC1CQ,EAAShB,OAAOO,MAAMC,GAC5B,IAAK,IAAIE,EAAQ,EAAGA,EAAQF,EAAQE,IAClCM,EAAON,GAASE,EAAQF,GAASG,EAAQH,GAE3C,OAAOM,CACT,CA2BA,SAASC,EAAIjC,EAAakC,GACxB,MAAMC,EAA4C,CAChD,GAAI,cACJ,GAAI,cACJ,GAAI,eAEN,IAAKA,EAAkBnC,EAAIwB,QACzB,MAAM,IAAIY,MAAM,iDAElB,MAAMC,EAAS,mBAAsBF,EAAkBnC,EAAIwB,QAASxB,EAAKe,GACnEuB,EAASD,EAAOE,OAAOL,GAE7B,OADAG,EAAOG,QACAF,CACT,CAce,SAASG,EAAKzC,EAAakC,GACxC,MAAMQ,EA5CR,SAAyB1C,GACvB,MAAM2C,EAAIV,EAAIjC,EAAKe,GAEnB,IAAI6B,EAAUxB,EAAauB,GAChB,IAAPA,EAAE,KACJC,EAAUjB,EAAIiB,EAAS1B,IAGzB,IAAI2B,EAAUzB,EAAawB,GAK3B,OAJiB,IAAbA,EAAQ,KACVC,EAAUlB,EAAIkB,EAAS3B,IAGlB,CAAE0B,QAASA,EAASC,QAASA,EACtC,CA8BkBC,CAAgB9C,GAChC,IACI+C,EAAuBC,EAAWC,EADlCC,EAAapB,KAAKqB,KAAKjB,EAAQV,OAASL,GAGzB,IAAf+B,GACFA,EAAa,EACbH,GAAwB,GAExBA,EAAwBb,EAAQV,OAASL,GAAoB,EAG/D8B,EAAiBC,EAAa,EAG5BF,EADED,EACUpB,EAAIyB,EAAgBlB,EAASe,GAAiBP,EAAQE,SAEtDjB,EA0BhB,SAA+BO,EAAiBmB,GAC9C,MAAMC,EAAQtC,OAAOO,MAAMJ,GACrBoC,EAAQF,EAAalC,EACrBqC,EAAMtB,EAAQV,OAMpB,OAJA8B,EAAMG,KAAK,GACXvB,EAAQwB,KAAKJ,EAAO,EAAGC,EAAOC,GAC9BF,EAAME,EAAMD,GAAS,IAEdD,CACT,CApCoBK,CAAsBzB,EAASe,GAAiBP,EAAQG,SAG1E,IACIe,EADAC,EAAI7C,OAAOC,KAAK,mCAAoC,OAGxD,IAAK,IAAIS,EAAQ,EAAGA,EAAQuB,EAAgBvB,IAC1CkC,EAAIjC,EAAIkC,EAAGT,EAAgBlB,EAASR,IAGpCmC,EAAI5B,EAAIjC,EAAK4D,GAGf,OADAA,EAAIjC,EAAIqB,EAAWa,GACZ5B,EAAIjC,EAAK4D,EAClB,CAEA,SAASR,EAAgBlB,EAAiBmB,GACxC,MAAMC,EAAQtC,OAAOO,MAAMJ,GACrBoC,EAAQF,EAAalC,EACrBqC,EAAMD,EAAQpC,EAIpB,OAFAe,EAAQwB,KAAKJ,EAAO,EAAGC,EAAOC,GAEvBF,CACT,C", "sources": ["webpack://ecubuspro/webpack/bootstrap", "webpack://ecubuspro/webpack/runtime/compat get default export", "webpack://ecubuspro/webpack/runtime/define property getters", "webpack://ecubuspro/webpack/runtime/hasOwnProperty shorthand", "webpack://ecubuspro/webpack/runtime/make namespace object", "webpack://ecubuspro/external node-commonjs \"crypto\"", "webpack://ecubuspro/./src/main/worker/cmac.ts"], "sourcesContent": ["// The require scope\nvar __webpack_require__ = {};\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "const __WEBPACK_NAMESPACE_OBJECT__ = require(\"crypto\");", "import crypto from 'crypto'\r\n\r\nconst const_Zero = Buffer.from('00000000000000000000000000000000', 'hex')\r\nconst const_Rb = Buffer.from('00000000000000000000000000000087', 'hex')\r\nconst const_blockSize = 16\r\n\r\nfunction bitShiftLeft(buffer: Buffer) {\r\n  const shifted = Buffer.alloc(buffer.length)\r\n  const last = buffer.length - 1\r\n  for (let index = 0; index < last; index++) {\r\n    shifted[index] = buffer[index] << 1\r\n    if (buffer[index + 1] & 0x80) {\r\n      shifted[index] += 0x01\r\n    }\r\n  }\r\n  shifted[last] = buffer[last] << 1\r\n  return shifted\r\n}\r\nfunction xor(bufferA: Buffer, bufferB: Buffer) {\r\n  const length = Math.min(bufferA.length, bufferB.length)\r\n  const output = Buffer.alloc(length)\r\n  for (let index = 0; index < length; index++) {\r\n    output[index] = bufferA[index] ^ bufferB[index]\r\n  }\r\n  return output\r\n}\r\nconst bitmasks = [0x80, 0x40, 0x20, 0x10, 0x08, 0x04, 0x02, 0x01]\r\nfunction toBinaryString(buffer: <PERSON>uffer) {\r\n  let binary = ''\r\n  for (let bufferIndex = 0; bufferIndex < buffer.length; bufferIndex++) {\r\n    for (let bitmaskIndex = 0; bitmaskIndex < bitmasks.length; bitmaskIndex++) {\r\n      binary += buffer[bufferIndex] & bitmasks[bitmaskIndex] ? '1' : '0'\r\n    }\r\n  }\r\n  return binary\r\n}\r\nfunction generateSubkeys(key: Buffer) {\r\n  const l = aes(key, const_Zero)\r\n\r\n  let subkey1 = bitShiftLeft(l)\r\n  if (l[0] & 0x80) {\r\n    subkey1 = xor(subkey1, const_Rb)\r\n  }\r\n\r\n  let subkey2 = bitShiftLeft(subkey1)\r\n  if (subkey1[0] & 0x80) {\r\n    subkey2 = xor(subkey2, const_Rb)\r\n  }\r\n\r\n  return { subkey1: subkey1, subkey2: subkey2 }\r\n}\r\n\r\nfunction aes(key: Buffer, message: Buffer): Buffer {\r\n  const keyLengthToCipher: Record<number, string> = {\r\n    16: 'aes-128-cbc',\r\n    24: 'aes-192-cbc',\r\n    32: 'aes-256-cbc'\r\n  }\r\n  if (!keyLengthToCipher[key.length]) {\r\n    throw new Error('Keys must be 128, 192, or 256 bits in length.')\r\n  }\r\n  const cipher = crypto.createCipheriv(keyLengthToCipher[key.length], key, const_Zero)\r\n  const result = cipher.update(message)\r\n  cipher.final()\r\n  return result\r\n}\r\n/**\r\n * CMAC algorithm, nodejs default crypto module does not support CMAC, so we need to implement it ourselves.\r\n * @category  Crypto Extend\r\n * @param key key must be 128, 192, or 256 bits in length.\r\n * @param message input message\r\n * @returns CMAC result\r\n * @example\r\n * const key=Buffer.from('2b7e151628aed2a6abf7158809cf4f3c','hex');\r\n * const message=Buffer.from('6bc1bee22e409f96e93d7e117393172a','hex');\r\n * const result=CMAC(key,message);\r\n * //which should print 070a16b46b4d4144f79bdd9dd04a287c\r\n * console.log(result.toString('hex'));\r\n */\r\nexport default function CMAC(key: Buffer, message: Buffer) {\r\n  const subkeys = generateSubkeys(key)\r\n  let blockCount = Math.ceil(message.length / const_blockSize)\r\n  let lastBlockCompleteFlag, lastBlock, lastBlockIndex\r\n\r\n  if (blockCount === 0) {\r\n    blockCount = 1\r\n    lastBlockCompleteFlag = false\r\n  } else {\r\n    lastBlockCompleteFlag = message.length % const_blockSize === 0\r\n  }\r\n  // eslint-disable-next-line prefer-const\r\n  lastBlockIndex = blockCount - 1\r\n\r\n  if (lastBlockCompleteFlag) {\r\n    lastBlock = xor(getMessageBlock(message, lastBlockIndex), subkeys.subkey1)\r\n  } else {\r\n    lastBlock = xor(getPaddedMessageBlock(message, lastBlockIndex), subkeys.subkey2)\r\n  }\r\n\r\n  let x = Buffer.from('00000000000000000000000000000000', 'hex')\r\n  let y: Buffer | undefined\r\n\r\n  for (let index = 0; index < lastBlockIndex; index++) {\r\n    y = xor(x, getMessageBlock(message, index))\r\n    // eslint-disable-next-line @typescript-eslint/ban-ts-comment\r\n    //@ts-ignore\r\n    x = aes(key, y)\r\n  }\r\n  y = xor(lastBlock, x)\r\n  return aes(key, y)\r\n}\r\n\r\nfunction getMessageBlock(message: Buffer, blockIndex: number) {\r\n  const block = Buffer.alloc(const_blockSize)\r\n  const start = blockIndex * const_blockSize\r\n  const end = start + const_blockSize\r\n\r\n  message.copy(block, 0, start, end)\r\n\r\n  return block\r\n}\r\n\r\nfunction getPaddedMessageBlock(message: Buffer, blockIndex: number) {\r\n  const block = Buffer.alloc(const_blockSize)\r\n  const start = blockIndex * const_blockSize\r\n  const end = message.length\r\n\r\n  block.fill(0)\r\n  message.copy(block, 0, start, end)\r\n  block[end - start] = 0x80\r\n\r\n  return block\r\n}\r\n"], "names": ["__webpack_require__", "module", "getter", "__esModule", "d", "a", "exports", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "Symbol", "toStringTag", "value", "require", "const_Zero", "<PERSON><PERSON><PERSON>", "from", "const_Rb", "const_blockSize", "bitShiftLeft", "buffer", "shifted", "alloc", "length", "last", "index", "xor", "bufferA", "bufferB", "Math", "min", "output", "aes", "message", "keyLengthToCipher", "Error", "cipher", "result", "update", "final", "CMAC", "subkeys", "l", "subkey1", "subkey2", "generateSubkeys", "lastBlockCompleteFlag", "lastBlock", "lastBlockIndex", "blockCount", "ceil", "getMessageBlock", "blockIndex", "block", "start", "end", "fill", "copy", "getPaddedMessageBlock", "y", "x"], "sourceRoot": ""}