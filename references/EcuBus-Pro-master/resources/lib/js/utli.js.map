{"version": 3, "file": "utli.js", "mappings": "mBACA,IAAIA,EAAsB,CCA1BA,EAAwB,CAACC,EAASC,KACjC,IAAI,IAAIC,KAAOD,EACXF,EAAoBI,EAAEF,EAAYC,KAASH,EAAoBI,EAAEH,EAASE,IAC5EE,OAAOC,eAAeL,EAASE,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,IAE1E,ECNDH,EAAwB,CAACS,EAAKC,IAAUL,OAAOM,UAAUC,eAAeC,KAAKJ,EAAKC,GCClFV,EAAyBC,IACH,oBAAXa,QAA0BA,OAAOC,aAC1CV,OAAOC,eAAeL,EAASa,OAAOC,YAAa,CAAEC,MAAO,WAE7DX,OAAOC,eAAeL,EAAS,aAAc,CAAEe,OAAO,GAAO,G,wCCY9D,MAAMC,EAAgB,qDAItB,SAASC,EAASC,GAChB,OAA+D,KAAvDA,EAAMC,QAAO,CAACC,EAAaC,IAAcD,EAAMC,GAAG,EAC5D,CAWA,SAASC,EAAOC,GACd,OAAOA,EAAOC,SAAS,IAAIC,cAAcC,SAAS,EAAG,IACvD,CAGAC,OAAOC,UACLD,OAAOC,WACP,SAAUb,GACR,MAAwB,iBAAVA,GAAsBc,SAASd,IAAUe,KAAKC,MAAMhB,KAAWA,CAC/E,EAcK,MAAMiB,EACHC,QAQR,WAAAC,CAAYC,GAGV,GAFAC,KAAKH,QAAU,IAAII,IAEfF,GAAUtB,OAAOyB,YAAYH,EAC/B,IAAK,MAAMI,KAASJ,EAAsC,CACxD,IAAKK,MAAMC,QAAQF,IAA2B,IAAjBA,EAAMG,OACjC,MAAM,IAAIC,MACR,iGAGJP,KAAKQ,IAAIL,EAAM,GAAIA,EAAM,GAC3B,MACK,GAAsB,iBAAXJ,GAEhB,GAAIA,EAAQ,CACV,MAAMU,EAAQzC,OAAO0C,KAAKX,GAC1B,IAAK,MAAMY,KAAQF,EACjBT,KAAKQ,IAAII,SAASD,GAAOZ,EAAOY,GAEpC,OACK,GAAIZ,QACT,MAAM,IAAIQ,MACR,gGAGN,CAEA,GAAAC,CAAIG,EAAchC,GAChB,IAAKY,OAAOC,UAAUmB,GACpB,MAAM,IAAIJ,MAAM,oDAElB,GAAII,EAAO,EACT,MAAM,IAAIJ,MAAM,8CAElB,KAAM5B,aAAiBkC,QACrB,MAAM,IAAIN,MAAM,kDAElB,OAAOP,KAAKH,QAAQW,IAAIG,EAAMhC,EAChC,CAEA,GAAAR,CAAIwC,GACF,OAAOX,KAAKH,QAAQ1B,IAAIwC,EAC1B,CACA,KAAAG,GACE,OAAOd,KAAKH,QAAQiB,OACtB,CACA,OAAOH,GACL,OAAOX,KAAKH,QAAQkB,OAAOJ,EAC7B,CACA,OAAAK,GACE,OAAOhB,KAAKH,QAAQmB,SACtB,CACA,OAAAC,CACEC,EACAC,GAEA,OAAOnB,KAAKH,QAAQoB,QAAQC,EAAUC,EACxC,CACA,GAAAC,CAAIT,GACF,OAAOX,KAAKH,QAAQuB,IAAIT,EAC1B,CACA,IAAAD,GACE,OAAOV,KAAKH,QAAQa,MACtB,CACA,MAAAW,GACE,OAAOrB,KAAKH,QAAQwB,QACtB,CACA,QAAIC,GACF,OAAOtB,KAAKH,QAAQyB,IACtB,CACA,CAAC7C,OAAOyB,YACN,OAAOF,KAAKH,QAAQpB,OAAOyB,WAC7B,CAmCA,cAAOqB,CAAQC,EAAiBC,EAAeC,KAC7C,MAAM3B,EAAS,IAAIH,EAEnB,IACI+B,EADAC,EAAsB,EAEtBC,EAAc,EAMdC,EAAO,EAIX,IAFAlD,EAAcmD,UAAY,EAE6B,QAA/CJ,EAAc/C,EAAcoD,KAAKR,KAAoB,CAK3D,GAJAK,IAIID,IAAwBD,EAAYM,MACtC,MAAM,IAAI1B,MACR,0DACEqB,EACA,QACAD,EAAYM,MACZ,MACAT,EACGU,UAAUN,EAAqBlC,KAAKyC,IAAIR,EAAYM,MAAOL,EAAsB,KACjFQ,OACH,MAGNR,EAAsBhD,EAAcmD,UAGpC,MAAO,CAAEM,EAAWC,GAAkBX,EAGhCY,EAAc1B,OAAO2B,KACzBH,EAAUI,MAAM,iBAAiBC,KAAKC,GAAM/B,SAAS+B,EAAG,OAAQ,IAG5DC,EAAeL,EAAY,GACjC,GAAIK,EAAe,IAAML,EAAYjC,OACnC,MAAM,IAAIC,MACR,sCACEsB,EACA,KACAF,EAAY,GAAGS,OACf,eACAQ,EACA,qCACCL,EAAYjC,OAAS,IAI5B,MAAMuC,EAAKhE,EAAS0D,GACpB,GAAI3B,SAAS0B,EAAgB,MAAQO,EACnC,MAAM,IAAItC,MACR,6BACEsB,EACA,KACAF,EAAY,GAAGS,OACf,gBACAS,EAAGzD,SAAS,KAIlB,MAAM0D,GAAUP,EAAY,IAAM,GAAKA,EAAY,GAC7CQ,EAAaR,EAAY,GACzBS,EAAOT,EAAYU,SAAS,GAElC,GAAmB,IAAfF,EAAkB,CAGpB,GAAIhD,EAAOqB,IAAIU,EAAOgB,GACpB,MAAM,IAAIvC,MACR,6BAA+BsB,EAAc,KAAOF,EAAY,GAAGS,OAAS,KAGhF,GAAIU,EAASE,EAAK1C,OAAS,MACzB,MAAM,IAAIC,MACR,kBACEsB,EACA,KACAF,EAAY,GAAGS,OACf,iLAINrC,EAAOS,IAAIsB,EAAOgB,EAAQE,EAC5B,KAAO,CAEL,GAAe,IAAXF,EACF,MAAM,IAAIvC,MACR,UACEsB,EACA,KACAF,EAAY,GAAGS,OACf,oCAIN,OAAQW,GACN,KAAK,EACH,GAAInB,IAAwBJ,EAAQlB,OAElC,MAAM,IAAIC,MAAM,+CAAiDsB,GAGnE,OAAO9B,EAAOmD,KAAKzB,GAErB,KAAK,EAGHK,GAASkB,EAAK,IAAM,GAAKA,EAAK,IAAO,EACrC,MAEF,KAAK,EAWL,KAAK,EAMH,MAZF,KAAK,EAGHlB,GAASkB,EAAK,IAAM,GAAKA,EAAK,IAAO,GACrC,MASF,QACE,MAAM,IAAIzC,MACR,yBACErB,EAAO6D,GACP,cACAlB,EACA,sCAGV,CACF,CAEA,MAAIA,EACI,IAAItB,MAAM,gCAEV,IAAIA,MAAM,qDAEpB,CAuBA,IAAA2C,CAAKzB,EAAeC,KAElB,MAAMyB,EAAa/C,MAAMoC,KAAKxC,KAAKU,QAAQ0C,MAAK,CAACC,EAAGC,IAAMD,EAAIC,IACxDC,EAAa,IAAItD,IACvB,IAAIuD,GAAiB,EACjBC,GAAoB,EAExB,IAAK,IAAIC,EAAI,EAAGC,EAAIR,EAAW7C,OAAQoD,EAAIC,EAAGD,IAAK,CACjD,MAAME,EAAYT,EAAWO,GACvBG,EAAc7D,KAAK7B,IAAIgF,EAAWO,IAAKpD,OAE7C,GAAImD,IAAqBG,GAAaH,EAAmBD,EAAgB/B,EAGvE8B,EAAW/C,IAAIgD,EAAeD,EAAWpF,IAAIqF,GAAiBK,GAC9DJ,GAAoBI,MACf,MAAIJ,GAAoBG,GAM7B,MAAM,IAAIrD,MAAM,qCAAuCqD,EAAUxE,SAAS,KAJ1EmE,EAAW/C,IAAIoD,EAAWC,GAC1BL,EAAgBI,EAChBH,EAAmBG,EAAYC,CAGjC,CACF,CAGA,MAAMC,EAAe,IAAIlE,EACzB,IAAImE,EACAC,GAAoB,EACxB,IAAK,IAAIN,EAAI,EAAGC,EAAIR,EAAW7C,OAAQoD,EAAIC,EAAGD,IAAK,CACjD,MAAME,EAAYT,EAAWO,GACzBH,EAAWnC,IAAIwC,KACjBG,EAAelD,OAAOoD,MAAMV,EAAWpF,IAAIyF,IAC3CE,EAAatD,IAAIoD,EAAWG,GAC5BC,EAAmBJ,GAErBG,EAAcvD,IAAIR,KAAK7B,IAAIyF,GAAaA,EAAYI,EACtD,CAEA,OAAOF,CACT,CAuDA,2BAAOI,CAAqBC,GAE1B,MAAMC,EAAO,IAAIC,IACjB,IAAK,MAAO,CAAEtE,KAAWoE,EACvB,IAAK,MAAOG,EAASC,KAAUxE,EAC7BqE,EAAKI,IAAIF,GACTF,EAAKI,IAAIF,EAAUC,EAAMjE,QAI7B,MAAMmE,EAAcrE,MAAMoC,KAAK4B,EAAK/C,UAAU+B,MAAK,CAACC,EAAGC,IAAMD,EAAIC,IAC3DoB,EAAW,IAAIzE,IAGrB,IAAK,IAAIyD,EAAI,EAAGC,EAAIc,EAAYnE,OAAS,EAAGoD,EAAIC,EAAGD,IAAK,CACtD,MAAMiB,EAAMF,EAAYf,GAClBkB,EAAUH,EAAYf,EAAI,GAC1BmB,EAAS,GAEf,IAAK,MAAOC,EAAO/E,KAAWoE,EAAe,CAG3C,MAAMP,EAAYxD,MAAMoC,KAAKzC,EAAOW,QAAQ3B,QAAO,CAACgG,EAAKC,IACnDA,EAAML,EACDI,EAEFrF,KAAKuF,IAAIF,EAAKC,KACnB,GAEJ,IAAmB,IAAfpB,EAAkB,CACpB,MAAMW,EAAQxE,EAAO5B,IAAIyF,GACnBsB,EAAgBP,EAAMf,EACtBuB,EAAcP,EAAUhB,EAE1BsB,EAAgBX,EAAMjE,QACxBuE,EAAOO,KAAK,CAACN,EAAOP,EAAMtB,SAASiC,EAAeC,IAEtD,CACF,CAEIN,EAAOvE,QACToE,EAASlE,IAAImE,EAAKE,EAEtB,CAEA,OAAOH,CACT,CAkBA,sBAAOW,CAAgBX,GACrB,OAAO,IAAI9E,EACTQ,MAAMoC,KAAKkC,EAAS1D,WAAW0B,KAAI,EAAE4B,EAASO,KACrC,CAACP,EAASO,EAAOA,EAAOvE,OAAS,GAAG,MAGjD,CA2BA,QAAAgF,CAASC,EAAW,KAAMC,EAAM,KAC9B,GAAID,GAAY,EACd,MAAM,IAAIhF,MAAM,uCAElB,MAAMkF,EAAW,IAAI7F,EACrB,IAAI8F,EAEJ,MAAMvC,EAAa/C,MAAMoC,KAAKxC,KAAKU,QAAQ0C,MAAK,CAACC,EAAGC,IAAMD,EAAIC,IAE9D,IAAK,IAAII,EAAI,EAAGC,EAAIR,EAAW7C,OAAQoD,EAAIC,EAAGD,IAAK,CACjD,MAAME,EAAYT,EAAWO,GACvBa,EAAQvE,KAAK7B,IAAIyF,GACjBC,EAAcU,EAAMjE,OACpBqF,EAAW/B,EAAYC,EAE7B,IACE,IAAI+B,EAAWhC,EAAaA,EAAY2B,EACxCK,EAAWD,EACXC,GAAYL,EACZ,CACAG,EAAOD,EAAStH,IAAIyH,GACfF,IACHA,EAAO7E,OAAOoD,MAAMsB,GACpBG,EAAKG,KAAKL,GACVC,EAASjF,IAAIoF,EAAUF,IAGzB,MAAM5C,EAAS8C,EAAWhC,EAC1B,IAAIkC,EACAhD,GAAU,GAEZgD,EAAWvB,EAAMtB,SAAS,EAAGvD,KAAKyC,IAAIoD,EAAWzC,EAAQe,IACzD6B,EAAKlF,IAAIsF,GAAWhD,KAGpBgD,EAAWvB,EAAMtB,SAASH,EAAQA,EAASpD,KAAKyC,IAAIoD,EAAU1B,EAAcf,IAC5E4C,EAAKlF,IAAIsF,EAAU,GAEvB,CACF,CAEA,OAAOL,CACT,CAiBA,SAAAM,CAAUjD,EAAgBkD,GACxB,MAAMtF,EAAON,MAAMoC,KAAKxC,KAAKU,QAE7B,IAAK,IAAIgD,EAAI,EAAGC,EAAIjD,EAAKJ,OAAQoD,EAAIC,EAAGD,IAAK,CAC3C,MAAME,EAAYlD,EAAKgD,GACjBa,EAAQvE,KAAK7B,IAAIyF,GACjBC,EAAcU,EAAMjE,OAG1B,GAAIsD,GAAad,GAAUA,EAAS,GAFnBc,EAAYC,EAG3B,OAAO,IAAIoC,SAAS1B,EAAM2B,OAAQpD,EAASc,EAAW,GAAGmC,UAAU,EAAGC,EAE1E,CAEF,CAsBA,WAAAG,CAAYC,EAAW,IACrB,IAAIC,EAAa,EACbC,GAAc,MAClB,MAAMC,EAAU,GAChB,GAAIH,GAAY,EACd,MAAM,IAAI7F,MAAM,4CACX,GAAI6F,EAAW,IACpB,MAAM,IAAI7F,MAAM,wCAIlB,MAAMiG,EAAe3F,OAAOoD,MAAM,GAC5BwC,EAAe5F,OAAOoD,MAAM,GAE5Bd,EAAa/C,MAAMoC,KAAKxC,KAAKU,QAAQ0C,MAAK,CAACC,EAAGC,IAAMD,EAAIC,IAC9D,IAAK,IAAII,EAAI,EAAGC,EAAIR,EAAW7C,OAAQoD,EAAIC,EAAGD,IAAK,CACjD,MAAME,EAAYT,EAAWO,GACvBa,EAAQvE,KAAK7B,IAAIyF,GAGvB,KAAMW,aAAiB1D,QACrB,MAAM,IAAIN,MAAM,mBAAqBqD,EAAY,qBAEnD,GAAIA,EAAY,EACd,MAAM,IAAIrD,MAAM,mBAAqBqD,EAAY,wCAEnD,MAAM8C,EAAYnC,EAAMjE,OACxB,IAAKoG,EACH,SAyBF,GAtBI9C,EAAY0C,EAAc,QAK5BA,EAAc1C,EAAaA,EAAY,MACvCyC,EAAa,EAEbG,EAAa,GAAK,EAClBA,EAAa,GAAK,EAClBA,EAAa,GAAK,EAClBA,EAAa,GAAK,EAClBA,EAAa,GAAKF,GAAe,GACjCE,EAAa,GAAKF,GAAe,GAEjCC,EAAQnB,KACN,IACEhF,MAAM9B,UAAUoE,IAAIlE,KAAKgI,EAActH,GAAQgE,KAAK,IACpDhE,EAAOL,EAAS2H,MAIlB5C,EAAY0C,EAAcD,EAC5B,MAAM,IAAI9F,MACR,uBAAyBqD,EAAUxE,SAAS,IAAM,oCAItDiH,EAAazC,EAAY,MACzB,IAAI+C,EAAc,EAClB,MAAMhB,EAAW/B,EAAY8C,EAC7B,GAAIf,EAAW,WACb,MAAM,IAAIpF,MAAM,kCAIlB,KAAO+F,EAAcD,EAAaV,GAAU,CACtCU,EAAa,QAEfC,GAAe,MACfD,EAAa,EAEbG,EAAa,GAAK,EAClBA,EAAa,GAAK,EAClBA,EAAa,GAAK,EAClBA,EAAa,GAAK,EAClBA,EAAa,GAAKF,GAAe,GACjCE,EAAa,GAAKF,GAAe,GAEjCC,EAAQnB,KACN,IACEhF,MAAM9B,UAAUoE,IAAIlE,KAAKgI,EAActH,GAAQgE,KAAK,IACpDhE,EAAOL,EAAS2H,MAItB,IAAII,GAAc,EAElB,KAAOP,EAAa,OAAWO,GAO7B,GANAA,EAAalH,KAAKyC,IAChBiE,EACAT,EAAWW,EAAcD,EACzB,MAAUA,GAGRO,EAAY,CACdH,EAAa,GAAKG,EAClBH,EAAa,GAAKJ,GAAc,EAChCI,EAAa,GAAKJ,EAClBI,EAAa,GAAK,EAElB,MAAMX,EAAWvB,EAAMtB,SAAS0D,EAAaA,EAAcC,GAE3DL,EAAQnB,KACN,IACEhF,MAAM9B,UAAUoE,IAAIlE,KAAKiI,EAAcvH,GAAQgE,KAAK,IACpD9C,MAAM9B,UAAUoE,IAAIlE,KAAKsH,EAAU5G,GAAQgE,KAAK,IAChDhE,GA/sBqB2H,EA+sBYf,EA5sBf,MA4sBCW,EA9sBT1H,QAAO,CAACC,EAAaC,IAAcD,EAAMC,GAAG,GACnD4H,EAAO9H,QAAO,CAACC,EAAaC,IAAcD,EAAMC,GAAG,OAgtB1D0H,GAAeC,EACfP,GAAcO,CAChB,CAEJ,CACF,CAvtBJ,IAAqCC,EA2tBjC,OAFAN,EAAQnB,KAAK,eAENmB,EAAQrD,KAAK,KACtB,CASA,KAAA4D,GACE,MAAMC,EAAS,IAAInH,EAEnB,IAAK,MAAOe,EAAMhC,KAAUqB,KAC1B+G,EAAOvG,IAAIG,EAAME,OAAO2B,KAAK7D,IAG/B,OAAOoI,CACT,CA0BA,uBAAOC,CAAiBlI,EAAemI,EAAU,IAAMC,EAAe,IACpE,KAAMpI,aAAiB+B,QACrB,MAAM,IAAIN,MAAM,sDAWlB,MAAM4G,EAAS,IAAIvH,EACnB,IAAIwH,EAAkB,EAClBC,GAAc,EACdC,EAAc,EACdC,GAAgB,EACpB,MAAM5D,EAAI7E,EAAMwB,OAEhB,IAAK,IAAIK,EAAO,EAAGA,EAAOgD,EAAGhD,IACd7B,EAAM6B,KAENsG,GACXG,IACIA,GAAmBF,KAGD,IAAhBG,GAEFF,EAAO3G,IAAI8G,EAAaxI,EAAMmE,SAASqE,EAAaD,EAAa,IAGnEE,GAAgB,KAGdA,IACFA,GAAgB,EAChBD,EAAc3G,GAEhB0G,EAAa1G,EACbyG,EAAkB,GAStB,OAJKG,IAAiC,IAAhBF,GACpBF,EAAO3G,IAAI8G,EAAaxI,EAAMmE,SAASqE,EAAa3D,IAG/CwD,CACT,CAkBA,KAAAK,CAAMlD,EAAiBhE,EAASoB,KAC9B,GAAIpB,EAAS,EACX,MAAM,IAAIC,MAAM,0CAGlB,MAAMkH,EAAS,IAAI7H,EAEnB,IAAK,MAAOgE,EAAWW,KAAUvE,KAAM,CACrC,MAAM6D,EAAcU,EAAMjE,OAE1B,GAAIsD,EAAYC,GAAeS,GAAWV,EAAYU,EAAUhE,EAAQ,CACtE,MAAMoH,EAAahI,KAAKuF,IAAIX,EAASV,GAE/B+D,EADWjI,KAAKyC,IAAImC,EAAUhE,EAAQsD,EAAYC,GACzB6D,EACzBE,EAAqBF,EAAa9D,EAEpC+D,EAAc,GAChBF,EAAOjH,IACLkH,EACAnD,EAAMtB,SAAS2E,EAAoBA,EAAqBD,GAG9D,CACF,CACA,OAAOF,CACT,CAeA,QAAAI,CAASvD,EAAiBhE,EAAgB2G,EAAU,KAClD,GAAI3G,EAAS,EACX,MAAM,IAAIC,MAAM,0CAGlB,MAAMuH,EAAMjH,OAAOoD,MAAM3D,EAAQ2G,GAEjC,IAAK,MAAOrD,EAAWW,KAAUvE,KAAM,CACrC,MAAM6D,EAAcU,EAAMjE,OAE1B,GAAIsD,EAAYC,GAAeS,GAAWV,EAAYU,EAAUhE,EAAQ,CACtE,MAAMoH,EAAahI,KAAKuF,IAAIX,EAASV,GAE/B+D,EADWjI,KAAKyC,IAAImC,EAAUhE,EAAQsD,EAAYC,GACzB6D,EACzBE,EAAqBF,EAAa9D,EAEpC+D,EAAc,GAChBG,EAAItH,IACF+D,EAAMtB,SAAS2E,EAAoBA,EAAqBD,GACxDD,EAAapD,EAGnB,CACF,CACA,OAAOwD,CACT,CAoBA,QAAAC,CAASZ,GACP,IAAK,MAAOvD,EAAWW,KAAU4C,EAAQ,CACvC,MAAMtD,EAAcU,EAAMjE,OAEpBkH,EAAQxH,KAAKwH,MAAM5D,EAAWC,GAAaX,OAAO/E,IAAIyF,GAE5D,IAAK4D,GAASA,EAAMlH,SAAWuD,EAC7B,OAAO,EAGT,IAAK,MAAMH,KAAKa,EACd,GAAIA,EAAMb,KAAO8D,EAAM9D,GACrB,OAAO,CAGb,CACA,OAAO,CACT,E", "sources": ["webpack://ecubuspro/webpack/bootstrap", "webpack://ecubuspro/webpack/runtime/define property getters", "webpack://ecubuspro/webpack/runtime/hasOwnProperty shorthand", "webpack://ecubuspro/webpack/runtime/make namespace object", "webpack://ecubuspro/./src/main/worker/utli.ts"], "sourcesContent": ["// The require scope\nvar __webpack_require__ = {};\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "/**\r\n * Parser/writer for the \"Intel hex\" format.\r\n */\r\n\r\n/*\r\n * A regexp that matches lines in a .hex file.\r\n *\r\n * One hexadecimal character is matched by \"[0-9A-Fa-f]\".\r\n * Two hex characters are matched by \"[0-9A-Fa-f]{2}\"\r\n * Eight or more hex characters are matched by \"[0-9A-Fa-f]{8,}\"\r\n * A capture group of two hex characters is \"([0-9A-Fa-f]{2})\"\r\n *\r\n * Record mark         :\r\n * 8 or more hex chars  ([0-9A-Fa-f]{8,})\r\n * Checksum                              ([0-9A-Fa-f]{2})\r\n * Optional newline                                      (?:\\r\\n|\\r|\\n|)\r\n */\r\nconst hexLineRegexp = /:([0-9A-Fa-f]{8,})([0-9A-Fa-f]{2})(?:\\r\\n|\\r|\\n|)/g\r\n\r\n// Takes a Buffer as input,\r\n// Returns an integer in the 0-255 range.\r\nfunction checksum(bytes: Buffer): number {\r\n  return -bytes.reduce((sum: number, v: number) => sum + v, 0) & 0xff\r\n}\r\n\r\n// Takes two Buffers as input,\r\n// Returns an integer in the 0-255 range.\r\nfunction checksumTwo(array1: Buffer, array2: Buffer): number {\r\n  const partial1 = array1.reduce((sum: number, v: number) => sum + v, 0)\r\n  const partial2 = array2.reduce((sum: number, v: number) => sum + v, 0)\r\n  return -(partial1 + partial2) & 0xff\r\n}\r\n\r\n// Trivial utility. Converts a number to hex and pads with zeroes up to 2 characters.\r\nfunction hexpad(number: number): string {\r\n  return number.toString(16).toUpperCase().padStart(2, '0')\r\n}\r\n\r\n// Polyfill as per https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Number/isInteger\r\nNumber.isInteger =\r\n  Number.isInteger ||\r\n  function (value) {\r\n    return typeof value === 'number' && isFinite(value) && Math.floor(value) === value\r\n  }\r\n\r\n/**\r\n * @category Util\r\n * @example\r\n * import {HexMemoryMap} from 'ECB';\r\n *\r\n * let memMap1 = new HexMemoryMap();\r\n * let memMap2 = new HexMemoryMap([[0, new Buffer(1,2,3,4)]]);\r\n * let memMap3 = new HexMemoryMap({0: new Buffer(1,2,3,4)});\r\n * let memMap4 = new HexMemoryMap({0xCF0: new Buffer(1,2,3,4)});\r\n *\r\n * const block = HexMemoryMap.fromHex(hexText);\r\n */\r\nexport class HexMemoryMap {\r\n  private _blocks: Map<number, Buffer>\r\n\r\n  /**\r\n   * @param {Iterable} blocks The initial value for the memory blocks inside this\r\n   * <tt>HexMemoryMap</tt>. All keys must be numeric, and all values must be instances of\r\n   * <tt>Buffer</tt>. Optionally it can also be a plain <tt>Object</tt> with\r\n   * only numeric keys.\r\n   */\r\n  constructor(blocks?: Iterable<[number, Buffer]> | { [key: string]: Buffer } | null) {\r\n    this._blocks = new Map()\r\n\r\n    if (blocks && Symbol.iterator in blocks) {\r\n      for (const tuple of blocks as Iterable<[number, Buffer]>) {\r\n        if (!Array.isArray(tuple) || tuple.length !== 2) {\r\n          throw new Error(\r\n            'First parameter to HexMemoryMap constructor must be an iterable of [addr, bytes] or undefined'\r\n          )\r\n        }\r\n        this.set(tuple[0], tuple[1])\r\n      }\r\n    } else if (typeof blocks === 'object') {\r\n      // Try iterating through the object's keys\r\n      if (blocks) {\r\n        const addrs = Object.keys(blocks)\r\n        for (const addr of addrs) {\r\n          this.set(parseInt(addr), blocks[addr])\r\n        }\r\n      }\r\n    } else if (blocks !== undefined && blocks !== null) {\r\n      throw new Error(\r\n        'First parameter to HexMemoryMap constructor must be an iterable of [addr, bytes] or undefined'\r\n      )\r\n    }\r\n  }\r\n\r\n  set(addr: number, value: Buffer): Map<number, Buffer> {\r\n    if (!Number.isInteger(addr)) {\r\n      throw new Error('Address passed to HexMemoryMap is not an integer')\r\n    }\r\n    if (addr < 0) {\r\n      throw new Error('Address passed to HexMemoryMap is negative')\r\n    }\r\n    if (!(value instanceof Buffer)) {\r\n      throw new Error('Bytes passed to HexMemoryMap are not an Buffer')\r\n    }\r\n    return this._blocks.set(addr, value)\r\n  }\r\n  // Delegate the following to the 'this._blocks' Map:\r\n  get(addr: number): Buffer | undefined {\r\n    return this._blocks.get(addr)\r\n  }\r\n  clear(): void {\r\n    return this._blocks.clear()\r\n  }\r\n  delete(addr: number): boolean {\r\n    return this._blocks.delete(addr)\r\n  }\r\n  entries(): IterableIterator<[number, Buffer]> {\r\n    return this._blocks.entries()\r\n  }\r\n  forEach(\r\n    callback: (value: Buffer, key: number, map: Map<number, Buffer>) => void,\r\n    thisArg?: any\r\n  ): void {\r\n    return this._blocks.forEach(callback, thisArg)\r\n  }\r\n  has(addr: number): boolean {\r\n    return this._blocks.has(addr)\r\n  }\r\n  keys(): IterableIterator<number> {\r\n    return this._blocks.keys()\r\n  }\r\n  values(): IterableIterator<Buffer> {\r\n    return this._blocks.values()\r\n  }\r\n  get size(): number {\r\n    return this._blocks.size\r\n  }\r\n  [Symbol.iterator](): IterableIterator<[number, Buffer]> {\r\n    return this._blocks[Symbol.iterator]()\r\n  }\r\n\r\n  /**\r\n   * Parses a string containing data formatted in \"Intel HEX\" format, and\r\n   * returns an instance of {@linkcode HexMemoryMap}.\r\n   *<br/>\r\n   * The insertion order of keys in the {@linkcode HexMemoryMap} is guaranteed to be strictly\r\n   * ascending. In other words, when iterating through the {@linkcode HexMemoryMap}, the addresses\r\n   * will be ordered in ascending order.\r\n   *<br/>\r\n   * The parser has an opinionated behaviour, and will throw a descriptive error if it\r\n   * encounters some malformed input. Check the project's\r\n   * {@link https://github.com/NordicSemiconductor/ECB#Features|README file} for details.\r\n   *<br/>\r\n   * If <tt>maxBlockSize</tt> is given, any contiguous data block larger than that will\r\n   * be split in several blocks.\r\n   *\r\n   * @param {String} hexText The contents of a .hex file.\r\n   * @param {Number} [maxBlockSize=Infinity] Maximum size of the returned <tt>Buffer</tt>s.\r\n   *\r\n   * @return {HexMemoryMap}\r\n   *\r\n   * @example\r\n   * import {HexMemoryMap} from 'ECB';\r\n   *\r\n   * let intelHexString =\r\n   *     \":100000000102030405060708090A0B0C0D0E0F1068\\n\" +\r\n   *     \":00000001FF\";\r\n   *\r\n   * let memMap = HexMemoryMap.fromHex(intelHexString);\r\n   *\r\n   * for (let [address, dataBlock] of memMap) {\r\n   *     console.log('Data block at ', address, ', bytes: ', dataBlock);\r\n   * }\r\n   */\r\n  static fromHex(hexText: string, maxBlockSize = Infinity) {\r\n    const blocks = new HexMemoryMap()\r\n\r\n    let lastCharacterParsed = 0\r\n    let matchResult\r\n    let recordCount = 0\r\n\r\n    // Upper Linear Base Address, the 16 most significant bits (2 bytes) of\r\n    // the current 32-bit (4-byte) address\r\n    // In practice this is a offset that is summed to the \"load offset\" of the\r\n    // data records\r\n    let ulba = 0\r\n\r\n    hexLineRegexp.lastIndex = 0 // Reset the regexp, if not it would skip content when called twice\r\n\r\n    while ((matchResult = hexLineRegexp.exec(hexText)) !== null) {\r\n      recordCount++\r\n\r\n      // By default, a regexp loop ignores gaps between matches, but\r\n      // we want to be aware of them.\r\n      if (lastCharacterParsed !== matchResult.index) {\r\n        throw new Error(\r\n          'Malformed hex file: Could not parse between characters ' +\r\n            lastCharacterParsed +\r\n            ' and ' +\r\n            matchResult.index +\r\n            ' (\"' +\r\n            hexText\r\n              .substring(lastCharacterParsed, Math.min(matchResult.index, lastCharacterParsed + 16))\r\n              .trim() +\r\n            '\")'\r\n        )\r\n      }\r\n      lastCharacterParsed = hexLineRegexp.lastIndex\r\n\r\n      // Give pretty names to the match's capture groups\r\n      const [, recordStr, recordChecksum] = matchResult\r\n\r\n      // String to Buffer - https://stackoverflow.com/questions/43131242/how-to-convert-a-hexademical-string-of-data-to-an-arraybuffer-in-javascript\r\n      const recordBytes = Buffer.from(\r\n        recordStr.match(/[\\da-f]{2}/gi)?.map((h) => parseInt(h, 16)) || []\r\n      )\r\n\r\n      const recordLength = recordBytes[0]\r\n      if (recordLength + 4 !== recordBytes.length) {\r\n        throw new Error(\r\n          'Mismatched record length at record ' +\r\n            recordCount +\r\n            ' (' +\r\n            matchResult[0].trim() +\r\n            '), expected ' +\r\n            recordLength +\r\n            ' data bytes but actual length is ' +\r\n            (recordBytes.length - 4)\r\n        )\r\n      }\r\n\r\n      const cs = checksum(recordBytes)\r\n      if (parseInt(recordChecksum, 16) !== cs) {\r\n        throw new Error(\r\n          'Checksum failed at record ' +\r\n            recordCount +\r\n            ' (' +\r\n            matchResult[0].trim() +\r\n            '), should be ' +\r\n            cs.toString(16)\r\n        )\r\n      }\r\n\r\n      const offset = (recordBytes[1] << 8) + recordBytes[2]\r\n      const recordType = recordBytes[3]\r\n      const data = recordBytes.subarray(4)\r\n\r\n      if (recordType === 0) {\r\n        // Data record, contains data\r\n        // Create a new block, at (upper linear base address + offset)\r\n        if (blocks.has(ulba + offset)) {\r\n          throw new Error(\r\n            'Duplicated data at record ' + recordCount + ' (' + matchResult[0].trim() + ')'\r\n          )\r\n        }\r\n        if (offset + data.length > 0x10000) {\r\n          throw new Error(\r\n            'Data at record ' +\r\n              recordCount +\r\n              ' (' +\r\n              matchResult[0].trim() +\r\n              ') wraps over 0xFFFF. This would trigger ambiguous behaviour. Please restructure your data so that for every record the data offset plus the data length do not exceed 0xFFFF.'\r\n          )\r\n        }\r\n\r\n        blocks.set(ulba + offset, data)\r\n      } else {\r\n        // All non-data records must have a data offset of zero\r\n        if (offset !== 0) {\r\n          throw new Error(\r\n            'Record ' +\r\n              recordCount +\r\n              ' (' +\r\n              matchResult[0].trim() +\r\n              ') must have 0000 as data offset.'\r\n          )\r\n        }\r\n\r\n        switch (recordType) {\r\n          case 1: // EOF\r\n            if (lastCharacterParsed !== hexText.length) {\r\n              // This record should be at the very end of the string\r\n              throw new Error('There is data after an EOF record at record ' + recordCount)\r\n            }\r\n\r\n            return blocks.join(maxBlockSize)\r\n\r\n          case 2: // Extended Segment Address Record\r\n            // Sets the 16 most significant bits of the 20-bit Segment Base\r\n            // Address for the subsequent data.\r\n            ulba = ((data[0] << 8) + data[1]) << 4\r\n            break\r\n\r\n          case 3: // Start Segment Address Record\r\n            // Do nothing. Record type 3 only applies to 16-bit Intel CPUs,\r\n            // where it should reset the program counter (CS+IP CPU registers)\r\n            break\r\n\r\n          case 4: // Extended Linear Address Record\r\n            // Sets the 16 most significant (upper) bits of the 32-bit Linear Address\r\n            // for the subsequent data\r\n            ulba = ((data[0] << 8) + data[1]) << 16\r\n            break\r\n\r\n          case 5: // Start Linear Address Record\r\n            // Do nothing. Record type 5 only applies to 32-bit Intel CPUs,\r\n            // where it should reset the program counter (EIP CPU register)\r\n            // It might have meaning for other CPU architectures\r\n            // (see http://infocenter.arm.com/help/index.jsp?topic=/com.arm.doc.faqs/ka9903.html )\r\n            // but will be ignored nonetheless.\r\n            break\r\n          default:\r\n            throw new Error(\r\n              'Invalid record type 0x' +\r\n                hexpad(recordType) +\r\n                ' at record ' +\r\n                recordCount +\r\n                ' (should be between 0x00 and 0x05)'\r\n            )\r\n        }\r\n      }\r\n    }\r\n\r\n    if (recordCount) {\r\n      throw new Error('No EOF record at end of file')\r\n    } else {\r\n      throw new Error('Malformed .hex file, could not parse any registers')\r\n    }\r\n  }\r\n\r\n  /**\r\n   * Returns a <strong>new</strong> instance of {@linkcode HexMemoryMap}, containing\r\n   * the same data, but concatenating together those memory blocks that are adjacent.\r\n   *<br/>\r\n   * The insertion order of keys in the {@linkcode HexMemoryMap} is guaranteed to be strictly\r\n   * ascending. In other words, when iterating through the {@linkcode HexMemoryMap}, the addresses\r\n   * will be ordered in ascending order.\r\n   *<br/>\r\n   * If <tt>maxBlockSize</tt> is given, blocks will be concatenated together only\r\n   * until the joined block reaches this size in bytes. This means that the output\r\n   * {@linkcode HexMemoryMap} might have more entries than the input one.\r\n   *<br/>\r\n   * If there is any overlap between blocks, an error will be thrown.\r\n   *<br/>\r\n   * The returned {@linkcode HexMemoryMap} will use newly allocated memory.\r\n   *\r\n   * @param {Number} [maxBlockSize=Infinity] Maximum size of the <tt>Buffer</tt>s in the\r\n   * returned {@linkcode HexMemoryMap}.\r\n   *\r\n   * @return {HexMemoryMap}\r\n   */\r\n  join(maxBlockSize = Infinity) {\r\n    // First pass, create a Map of address→length of contiguous blocks\r\n    const sortedKeys = Array.from(this.keys()).sort((a, b) => a - b)\r\n    const blockSizes = new Map()\r\n    let lastBlockAddr = -1\r\n    let lastBlockEndAddr = -1\r\n\r\n    for (let i = 0, l = sortedKeys.length; i < l; i++) {\r\n      const blockAddr = sortedKeys[i]\r\n      const blockLength = this.get(sortedKeys[i])!.length\r\n\r\n      if (lastBlockEndAddr === blockAddr && lastBlockEndAddr - lastBlockAddr < maxBlockSize) {\r\n        // Grow when the previous end address equals the current,\r\n        // and we don't go over the maximum block size.\r\n        blockSizes.set(lastBlockAddr, blockSizes.get(lastBlockAddr) + blockLength)\r\n        lastBlockEndAddr += blockLength\r\n      } else if (lastBlockEndAddr <= blockAddr) {\r\n        // Else mark a new block.\r\n        blockSizes.set(blockAddr, blockLength)\r\n        lastBlockAddr = blockAddr\r\n        lastBlockEndAddr = blockAddr + blockLength\r\n      } else {\r\n        throw new Error('Overlapping data around address 0x' + blockAddr.toString(16))\r\n      }\r\n    }\r\n\r\n    // Second pass: allocate memory for the contiguous blocks and copy data around.\r\n    const mergedBlocks = new HexMemoryMap()\r\n    let mergingBlock\r\n    let mergingBlockAddr = -1\r\n    for (let i = 0, l = sortedKeys.length; i < l; i++) {\r\n      const blockAddr = sortedKeys[i]\r\n      if (blockSizes.has(blockAddr)) {\r\n        mergingBlock = Buffer.alloc(blockSizes.get(blockAddr))\r\n        mergedBlocks.set(blockAddr, mergingBlock)\r\n        mergingBlockAddr = blockAddr\r\n      }\r\n      mergingBlock!.set(this.get(blockAddr)!, blockAddr - mergingBlockAddr)\r\n    }\r\n\r\n    return mergedBlocks\r\n  }\r\n\r\n  /**\r\n   * Given a {@link https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Map|<tt>Map</tt>}\r\n   * of {@linkcode HexMemoryMap}s, indexed by a alphanumeric ID,\r\n   * returns a <tt>Map</tt> of address to tuples (<tt>Arrays</tt>s of length 2) of the form\r\n   * <tt>(id, Buffer)</tt>s.\r\n   *<br/>\r\n   * The scenario for using this is having several {@linkcode HexMemoryMap}s, from several calls to\r\n   * {@link module:ECB~hexToArrays|hexToArrays}, each having a different identifier.\r\n   * This function locates where those memory block sets overlap, and returns a <tt>Map</tt>\r\n   * containing addresses as keys, and arrays as values. Each array will contain 1 or more\r\n   * <tt>(id, Buffer)</tt> tuples: the identifier of the memory block set that has\r\n   * data in that region, and the data itself. When memory block sets overlap, there will\r\n   * be more than one tuple.\r\n   *<br/>\r\n   * The <tt>Buffer</tt>s in the output are\r\n   * {@link https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/TypedArray/subarray|subarrays}\r\n   * of the input data; new memory is <strong>not</strong> allocated for them.\r\n   *<br/>\r\n   * The insertion order of keys in the output <tt>Map</tt> is guaranteed to be strictly\r\n   * ascending. In other words, when iterating through the <tt>Map</tt>, the addresses\r\n   * will be ordered in ascending order.\r\n   *<br/>\r\n   * When two blocks overlap, the corresponding array of tuples will have the tuples ordered\r\n   * in the insertion order of the input <tt>Map</tt> of block sets.\r\n   *<br/>\r\n   *\r\n   * @param {Map.HexMemoryMap} HexMemoryMaps The input memory block sets\r\n   *\r\n   * @example\r\n   * import {HexMemoryMap} from 'ECB';\r\n   *\r\n   * let memMap1 = HexMemoryMap.fromHex( hexdata1 );\r\n   * let memMap2 = HexMemoryMap.fromHex( hexdata2 );\r\n   * let memMap3 = HexMemoryMap.fromHex( hexdata3 );\r\n   *\r\n   * let maps = new Map([\r\n   *  ['file A', blocks1],\r\n   *  ['file B', blocks2],\r\n   *  ['file C', blocks3]\r\n   * ]);\r\n   *\r\n   * let overlappings = HexMemoryMap.overlapHexMemoryMaps(maps);\r\n   *\r\n   * for (let [address, tuples] of overlappings) {\r\n   *     // if 'tuples' has length > 1, there is an overlap starting at 'address'\r\n   *\r\n   *     for (let [address, tuples] of overlappings) {\r\n   *         let [id, bytes] = tuple;\r\n   *         // 'id' in this example is either 'file A', 'file B' or 'file C'\r\n   *     }\r\n   * }\r\n   * @return {Map.Array<mixed,Buffer>} The map of possibly overlapping memory blocks\r\n   */\r\n  static overlapHexMemoryMaps(HexMemoryMaps: Map<string, HexMemoryMap>) {\r\n    // First pass: create a list of addresses where any block starts or ends.\r\n    const cuts = new Set<number>()\r\n    for (const [, blocks] of HexMemoryMaps) {\r\n      for (const [address, block] of blocks) {\r\n        cuts.add(address)\r\n        cuts.add(address + block.length)\r\n      }\r\n    }\r\n\r\n    const orderedCuts = Array.from(cuts.values()).sort((a, b) => a - b)\r\n    const overlaps = new Map()\r\n\r\n    // Second pass: iterate through the cuts, get slices of every intersecting blockset\r\n    for (let i = 0, l = orderedCuts.length - 1; i < l; i++) {\r\n      const cut = orderedCuts[i]\r\n      const nextCut = orderedCuts[i + 1]\r\n      const tuples = []\r\n\r\n      for (const [setId, blocks] of HexMemoryMaps) {\r\n        // Find the block with the highest address that is equal or lower to\r\n        // the current cut (if any)\r\n        const blockAddr = Array.from(blocks.keys()).reduce((acc, val) => {\r\n          if (val > cut) {\r\n            return acc\r\n          }\r\n          return Math.max(acc, val)\r\n        }, -1)\r\n\r\n        if (blockAddr !== -1) {\r\n          const block = blocks.get(blockAddr)!\r\n          const subBlockStart = cut - blockAddr\r\n          const subBlockEnd = nextCut - blockAddr\r\n\r\n          if (subBlockStart < block.length) {\r\n            tuples.push([setId, block.subarray(subBlockStart, subBlockEnd)])\r\n          }\r\n        }\r\n      }\r\n\r\n      if (tuples.length) {\r\n        overlaps.set(cut, tuples)\r\n      }\r\n    }\r\n\r\n    return overlaps\r\n  }\r\n\r\n  /**\r\n   * Given the output of the {@linkcode HexMemoryMap.overlapHexMemoryMaps|overlapHexMemoryMaps}\r\n   * (a <tt>Map</tt> of address to an <tt>Array</tt> of <tt>(id, Buffer)</tt> tuples),\r\n   * returns a {@linkcode HexMemoryMap}. This discards the IDs in the process.\r\n   *<br/>\r\n   * The output <tt>Map</tt> contains as many entries as the input one (using the same addresses\r\n   * as keys), but the value for each entry will be the <tt>Buffer</tt> of the <b>last</b>\r\n   * tuple for each address in the input data.\r\n   *<br/>\r\n   * The scenario is wanting to join together several parsed .hex files, not worrying about\r\n   * their overlaps.\r\n   *<br/>\r\n   *\r\n   * @param {Map.Array<mixed,Buffer>} overlaps The (possibly overlapping) input memory blocks\r\n   * @return {HexMemoryMap} The flattened memory blocks\r\n   */\r\n  static flattenOverlaps(overlaps: Map<number, [string, Buffer][]>) {\r\n    return new HexMemoryMap(\r\n      Array.from(overlaps.entries()).map(([address, tuples]) => {\r\n        return [address, tuples[tuples.length - 1][1]] as [number, Buffer]\r\n      })\r\n    )\r\n  }\r\n\r\n  /**\r\n   * Returns a new instance of {@linkcode HexMemoryMap}, where:\r\n   *\r\n   * <ul>\r\n   *  <li>Each key (the start address of each <tt>Buffer</tt>) is a multiple of\r\n   *    <tt>pageSize</tt></li>\r\n   *  <li>The size of each <tt>Buffer</tt> is exactly <tt>pageSize</tt></li>\r\n   *  <li>Bytes from the input map to bytes in the output</li>\r\n   *  <li>Bytes not in the input are replaced by a padding value</li>\r\n   * </ul>\r\n   *<br/>\r\n   * The scenario is wanting to prepare pages of bytes for a write operation, where the write\r\n   * operation affects a whole page/sector at once.\r\n   *<br/>\r\n   * The insertion order of keys in the output {@linkcode HexMemoryMap} is guaranteed\r\n   * to be strictly ascending. In other words, when iterating through the\r\n   * {@linkcode HexMemoryMap}, the addresses will be ordered in ascending order.\r\n   *<br/>\r\n   * The <tt>Buffer</tt>s in the output will be newly allocated.\r\n   *<br/>\r\n   *\r\n   * @param {Number} [pageSize=1024] The size of the output pages, in bytes\r\n   * @param {Number} [pad=0xFF] The byte value to use for padding\r\n   * @return {HexMemoryMap}\r\n   */\r\n  paginate(pageSize = 1024, pad = 0xff) {\r\n    if (pageSize <= 0) {\r\n      throw new Error('Page size must be greater than zero')\r\n    }\r\n    const outPages = new HexMemoryMap()\r\n    let page\r\n\r\n    const sortedKeys = Array.from(this.keys()).sort((a, b) => a - b)\r\n\r\n    for (let i = 0, l = sortedKeys.length; i < l; i++) {\r\n      const blockAddr = sortedKeys[i]\r\n      const block = this.get(blockAddr)!\r\n      const blockLength = block.length\r\n      const blockEnd = blockAddr + blockLength\r\n\r\n      for (\r\n        let pageAddr = blockAddr - (blockAddr % pageSize);\r\n        pageAddr < blockEnd;\r\n        pageAddr += pageSize\r\n      ) {\r\n        page = outPages.get(pageAddr)\r\n        if (!page) {\r\n          page = Buffer.alloc(pageSize)\r\n          page.fill(pad)\r\n          outPages.set(pageAddr, page)\r\n        }\r\n\r\n        const offset = pageAddr - blockAddr\r\n        let subBlock\r\n        if (offset <= 0) {\r\n          // First page which intersects the block\r\n          subBlock = block.subarray(0, Math.min(pageSize + offset, blockLength))\r\n          page.set(subBlock, -offset)\r\n        } else {\r\n          // Any other page which intersects the block\r\n          subBlock = block.subarray(offset, offset + Math.min(pageSize, blockLength - offset))\r\n          page.set(subBlock, 0)\r\n        }\r\n      }\r\n    }\r\n\r\n    return outPages\r\n  }\r\n\r\n  /**\r\n   * Locates the <tt>Buffer</tt> which contains the given offset,\r\n   * and returns the four bytes held at that offset, as a 32-bit unsigned integer.\r\n   *\r\n   *<br/>\r\n   * Behaviour is similar to {@linkcode https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DataView/getUint32|DataView.prototype.getUint32},\r\n   * except that this operates over a {@linkcode HexMemoryMap} instead of\r\n   * over an <tt>ArrayBuffer</tt>, and that this may return <tt>undefined</tt> if\r\n   * the address is not <em>entirely</em> contained within one of the <tt>Buffer</tt>s.\r\n   *<br/>\r\n   *\r\n   * @param {Number} offset The memory offset to read the data\r\n   * @param {Boolean} [littleEndian=false] Whether to fetch the 4 bytes as a little- or big-endian integer\r\n   * @return {Number|undefined} An unsigned 32-bit integer number\r\n   */\r\n  getUint32(offset: number, littleEndian: boolean) {\r\n    const keys = Array.from(this.keys())\r\n\r\n    for (let i = 0, l = keys.length; i < l; i++) {\r\n      const blockAddr = keys[i]\r\n      const block = this.get(blockAddr)!\r\n      const blockLength = block.length\r\n      const blockEnd = blockAddr + blockLength\r\n\r\n      if (blockAddr <= offset && offset + 4 <= blockEnd) {\r\n        return new DataView(block.buffer, offset - blockAddr, 4).getUint32(0, littleEndian)\r\n      }\r\n    }\r\n    return\r\n  }\r\n\r\n  /**\r\n   * Returns a <tt>String</tt> of text representing a .hex file.\r\n   * <br/>\r\n   * The writer has an opinionated behaviour. Check the project's\r\n   * {@link https://github.com/NordicSemiconductor/ECB#Features|README file} for details.\r\n   *\r\n   * @param {Number} [lineSize=16] Maximum number of bytes to be encoded in each data record.\r\n   * Must have a value between 1 and 255, as per the specification.\r\n   *\r\n   * @return {String} String of text with the .hex representation of the input binary data\r\n   *\r\n   * @example\r\n   * import {HexMemoryMap} from 'ECB';\r\n   *\r\n   * let memMap = new HexMemoryMap();\r\n   * let bytes = new Buffer(....);\r\n   * memMap.set(0x0FF80000, bytes); // The block with 'bytes' will start at offset 0x0FF80000\r\n   *\r\n   * let string = memMap.asHexString();\r\n   */\r\n  asHexString(lineSize = 16) {\r\n    let lowAddress = 0 // 16 least significant bits of the current addr\r\n    let highAddress = -1 << 16 // 16 most significant bits of the current addr\r\n    const records = []\r\n    if (lineSize <= 0) {\r\n      throw new Error('Size of record must be greater than zero')\r\n    } else if (lineSize > 255) {\r\n      throw new Error('Size of record must be less than 256')\r\n    }\r\n\r\n    // Placeholders\r\n    const offsetRecord = Buffer.alloc(6)\r\n    const recordHeader = Buffer.alloc(4)\r\n\r\n    const sortedKeys = Array.from(this.keys()).sort((a, b) => a - b)\r\n    for (let i = 0, l = sortedKeys.length; i < l; i++) {\r\n      const blockAddr = sortedKeys[i]\r\n      const block = this.get(blockAddr)\r\n\r\n      // Sanity checks\r\n      if (!(block instanceof Buffer)) {\r\n        throw new Error('Block at offset ' + blockAddr + ' is not an Buffer')\r\n      }\r\n      if (blockAddr < 0) {\r\n        throw new Error('Block at offset ' + blockAddr + ' has a negative thus invalid address')\r\n      }\r\n      const blockSize = block.length\r\n      if (!blockSize) {\r\n        continue\r\n      } // Skip zero-length blocks\r\n\r\n      if (blockAddr > highAddress + 0xffff) {\r\n        // Insert a new 0x04 record to jump to a new 64KiB block\r\n\r\n        // Round up the least significant 16 bits - no bitmasks because they trigger\r\n        // base-2 negative numbers, whereas subtracting the modulo maintains precision\r\n        highAddress = blockAddr - (blockAddr % 0x10000)\r\n        lowAddress = 0\r\n\r\n        offsetRecord[0] = 2 // Length\r\n        offsetRecord[1] = 0 // Load offset, high byte\r\n        offsetRecord[2] = 0 // Load offset, low byte\r\n        offsetRecord[3] = 4 // Record type\r\n        offsetRecord[4] = highAddress >> 24 // new address offset, high byte\r\n        offsetRecord[5] = highAddress >> 16 // new address offset, low byte\r\n\r\n        records.push(\r\n          ':' +\r\n            Array.prototype.map.call(offsetRecord, hexpad).join('') +\r\n            hexpad(checksum(offsetRecord))\r\n        )\r\n      }\r\n\r\n      if (blockAddr < highAddress + lowAddress) {\r\n        throw new Error(\r\n          'Block starting at 0x' + blockAddr.toString(16) + ' overlaps with a previous block.'\r\n        )\r\n      }\r\n\r\n      lowAddress = blockAddr % 0x10000\r\n      let blockOffset = 0\r\n      const blockEnd = blockAddr + blockSize\r\n      if (blockEnd > 0xffffffff) {\r\n        throw new Error('Data cannot be over 0xFFFFFFFF')\r\n      }\r\n\r\n      // Loop for every 64KiB memory segment that spans this block\r\n      while (highAddress + lowAddress < blockEnd) {\r\n        if (lowAddress > 0xffff) {\r\n          // Insert a new 0x04 record to jump to a new 64KiB block\r\n          highAddress += 1 << 16 // Increase by one\r\n          lowAddress = 0\r\n\r\n          offsetRecord[0] = 2 // Length\r\n          offsetRecord[1] = 0 // Load offset, high byte\r\n          offsetRecord[2] = 0 // Load offset, low byte\r\n          offsetRecord[3] = 4 // Record type\r\n          offsetRecord[4] = highAddress >> 24 // new address offset, high byte\r\n          offsetRecord[5] = highAddress >> 16 // new address offset, low byte\r\n\r\n          records.push(\r\n            ':' +\r\n              Array.prototype.map.call(offsetRecord, hexpad).join('') +\r\n              hexpad(checksum(offsetRecord))\r\n          )\r\n        }\r\n\r\n        let recordSize = -1\r\n        // Loop for every record for that spans the current 64KiB memory segment\r\n        while (lowAddress < 0x10000 && recordSize) {\r\n          recordSize = Math.min(\r\n            lineSize, // Normal case\r\n            blockEnd - highAddress - lowAddress, // End of block\r\n            0x10000 - lowAddress // End of low addresses\r\n          )\r\n\r\n          if (recordSize) {\r\n            recordHeader[0] = recordSize // Length\r\n            recordHeader[1] = lowAddress >> 8 // Load offset, high byte\r\n            recordHeader[2] = lowAddress // Load offset, low byte\r\n            recordHeader[3] = 0 // Record type\r\n\r\n            const subBlock = block.subarray(blockOffset, blockOffset + recordSize) // Data bytes for this record\r\n\r\n            records.push(\r\n              ':' +\r\n                Array.prototype.map.call(recordHeader, hexpad).join('') +\r\n                Array.prototype.map.call(subBlock, hexpad).join('') +\r\n                hexpad(checksumTwo(recordHeader, subBlock))\r\n            )\r\n\r\n            blockOffset += recordSize\r\n            lowAddress += recordSize\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    records.push(':00000001FF') // EOF record\r\n\r\n    return records.join('\\n')\r\n  }\r\n\r\n  /**\r\n   * Performs a deep copy of the current {@linkcode HexMemoryMap}, returning a new one\r\n   * with exactly the same contents, but allocating new memory for each of its\r\n   * <tt>Buffer</tt>s.\r\n   *\r\n   * @return {HexMemoryMap}\r\n   */\r\n  clone() {\r\n    const cloned = new HexMemoryMap()\r\n\r\n    for (const [addr, value] of this) {\r\n      cloned.set(addr, Buffer.from(value))\r\n    }\r\n\r\n    return cloned\r\n  }\r\n\r\n  /**\r\n   * Given one <tt>Buffer</tt>, looks through its contents and returns a new\r\n   * {@linkcode HexMemoryMap}, stripping away those regions where there are only\r\n   * padding bytes.\r\n   * <br/>\r\n   * The start of the input <tt>Buffer</tt> is assumed to be offset zero for the output.\r\n   * <br/>\r\n   * The use case here is dumping memory from a working device and try to see the\r\n   * \"interesting\" memory regions it has. This assumes that there is a constant,\r\n   * predefined padding byte value being used in the \"non-interesting\" regions.\r\n   * In other words: this will work as long as the dump comes from a flash memory\r\n   * which has been previously erased (thus <tt>0xFF</tt>s for padding), or from a\r\n   * previously blanked HDD (thus <tt>0x00</tt>s for padding).\r\n   * <br/>\r\n   * This method uses <tt>subarray</tt> on the input data, and thus does not allocate memory\r\n   * for the <tt>Buffer</tt>s.\r\n   *\r\n   * @param {Buffer} bytes The input data\r\n   * @param {Number} [padByte=0xFF] The value of the byte assumed to be used as padding\r\n   * @param {Number} [minPadLength=64] The minimum number of consecutive pad bytes to\r\n   * be considered actual padding\r\n   *\r\n   * @return {HexMemoryMap}\r\n   */\r\n  static fromPaddedBuffer(bytes: Buffer, padByte = 0xff, minPadLength = 64) {\r\n    if (!(bytes instanceof Buffer)) {\r\n      throw new Error('Bytes passed to fromPaddedBuffer are not an Buffer')\r\n    }\r\n\r\n    // The algorithm used is naïve and checks every byte.\r\n    // An obvious optimization would be to implement Boyer-Moore\r\n    // (see https://en.wikipedia.org/wiki/Boyer%E2%80%93Moore_string_search_algorithm )\r\n    // or otherwise start skipping up to minPadLength bytes when going through a non-pad\r\n    // byte.\r\n    // Anyway, we could expect a lot of cases where there is a majority of pad bytes,\r\n    // and the algorithm should check most of them anyway, so the perf gain is questionable.\r\n\r\n    const memMap = new HexMemoryMap()\r\n    let consecutivePads = 0\r\n    let lastNonPad = -1\r\n    let firstNonPad = 0\r\n    let skippingBytes = false\r\n    const l = bytes.length\r\n\r\n    for (let addr = 0; addr < l; addr++) {\r\n      const byte = bytes[addr]\r\n\r\n      if (byte === padByte) {\r\n        consecutivePads++\r\n        if (consecutivePads >= minPadLength) {\r\n          // Edge case: ignore writing a zero-length block when skipping\r\n          // bytes at the beginning of the input\r\n          if (lastNonPad !== -1) {\r\n            /// Add the previous block to the result memMap\r\n            memMap.set(firstNonPad, bytes.subarray(firstNonPad, lastNonPad + 1))\r\n          }\r\n\r\n          skippingBytes = true\r\n        }\r\n      } else {\r\n        if (skippingBytes) {\r\n          skippingBytes = false\r\n          firstNonPad = addr\r\n        }\r\n        lastNonPad = addr\r\n        consecutivePads = 0\r\n      }\r\n    }\r\n\r\n    // At EOF, add the last block if not skipping bytes already (and input not empty)\r\n    if (!skippingBytes && lastNonPad !== -1) {\r\n      memMap.set(firstNonPad, bytes.subarray(firstNonPad, l))\r\n    }\r\n\r\n    return memMap\r\n  }\r\n\r\n  /**\r\n   * Returns a new instance of {@linkcode HexMemoryMap}, containing only data between\r\n   * the addresses <tt>address</tt> and <tt>address + length</tt>.\r\n   * Behaviour is similar to {@linkcode https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array/slice|Array.prototype.slice},\r\n   * in that the return value is a portion of the current {@linkcode HexMemoryMap}.\r\n   *\r\n   * <br/>\r\n   * The returned {@linkcode HexMemoryMap} might be empty.\r\n   *\r\n   * <br/>\r\n   * Internally, this uses <tt>subarray</tt>, so new memory is not allocated.\r\n   *\r\n   * @param {Number} address The start address of the slice\r\n   * @param {Number} length The length of memory map to slice out\r\n   * @return {HexMemoryMap}\r\n   */\r\n  slice(address: number, length = Infinity) {\r\n    if (length < 0) {\r\n      throw new Error('Length of the slice cannot be negative')\r\n    }\r\n\r\n    const sliced = new HexMemoryMap()\r\n\r\n    for (const [blockAddr, block] of this) {\r\n      const blockLength = block.length\r\n\r\n      if (blockAddr + blockLength >= address && blockAddr < address + length) {\r\n        const sliceStart = Math.max(address, blockAddr)\r\n        const sliceEnd = Math.min(address + length, blockAddr + blockLength)\r\n        const sliceLength = sliceEnd - sliceStart\r\n        const relativeSliceStart = sliceStart - blockAddr\r\n\r\n        if (sliceLength > 0) {\r\n          sliced.set(\r\n            sliceStart,\r\n            block.subarray(relativeSliceStart, relativeSliceStart + sliceLength)\r\n          )\r\n        }\r\n      }\r\n    }\r\n    return sliced\r\n  }\r\n\r\n  /**\r\n   * Returns a new instance of {@linkcode https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DataView/getUint32|Buffer}, containing only data between\r\n   * the addresses <tt>address</tt> and <tt>address + length</tt>. Any byte without a value\r\n   * in the input {@linkcode HexMemoryMap} will have a value of <tt>padByte</tt>.\r\n   *\r\n   * <br/>\r\n   * This method allocates new memory.\r\n   *\r\n   * @param {Number} address The start address of the slice\r\n   * @param {Number} length The length of memory map to slice out\r\n   * @param {Number} [padByte=0xFF] The value of the byte assumed to be used as padding\r\n   * @return {Buffer}\r\n   */\r\n  slicePad(address: number, length: number, padByte = 0xff) {\r\n    if (length < 0) {\r\n      throw new Error('Length of the slice cannot be negative')\r\n    }\r\n\r\n    const out = Buffer.alloc(length, padByte)\r\n\r\n    for (const [blockAddr, block] of this) {\r\n      const blockLength = block.length\r\n\r\n      if (blockAddr + blockLength >= address && blockAddr < address + length) {\r\n        const sliceStart = Math.max(address, blockAddr)\r\n        const sliceEnd = Math.min(address + length, blockAddr + blockLength)\r\n        const sliceLength = sliceEnd - sliceStart\r\n        const relativeSliceStart = sliceStart - blockAddr\r\n\r\n        if (sliceLength > 0) {\r\n          out.set(\r\n            block.subarray(relativeSliceStart, relativeSliceStart + sliceLength),\r\n            sliceStart - address\r\n          )\r\n        }\r\n      }\r\n    }\r\n    return out\r\n  }\r\n\r\n  /**\r\n   * Checks whether the current memory map contains the one given as a parameter.\r\n   *\r\n   * <br/>\r\n   * \"Contains\" means that all the offsets that have a byte value in the given\r\n   * memory map have a value in the current memory map, and that the byte values\r\n   * are the same.\r\n   *\r\n   * <br/>\r\n   * An empty memory map is always contained in any other memory map.\r\n   *\r\n   * <br/>\r\n   * Returns boolean <tt>true</tt> if the memory map is contained, <tt>false</tt>\r\n   * otherwise.\r\n   *\r\n   * @param {HexMemoryMap} memMap The memory map to check\r\n   * @return {Boolean}\r\n   */\r\n  contains(memMap: HexMemoryMap) {\r\n    for (const [blockAddr, block] of memMap) {\r\n      const blockLength = block.length\r\n\r\n      const slice = this.slice(blockAddr, blockLength).join().get(blockAddr)\r\n\r\n      if (!slice || slice.length !== blockLength) {\r\n        return false\r\n      }\r\n\r\n      for (const i in block) {\r\n        if (block[i] !== slice[i]) {\r\n          return false\r\n        }\r\n      }\r\n    }\r\n    return true\r\n  }\r\n}\r\n"], "names": ["__webpack_require__", "exports", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "Symbol", "toStringTag", "value", "hexLineRegexp", "checksum", "bytes", "reduce", "sum", "v", "hexpad", "number", "toString", "toUpperCase", "padStart", "Number", "isInteger", "isFinite", "Math", "floor", "HexMemoryMap", "_blocks", "constructor", "blocks", "this", "Map", "iterator", "tuple", "Array", "isArray", "length", "Error", "set", "addrs", "keys", "addr", "parseInt", "<PERSON><PERSON><PERSON>", "clear", "delete", "entries", "for<PERSON>ach", "callback", "thisArg", "has", "values", "size", "fromHex", "hexText", "maxBlockSize", "Infinity", "matchResult", "lastCharacterParsed", "recordCount", "ulba", "lastIndex", "exec", "index", "substring", "min", "trim", "recordStr", "recordChecksum", "recordBytes", "from", "match", "map", "h", "recordLength", "cs", "offset", "recordType", "data", "subarray", "join", "sortedKeys", "sort", "a", "b", "blockSizes", "lastBlockAddr", "lastBlockEndAddr", "i", "l", "blockAddr", "blockLength", "mergedBlocks", "mergingBlock", "mergingBlockAddr", "alloc", "overlapHexMemoryMaps", "HexMemoryMaps", "cuts", "Set", "address", "block", "add", "orderedCuts", "overlaps", "cut", "nextCut", "tuples", "setId", "acc", "val", "max", "subBlockStart", "subBlockEnd", "push", "flattenOverlaps", "paginate", "pageSize", "pad", "outPages", "page", "blockEnd", "pageAddr", "fill", "subBlock", "getUint32", "littleEndian", "DataView", "buffer", "asHexString", "lineSize", "lowAddress", "highAddress", "records", "offsetRecord", "recordH<PERSON>er", "blockSize", "blockOffset", "recordSize", "array2", "clone", "cloned", "fromPaddedBuffer", "padByte", "min<PERSON><PERSON><PERSON><PERSON>", "memMap", "consecutivePads", "lastNonPad", "firstNonPad", "skipping<PERSON><PERSON>s", "slice", "sliced", "sliceStart", "slice<PERSON><PERSON>th", "relativeSliceStart", "slicePad", "out", "contains"], "sourceRoot": ""}