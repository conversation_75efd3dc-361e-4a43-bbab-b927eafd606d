(()=>{"use strict";var e={n:t=>{var r=t&&t.__esModule?()=>t.default:()=>t;return e.d(r,{a:r}),r},d:(t,r)=>{for(var n in r)e.o(r,n)&&!e.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:r[n]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{CMAC:()=>s});const r=require("crypto");var n=e.n(r);const o=Buffer.from("00000000000000000000000000000000","hex"),l=Buffer.from("00000000000000000000000000000087","hex"),u=16;function c(e){const t=Buffer.alloc(e.length),r=e.length-1;for(let n=0;n<r;n++)t[n]=e[n]<<1,128&e[n+1]&&(t[n]+=1);return t[r]=e[r]<<1,t}function f(e,t){const r=Math.min(e.length,t.length),n=Buffer.alloc(r);for(let o=0;o<r;o++)n[o]=e[o]^t[o];return n}function a(e,t){const r={16:"aes-128-cbc",24:"aes-192-cbc",32:"aes-256-cbc"};if(!r[e.length])throw new Error("Keys must be 128, 192, or 256 bits in length.");const l=n().createCipheriv(r[e.length],e,o),u=l.update(t);return l.final(),u}function s(e,t){const r=function(e){const t=a(e,o);let r=c(t);128&t[0]&&(r=f(r,l));let n=c(r);return 128&r[0]&&(n=f(n,l)),{subkey1:r,subkey2:n}}(e);let n,s,b,h=Math.ceil(t.length/u);0===h?(h=1,n=!1):n=t.length%u==0,b=h-1,s=n?f(i(t,b),r.subkey1):f(function(e,t){const r=Buffer.alloc(u),n=t*u,o=e.length;return r.fill(0),e.copy(r,0,n,o),r[o-n]=128,r}(t,b),r.subkey2);let y,d=Buffer.from("00000000000000000000000000000000","hex");for(let r=0;r<b;r++)y=f(d,i(t,r)),d=a(e,y);return y=f(s,d),a(e,y)}function i(e,t){const r=Buffer.alloc(u),n=t*u,o=n+u;return e.copy(r,0,n,o),r}var b=exports;for(var h in t)b[h]=t[h];t.__esModule&&Object.defineProperty(b,"__esModule",{value:!0})})();
//# sourceMappingURL=cryptoExt.js.map