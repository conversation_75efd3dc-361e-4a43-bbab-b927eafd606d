{"name": "ecubuspro", "version": "0.8.41", "description": "EcuBus-Pro", "main": "./out/main/index.js", "author": "<EMAIL>", "homepage": "https://app.whyengineer.com", "license": "license.txt", "ecubusPro": {"support": ["can"], "vendor": {"can": ["<PERSON><PERSON><PERSON>", "peak", "zlg", "<PERSON><PERSON>s", "vector"]}}, "lint-staged": {"*.{ts,vue}": ["prettier --write", "eslint --ext .ts,.vue --fix"]}, "scripts": {"format": "prettier --write .", "lint": "eslint . --ext .ts,.vue --fix", "typecheck:node": "tsc --noEmit -p tsconfig.node.json --composite false", "typecheck:web": "vue-tsc --noEmit -p tsconfig.web.json --composite false", "typecheck": "npm run typecheck:node && npm run typecheck:web", "worker": "cd src/main/worker/secureAccess && npx node-gyp rebuild &&  cd ../../../.. && npx webpack --config webpack.config.js --mode production", "worker:js": "npx webpack --config webpack.config.js --mode production", "start": "electron-vite preview", "dev": "electron-vite dev", "test": "vitest --config vitest.config.ts", "build": "npm run typecheck && electron-vite build", "cli:dev": "electron-vite dev -c cli.vite.ts -w --entry cli/out/ecb_cli2.js", "cli:build": "electron-vite build -c cli.vite.ts", "cli:build:win": "electron-vite build -c cli.vite.ts && cd cli && npm run win", "cli:build:linux": "electron-vite build -c cli.vite.ts && cd cli && npm run linux", "cli:build:mac": "electron-vite build -c cli.vite.ts && cd cli && npm run mac", "postinstall": "electron-builder install-app-deps", "build:unpack": "npm run build && electron-builder --dir", "build:win": "npm run build && electron-builder --win", "build:mac": "npm run build && electron-builder --mac", "build:linux": "npm run build && electron-builder --linux", "api": "typedoc --tsconfig tsconfig.worker.json", "docan": "cd src/main/docan && npx node-gyp rebuild", "dolin": "cd src/main/dolin && npx node-gyp rebuild", "docs:dev": "vitepress dev", "docs:build": "vitepress build", "docs:preview": "vitepress preview", "prepare": "husky"}, "dependencies": {"@electron-toolkit/preload": "^3.0.0", "@electron-toolkit/utils": "^3.0.0", "@element-plus/icons-vue": "^2.3.1", "@form-create/element-ui": "^3.2.22", "@iconify/icons-grommet-icons": "^1.2.5", "@iconify/icons-mdi": "^1.2.48", "@joint/core": "^4.0.4", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vueuse/core": "^10.7.2", "@vxe-ui/plugin-render-element": "^4.0.10", "@xterm/addon-fit": "^0.10.0", "@xterm/xterm": "^5.5.0", "animate.css": "^4.1.1", "async": "^3.2.6", "async-validator": "^4.2.5", "axios": "^1.8.2", "chevrotain": "^11.0.3", "codemirror": "^6.65.7", "colors": "^1.4.0", "commander": "^12.1.0", "dayjs": "^1.11.13", "e-virt-table": "1.1.5", "echarts": "^5.6.0", "electron-log": "^5.1.2", "electron-store": "^8.1.0", "electron-updater": "^6.3.9", "element-plus": "^2.9.3", "emittery": "^1.0.3", "events": "^3.3.0", "exceljs": "^4.4.0", "glob": "^11.0.0", "handlebars": "^4.7.8", "jquery": "^3.7.1", "jquery-ui": "^1.14.1", "js-beautify": "^1.15.1", "json5": "^2.2.3", "lodash": "^4.17.21", "marked": "^14.1.3", "path-browserify": "^1.0.1", "pinia": "^3.0.2", "python-shell": "^5.0.0", "sortablejs": "^1.15.2", "ts-morph": "^24.0.0", "uuid": "^9.0.1", "vite-plugin-string": "^1.2.3", "vite-raw-plugin": "^1.0.2", "vue-grid-layout-v3": "^3.1.2", "vue-router": "^4.2.5", "vuedraggable": "4.1.0", "vxe-pc-ui": "^4.2.53", "vxe-table": "4.9.4", "winston": "^3.17.0", "workerpool": "github:frankie-zeng/workerpool#24f9e85", "xterm": "^5.3.0"}, "devDependencies": {"@electron-toolkit/eslint-config": "^1.0.2", "@electron-toolkit/eslint-config-ts": "^1.0.1", "@electron-toolkit/tsconfig": "^1.0.1", "@electron/rebuild": "^3.7.0", "@iconify/icons-ep": "^1.2.12", "@iconify/icons-material-symbols": "^1.2.58", "@iconify/icons-ph": "^1.2.5", "@iconify/vue": "^4.1.1", "@liudonghua123/pkg": "^6.0.1", "@rollup/plugin-node-resolve": "^15.3.0", "@rushstack/eslint-patch": "^1.7.1", "@types/async": "^3.2.24", "@types/node": "^22.9.1", "@types/path-browserify": "^1.0.3", "@types/uuid": "^10.0.0", "@vitejs/plugin-vue": "^5.0.3", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^12.0.0", "ali-oss": "^6.21.0", "dotenv": "^16.4.5", "electron": "^32.3.1", "electron-builder": "24.13.3", "electron-vite": "^2.3.0", "eslint": "^8.57.1", "eslint-plugin-vue": "^9.32.0", "husky": "^9.1.7", "lint-staged": "^15.4.3", "mermaid": "^11.4.1", "node-addon-api": "^8.2.2", "node-gyp": "^11.1.0", "node-loader": "^2.1.0", "prettier": "^3.5.2", "rollup": "^4.27.3", "rollup-plugin-dts": "^6.1.1", "sass": "^1.83.0", "ts-loader": "^9.5.1", "typedoc": "^0.27.6", "typescript": "^5.3.3", "viewerjs": "^1.11.6", "vite": "^5.4.19", "vite-plugin-conditional-compiler": "^0.3.1", "vite-plugin-node-polyfills": "^0.21.0", "vitepress": "^1.6.3", "vitepress-plugin-mermaid": "^2.0.17", "vitest": "^3.0.9", "vue": "^3.5.13", "vue-tsc": "^2.1.6", "webpack": "^5.96.1", "webpack-cli": "^5.1.4", "yaml": "^2.6.0"}, "overrides": {"esbuild": "^0.25.0", "axios": "^1.8.2", "@babel/runtime": "^7.26.10", "tar-fs": "2.1.3"}}