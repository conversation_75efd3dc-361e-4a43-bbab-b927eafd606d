name: Release

on:
  push:
    tags:
      - 'v*'
  # Allows you to run this workflow manually from the Actions tab
  workflow_dispatch:

jobs:
  # Build job
  build-windows:
    runs-on: windows-latest
    permissions: write-all
    outputs:
      upload_url: ${{ steps.create_release.outputs.upload_url }}
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Not needed if lastUpdated is not enabled
      - name: Set Version
        id: version
        shell: pwsh
        run: |
          $version = $env:GITHUB_REF_NAME -replace '^v',''
          echo "VERSION=$version" >> $env:GITHUB_ENV
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: npm # or pnpm / yarn
      - name: Install dependencies
        run: npm install
      - name: Build with VitePress
        run: npm run docs:build
      - name: Build docan
        run: npm run docan
      - name: Build dolin
        run: npm run dolin
      - name: Build worker
        run: npm run worker
      - name: Build api
        run: npm run api
      - name: Build cli
        run: npm run cli:build:win
      - name: Build APP
        run: npm run build:win
      - name: Create Release
        id: create_release
        uses: actions/create-release@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }} # This token is provided by Actions, you do not need to create your own token
        with:
          tag_name: ${{ github.ref }}
          release_name: Release ${{ github.ref }}
          draft: false
          prerelease: false
      - name: Upload CLI
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ steps.create_release.outputs.upload_url }}
          asset_path: ./resources/lib/ecb_cli.exe
          asset_name: ecb_cli.exe
          asset_content_type: application/vnd.microsoft.portable-executable
      - name: Upload Release Asset
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ steps.create_release.outputs.upload_url }} # This pulls from the CREATE RELEASE step above, referencing it's ID to get its outputs object, which include a `upload_url`. See this blog post for more info: https://jasonet.co/posts/new-features-of-github-actions/#passing-data-to-future-steps
          asset_path: ./dist/EcuBus-Pro ${{ env.VERSION }}.exe
          asset_name: EcuBus-Pro ${{ env.VERSION }}.exe
          asset_content_type: application/vnd.microsoft.portable-executable
      - name: Archive Release
        uses: thedoctor0/zip-release@0.7.5
        with:
          type: zip
          directory: ./dist/win-unpacked
          filename: Portable EcuBus-Pro ${{ env.VERSION }}.zip
      - name: Upload Release Asset
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ steps.create_release.outputs.upload_url }}
          asset_path: ./dist/win-unpacked/Portable EcuBus-Pro ${{ env.VERSION }}.zip
          asset_name: Portable EcuBus-Pro ${{ env.VERSION }}.zip
          asset_content_type: application/zip
      - name: Upload to aliyun
        env:
          ALIYUN_ACCESS_KEY_ID: ${{ secrets.ALIYUN_ACCESS_KEY_ID }}
          ALIYUN_ACCESS_KEY_SECRET: ${{ secrets.ALIYUN_ACCESS_KEY_SECRET }}
        run: node tools/upload.js --note --app

  build-linux:
    needs: build-windows
    runs-on: ubuntu-latest
    permissions: write-all
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Set Version
        id: version
        shell: bash
        run: |
          version=${GITHUB_REF_NAME#v}
          echo "VERSION=$version" >> $GITHUB_ENV
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: npm
      - name: Install dependencies
        run: npm install
      - name: Build with VitePress
        run: npm run docs:build
      - name: Build docan
        run: npm run docan
      - name: Build dolin
        run: npm run dolin
      - name: Build worker
        run: npm run worker
      - name: Build api
        run: npm run api
      - name: Build cli
        run: npm run cli:build:linux
      - name: Build APP
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: npm run build:linux
      - name: Upload CLI
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.build-windows.outputs.upload_url }}
          asset_path: ./resources/lib/ecb_cli
          asset_name: ecb_cli
          asset_content_type: application/octet-stream

      - name: Upload DEB
        uses: actions/upload-release-asset@v1
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        with:
          upload_url: ${{ needs.build-windows.outputs.upload_url }}
          asset_path: ./dist/ecubuspro_${{ env.VERSION }}_amd64.deb
          asset_name: EcuBus-Pro_${{ env.VERSION }}_amd64.deb
          asset_content_type: application/vnd.debian.binary-package
