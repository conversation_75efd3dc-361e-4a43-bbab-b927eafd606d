{
  "extends": "@electron-toolkit/tsconfig/tsconfig.node.json",
  "include": [
    "electron.vite.config.*",
    "src/main/**/*.ts",
    "src/cli/**/*.ts",
    "src/preload/**/*",
    "test/**/*.test.ts",
    "src/renderer/src/database/**/*.ts",
    "src/renderer/src/worker/**/*.ts"
  ],
  "compilerOptions": {
    "composite": true,
    "noUnusedLocals": false,
    "noUnusedParameters": false,
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noImplicitAny":true,
    "forceConsistentCasingInFileNames": true,
    "allowSyntheticDefaultImports":true,  
    "strict": true,
    "types": [
      "electron-vite/node"
    ],
    "paths": {
      "nodeCan/*": [
        "src/main/share/*"
      ]
    },
    "baseUrl": ".",
  },

}