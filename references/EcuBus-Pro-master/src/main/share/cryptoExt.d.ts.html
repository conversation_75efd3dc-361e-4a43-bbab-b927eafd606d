/**
 * CMAC algorithm, nodejs default crypto module does not support CMAC, so we need to implement it ourselves.
 * @category  Crypto Extend
 * @param key key must be 128, 192, or 256 bits in length.
 * @param message input message
 * @returns CMAC result
 * @example
 * const key=Buffer.from('2b7e151628aed2a6abf7158809cf4f3c','hex');
 * const message=Buffer.from('6bc1bee22e409f96e93d7e117393172a','hex');
 * const result=CMAC(key,message);
 * //which should print 070a16b46b4d4144f79bdd9dd04a287c
 * console.log(result.toString('hex'));
 */
declare function CMAC(key: Buffer, message: Buffer): Buffer;

export { CMAC };
