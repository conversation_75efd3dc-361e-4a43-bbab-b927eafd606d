/**
 * Parser/writer for the "Intel hex" format.
 */
/**
 * @category Util
 * @example
 * import {HexMemoryMap} from 'ECB';
 *
 * let memMap1 = new HexMemoryMap();
 * let memMap2 = new HexMemoryMap([[0, new Buffer(1,2,3,4)]]);
 * let memMap3 = new HexMemoryMap({0: new Buffer(1,2,3,4)});
 * let memMap4 = new HexMemoryMap({0xCF0: new Buffer(1,2,3,4)});
 *
 * const block = HexMemoryMap.fromHex(hexText);
 */
declare class HexMemoryMap {
    private _blocks;
    /**
     * @param {Iterable} blocks The initial value for the memory blocks inside this
     * <tt>HexMemoryMap</tt>. All keys must be numeric, and all values must be instances of
     * <tt>Buffer</tt>. Optionally it can also be a plain <tt>Object</tt> with
     * only numeric keys.
     */
    constructor(blocks?: Iterable<[number, Buffer]> | {
        [key: string]: Buffer;
    } | null);
    set(addr: number, value: Buffer): Map<number, Buffer>;
    get(addr: number): Buffer | undefined;
    clear(): void;
    delete(addr: number): boolean;
    entries(): IterableIterator<[number, Buffer]>;
    forEach(callback: (value: Buffer, key: number, map: Map<number, Buffer>) => void, thisArg?: any): void;
    has(addr: number): boolean;
    keys(): IterableIterator<number>;
    values(): IterableIterator<Buffer>;
    get size(): number;
    [Symbol.iterator](): IterableIterator<[number, Buffer]>;
    /**
     * Parses a string containing data formatted in "Intel HEX" format, and
     * returns an instance of {@linkcode HexMemoryMap}.
     *<br/>
     * The insertion order of keys in the {@linkcode HexMemoryMap} is guaranteed to be strictly
     * ascending. In other words, when iterating through the {@linkcode HexMemoryMap}, the addresses
     * will be ordered in ascending order.
     *<br/>
     * The parser has an opinionated behaviour, and will throw a descriptive error if it
     * encounters some malformed input. Check the project's
     * {@link https://github.com/NordicSemiconductor/ECB#Features|README file} for details.
     *<br/>
     * If <tt>maxBlockSize</tt> is given, any contiguous data block larger than that will
     * be split in several blocks.
     *
     * @param {String} hexText The contents of a .hex file.
     * @param {Number} [maxBlockSize=Infinity] Maximum size of the returned <tt>Buffer</tt>s.
     *
     * @return {HexMemoryMap}
     *
     * @example
     * import {HexMemoryMap} from 'ECB';
     *
     * let intelHexString =
     *     ":100000000102030405060708090A0B0C0D0E0F1068\n" +
     *     ":00000001FF";
     *
     * let memMap = HexMemoryMap.fromHex(intelHexString);
     *
     * for (let [address, dataBlock] of memMap) {
     *     console.log('Data block at ', address, ', bytes: ', dataBlock);
     * }
     */
    static fromHex(hexText: string, maxBlockSize?: number): HexMemoryMap;
    /**
     * Returns a <strong>new</strong> instance of {@linkcode HexMemoryMap}, containing
     * the same data, but concatenating together those memory blocks that are adjacent.
     *<br/>
     * The insertion order of keys in the {@linkcode HexMemoryMap} is guaranteed to be strictly
     * ascending. In other words, when iterating through the {@linkcode HexMemoryMap}, the addresses
     * will be ordered in ascending order.
     *<br/>
     * If <tt>maxBlockSize</tt> is given, blocks will be concatenated together only
     * until the joined block reaches this size in bytes. This means that the output
     * {@linkcode HexMemoryMap} might have more entries than the input one.
     *<br/>
     * If there is any overlap between blocks, an error will be thrown.
     *<br/>
     * The returned {@linkcode HexMemoryMap} will use newly allocated memory.
     *
     * @param {Number} [maxBlockSize=Infinity] Maximum size of the <tt>Buffer</tt>s in the
     * returned {@linkcode HexMemoryMap}.
     *
     * @return {HexMemoryMap}
     */
    join(maxBlockSize?: number): HexMemoryMap;
    /**
     * Given a {@link https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Map|<tt>Map</tt>}
     * of {@linkcode HexMemoryMap}s, indexed by a alphanumeric ID,
     * returns a <tt>Map</tt> of address to tuples (<tt>Arrays</tt>s of length 2) of the form
     * <tt>(id, Buffer)</tt>s.
     *<br/>
     * The scenario for using this is having several {@linkcode HexMemoryMap}s, from several calls to
     * {@link module:ECB~hexToArrays|hexToArrays}, each having a different identifier.
     * This function locates where those memory block sets overlap, and returns a <tt>Map</tt>
     * containing addresses as keys, and arrays as values. Each array will contain 1 or more
     * <tt>(id, Buffer)</tt> tuples: the identifier of the memory block set that has
     * data in that region, and the data itself. When memory block sets overlap, there will
     * be more than one tuple.
     *<br/>
     * The <tt>Buffer</tt>s in the output are
     * {@link https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/TypedArray/subarray|subarrays}
     * of the input data; new memory is <strong>not</strong> allocated for them.
     *<br/>
     * The insertion order of keys in the output <tt>Map</tt> is guaranteed to be strictly
     * ascending. In other words, when iterating through the <tt>Map</tt>, the addresses
     * will be ordered in ascending order.
     *<br/>
     * When two blocks overlap, the corresponding array of tuples will have the tuples ordered
     * in the insertion order of the input <tt>Map</tt> of block sets.
     *<br/>
     *
     * @param {Map.HexMemoryMap} HexMemoryMaps The input memory block sets
     *
     * @example
     * import {HexMemoryMap} from 'ECB';
     *
     * let memMap1 = HexMemoryMap.fromHex( hexdata1 );
     * let memMap2 = HexMemoryMap.fromHex( hexdata2 );
     * let memMap3 = HexMemoryMap.fromHex( hexdata3 );
     *
     * let maps = new Map([
     *  ['file A', blocks1],
     *  ['file B', blocks2],
     *  ['file C', blocks3]
     * ]);
     *
     * let overlappings = HexMemoryMap.overlapHexMemoryMaps(maps);
     *
     * for (let [address, tuples] of overlappings) {
     *     // if 'tuples' has length > 1, there is an overlap starting at 'address'
     *
     *     for (let [address, tuples] of overlappings) {
     *         let [id, bytes] = tuple;
     *         // 'id' in this example is either 'file A', 'file B' or 'file C'
     *     }
     * }
     * @return {Map.Array<mixed,Buffer>} The map of possibly overlapping memory blocks
     */
    static overlapHexMemoryMaps(HexMemoryMaps: Map<string, HexMemoryMap>): Map<any, any>;
    /**
     * Given the output of the {@linkcode HexMemoryMap.overlapHexMemoryMaps|overlapHexMemoryMaps}
     * (a <tt>Map</tt> of address to an <tt>Array</tt> of <tt>(id, Buffer)</tt> tuples),
     * returns a {@linkcode HexMemoryMap}. This discards the IDs in the process.
     *<br/>
     * The output <tt>Map</tt> contains as many entries as the input one (using the same addresses
     * as keys), but the value for each entry will be the <tt>Buffer</tt> of the <b>last</b>
     * tuple for each address in the input data.
     *<br/>
     * The scenario is wanting to join together several parsed .hex files, not worrying about
     * their overlaps.
     *<br/>
     *
     * @param {Map.Array<mixed,Buffer>} overlaps The (possibly overlapping) input memory blocks
     * @return {HexMemoryMap} The flattened memory blocks
     */
    static flattenOverlaps(overlaps: Map<number, [string, Buffer][]>): HexMemoryMap;
    /**
     * Returns a new instance of {@linkcode HexMemoryMap}, where:
     *
     * <ul>
     *  <li>Each key (the start address of each <tt>Buffer</tt>) is a multiple of
     *    <tt>pageSize</tt></li>
     *  <li>The size of each <tt>Buffer</tt> is exactly <tt>pageSize</tt></li>
     *  <li>Bytes from the input map to bytes in the output</li>
     *  <li>Bytes not in the input are replaced by a padding value</li>
     * </ul>
     *<br/>
     * The scenario is wanting to prepare pages of bytes for a write operation, where the write
     * operation affects a whole page/sector at once.
     *<br/>
     * The insertion order of keys in the output {@linkcode HexMemoryMap} is guaranteed
     * to be strictly ascending. In other words, when iterating through the
     * {@linkcode HexMemoryMap}, the addresses will be ordered in ascending order.
     *<br/>
     * The <tt>Buffer</tt>s in the output will be newly allocated.
     *<br/>
     *
     * @param {Number} [pageSize=1024] The size of the output pages, in bytes
     * @param {Number} [pad=0xFF] The byte value to use for padding
     * @return {HexMemoryMap}
     */
    paginate(pageSize?: number, pad?: number): HexMemoryMap;
    /**
     * Locates the <tt>Buffer</tt> which contains the given offset,
     * and returns the four bytes held at that offset, as a 32-bit unsigned integer.
     *
     *<br/>
     * Behaviour is similar to {@linkcode https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DataView/getUint32|DataView.prototype.getUint32},
     * except that this operates over a {@linkcode HexMemoryMap} instead of
     * over an <tt>ArrayBuffer</tt>, and that this may return <tt>undefined</tt> if
     * the address is not <em>entirely</em> contained within one of the <tt>Buffer</tt>s.
     *<br/>
     *
     * @param {Number} offset The memory offset to read the data
     * @param {Boolean} [littleEndian=false] Whether to fetch the 4 bytes as a little- or big-endian integer
     * @return {Number|undefined} An unsigned 32-bit integer number
     */
    getUint32(offset: number, littleEndian: boolean): number | undefined;
    /**
     * Returns a <tt>String</tt> of text representing a .hex file.
     * <br/>
     * The writer has an opinionated behaviour. Check the project's
     * {@link https://github.com/NordicSemiconductor/ECB#Features|README file} for details.
     *
     * @param {Number} [lineSize=16] Maximum number of bytes to be encoded in each data record.
     * Must have a value between 1 and 255, as per the specification.
     *
     * @return {String} String of text with the .hex representation of the input binary data
     *
     * @example
     * import {HexMemoryMap} from 'ECB';
     *
     * let memMap = new HexMemoryMap();
     * let bytes = new Buffer(....);
     * memMap.set(0x0FF80000, bytes); // The block with 'bytes' will start at offset 0x0FF80000
     *
     * let string = memMap.asHexString();
     */
    asHexString(lineSize?: number): string;
    /**
     * Performs a deep copy of the current {@linkcode HexMemoryMap}, returning a new one
     * with exactly the same contents, but allocating new memory for each of its
     * <tt>Buffer</tt>s.
     *
     * @return {HexMemoryMap}
     */
    clone(): HexMemoryMap;
    /**
     * Given one <tt>Buffer</tt>, looks through its contents and returns a new
     * {@linkcode HexMemoryMap}, stripping away those regions where there are only
     * padding bytes.
     * <br/>
     * The start of the input <tt>Buffer</tt> is assumed to be offset zero for the output.
     * <br/>
     * The use case here is dumping memory from a working device and try to see the
     * "interesting" memory regions it has. This assumes that there is a constant,
     * predefined padding byte value being used in the "non-interesting" regions.
     * In other words: this will work as long as the dump comes from a flash memory
     * which has been previously erased (thus <tt>0xFF</tt>s for padding), or from a
     * previously blanked HDD (thus <tt>0x00</tt>s for padding).
     * <br/>
     * This method uses <tt>subarray</tt> on the input data, and thus does not allocate memory
     * for the <tt>Buffer</tt>s.
     *
     * @param {Buffer} bytes The input data
     * @param {Number} [padByte=0xFF] The value of the byte assumed to be used as padding
     * @param {Number} [minPadLength=64] The minimum number of consecutive pad bytes to
     * be considered actual padding
     *
     * @return {HexMemoryMap}
     */
    static fromPaddedBuffer(bytes: Buffer, padByte?: number, minPadLength?: number): HexMemoryMap;
    /**
     * Returns a new instance of {@linkcode HexMemoryMap}, containing only data between
     * the addresses <tt>address</tt> and <tt>address + length</tt>.
     * Behaviour is similar to {@linkcode https://developer.mozilla.org/docs/Web/JavaScript/Reference/Global_Objects/Array/slice|Array.prototype.slice},
     * in that the return value is a portion of the current {@linkcode HexMemoryMap}.
     *
     * <br/>
     * The returned {@linkcode HexMemoryMap} might be empty.
     *
     * <br/>
     * Internally, this uses <tt>subarray</tt>, so new memory is not allocated.
     *
     * @param {Number} address The start address of the slice
     * @param {Number} length The length of memory map to slice out
     * @return {HexMemoryMap}
     */
    slice(address: number, length?: number): HexMemoryMap;
    /**
     * Returns a new instance of {@linkcode https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/DataView/getUint32|Buffer}, containing only data between
     * the addresses <tt>address</tt> and <tt>address + length</tt>. Any byte without a value
     * in the input {@linkcode HexMemoryMap} will have a value of <tt>padByte</tt>.
     *
     * <br/>
     * This method allocates new memory.
     *
     * @param {Number} address The start address of the slice
     * @param {Number} length The length of memory map to slice out
     * @param {Number} [padByte=0xFF] The value of the byte assumed to be used as padding
     * @return {Buffer}
     */
    slicePad(address: number, length: number, padByte?: number): Buffer;
    /**
     * Checks whether the current memory map contains the one given as a parameter.
     *
     * <br/>
     * "Contains" means that all the offsets that have a byte value in the given
     * memory map have a value in the current memory map, and that the byte values
     * are the same.
     *
     * <br/>
     * An empty memory map is always contained in any other memory map.
     *
     * <br/>
     * Returns boolean <tt>true</tt> if the memory map is contained, <tt>false</tt>
     * otherwise.
     *
     * @param {HexMemoryMap} memMap The memory map to check
     * @return {Boolean}
     */
    contains(memMap: HexMemoryMap): boolean;
}

export { HexMemoryMap };
