/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (https://www.swig.org).
 * Version 4.2.1
 *
 * Do not make changes to this file unless you know what you are doing - modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */


#define SWIG_VERSION 0x040201
#define SWIGJAVASCRIPT
/* -----------------------------------------------------------------------------
 *  This section contains generic SWIG labels for method/variable
 *  declarations/attributes, and other compiler dependent labels.
 * ----------------------------------------------------------------------------- */

/* template workaround for compilers that cannot correctly implement the C++ standard */
#ifndef SWIGTEMPLATEDISAMBIGUATOR
# if defined(__SUNPRO_CC) && (__SUNPRO_CC <= 0x560)
#  define SWIGTEMPLATEDISAMBIGUATOR template
# elif defined(__HP_aCC)
/* Needed even with `aCC -AA' when `aCC -V' reports HP ANSI C++ B3910B A.03.55 */
/* If we find a maximum version that requires this, the test would be __HP_aCC <= 35500 for A.03.55 */
#  define SWIGTEMPLATEDISAMBIGUATOR template
# else
#  define SWIGTEMPLATEDISAMBIGUATOR
# endif
#endif

/* inline attribute */
#ifndef SWIGINLINE
# if defined(__cplusplus) || (defined(__GNUC__) && !defined(__STRICT_ANSI__))
#   define SWIGINLINE inline
# else
#   define SWIGINLINE
# endif
#endif

/* attribute recognised by some compilers to avoid 'unused' warnings */
#ifndef SWIGUNUSED
# if defined(__GNUC__)
#   if !(defined(__cplusplus)) || (__GNUC__ > 3 || (__GNUC__ == 3 && __GNUC_MINOR__ >= 4))
#     define SWIGUNUSED __attribute__ ((__unused__))
#   else
#     define SWIGUNUSED
#   endif
# elif defined(__ICC)
#   define SWIGUNUSED __attribute__ ((__unused__))
# else
#   define SWIGUNUSED
# endif
#endif

#ifndef SWIG_MSC_UNSUPPRESS_4505
# if defined(_MSC_VER)
#   pragma warning(disable : 4505) /* unreferenced local function has been removed */
# endif
#endif

#ifndef SWIGUNUSEDPARM
# ifdef __cplusplus
#   define SWIGUNUSEDPARM(p)
# else
#   define SWIGUNUSEDPARM(p) p SWIGUNUSED
# endif
#endif

/* internal SWIG method */
#ifndef SWIGINTERN
# define SWIGINTERN static SWIGUNUSED
#endif

/* internal inline SWIG method */
#ifndef SWIGINTERNINLINE
# define SWIGINTERNINLINE SWIGINTERN SWIGINLINE
#endif

/* exporting methods */
#if defined(__GNUC__)
#  if (__GNUC__ >= 4) || (__GNUC__ == 3 && __GNUC_MINOR__ >= 4)
#    ifndef GCC_HASCLASSVISIBILITY
#      define GCC_HASCLASSVISIBILITY
#    endif
#  endif
#endif

#ifndef SWIGEXPORT
# if defined(_WIN32) || defined(__WIN32__) || defined(__CYGWIN__)
#   if defined(STATIC_LINKED)
#     define SWIGEXPORT
#   else
#     define SWIGEXPORT __declspec(dllexport)
#   endif
# else
#   if defined(__GNUC__) && defined(GCC_HASCLASSVISIBILITY)
#     define SWIGEXPORT __attribute__ ((visibility("default")))
#   else
#     define SWIGEXPORT
#   endif
# endif
#endif

/* calling conventions for Windows */
#ifndef SWIGSTDCALL
# if defined(_WIN32) || defined(__WIN32__) || defined(__CYGWIN__)
#   define SWIGSTDCALL __stdcall
# else
#   define SWIGSTDCALL
# endif
#endif

/* Deal with Microsoft's attempt at deprecating C standard runtime functions */
#if !defined(SWIG_NO_CRT_SECURE_NO_DEPRECATE) && defined(_MSC_VER) && !defined(_CRT_SECURE_NO_DEPRECATE)
# define _CRT_SECURE_NO_DEPRECATE
#endif

/* Deal with Microsoft's attempt at deprecating methods in the standard C++ library */
#if !defined(SWIG_NO_SCL_SECURE_NO_DEPRECATE) && defined(_MSC_VER) && !defined(_SCL_SECURE_NO_DEPRECATE)
# define _SCL_SECURE_NO_DEPRECATE
#endif

/* Deal with Apple's deprecated 'AssertMacros.h' from Carbon-framework */
#if defined(__APPLE__) && !defined(__ASSERT_MACROS_DEFINE_VERSIONS_WITHOUT_UNDERSCORES)
# define __ASSERT_MACROS_DEFINE_VERSIONS_WITHOUT_UNDERSCORES 0
#endif

/* Intel's compiler complains if a variable which was never initialised is
 * cast to void, which is a common idiom which we use to indicate that we
 * are aware a variable isn't used.  So we just silence that warning.
 * See: https://github.com/swig/swig/issues/192 for more discussion.
 */
#ifdef __INTEL_COMPILER
# pragma warning disable 592
#endif

#if defined(__cplusplus) && __cplusplus >=201103L
# define SWIG_NULLPTR nullptr
#else
# define SWIG_NULLPTR NULL
#endif 

/* -----------------------------------------------------------------------------
 * swigcompat.swg
 *
 * Macros to provide support compatibility with older C and C++ standards.
 * ----------------------------------------------------------------------------- */

/* C99 and C++11 should provide snprintf, but define SWIG_NO_SNPRINTF
 * if you're missing it.
 */
#if ((defined __STDC_VERSION__ && __STDC_VERSION__ >= 199901L) || \
     (defined __cplusplus && __cplusplus >= 201103L) || \
     defined SWIG_HAVE_SNPRINTF) && \
    !defined SWIG_NO_SNPRINTF
# define SWIG_snprintf(O,S,F,A) snprintf(O,S,F,A)
# define SWIG_snprintf2(O,S,F,A,B) snprintf(O,S,F,A,B)
#else
/* Fallback versions ignore the buffer size, but most of our uses either have a
 * fixed maximum possible size or dynamically allocate a buffer that's large
 * enough.
 */
# define SWIG_snprintf(O,S,F,A) sprintf(O,F,A)
# define SWIG_snprintf2(O,S,F,A,B) sprintf(O,F,A,B)
#endif


#define SWIG_FromCharPtrAndSize(cptr, size) SWIG_Env_FromCharPtrAndSize(env, cptr, size)
#define SWIG_FromCharPtr(cptr)              SWIG_Env_FromCharPtrAndSize(env, cptr, strlen(cptr))


#define SWIG_NAPI_FROM_DECL_ARGS(arg1)              (Napi::Env env, arg1)
#define SWIG_NAPI_FROM_CALL_ARGS(arg1)              (env, arg1)



#define SWIG_exception_fail(code, msg) do { SWIG_Error(code, msg); SWIG_fail; } while(0) 

#define SWIG_contract_assert(expr, msg) do { if (!(expr)) { SWIG_Error(SWIG_RuntimeError, msg); SWIG_fail; } } while (0) 



#if defined(_CPPUNWIND) || defined(__EXCEPTIONS)
#define NAPI_CPP_EXCEPTIONS
#else
#define NAPI_DISABLE_CPP_EXCEPTIONS
#define NODE_ADDON_API_ENABLE_MAYBE
#endif

// This gives us
// Branch Node.js v10.x - from v10.20.0
// Branch Node.js v12.x - from v12.17.0
// Everything from Node.js v14.0.0 on
// Our limiting feature is napi_set_instance_data
#ifndef NAPI_VERSION
#define NAPI_VERSION 6
#elif NAPI_VERSION < 6
#error NAPI_VERSION 6 is the minimum supported target (Node.js >=14, >=12.17, >=10.20)
#endif
#include <napi.h>

#include <errno.h>
#include <limits.h>
#include <stdlib.h>
#include <assert.h>
#include <map>

/* -----------------------------------------------------------------------------
 * swigrun.swg
 *
 * This file contains generic C API SWIG runtime support for pointer
 * type checking.
 * ----------------------------------------------------------------------------- */

/* This should only be incremented when either the layout of swig_type_info changes,
   or for whatever reason, the runtime changes incompatibly */
#define SWIG_RUNTIME_VERSION "4"

/* define SWIG_TYPE_TABLE_NAME as "SWIG_TYPE_TABLE" */
#ifdef SWIG_TYPE_TABLE
# define SWIG_QUOTE_STRING(x) #x
# define SWIG_EXPAND_AND_QUOTE_STRING(x) SWIG_QUOTE_STRING(x)
# define SWIG_TYPE_TABLE_NAME SWIG_EXPAND_AND_QUOTE_STRING(SWIG_TYPE_TABLE)
#else
# define SWIG_TYPE_TABLE_NAME
#endif

/*
  You can use the SWIGRUNTIME and SWIGRUNTIMEINLINE macros for
  creating a static or dynamic library from the SWIG runtime code.
  In 99.9% of the cases, SWIG just needs to declare them as 'static'.

  But only do this if strictly necessary, ie, if you have problems
  with your compiler or suchlike.
*/

#ifndef SWIGRUNTIME
# define SWIGRUNTIME SWIGINTERN
#endif

#ifndef SWIGRUNTIMEINLINE
# define SWIGRUNTIMEINLINE SWIGRUNTIME SWIGINLINE
#endif

/*  Generic buffer size */
#ifndef SWIG_BUFFER_SIZE
# define SWIG_BUFFER_SIZE 1024
#endif

/* Flags for pointer conversions */
#define SWIG_POINTER_DISOWN        0x1
#define SWIG_CAST_NEW_MEMORY       0x2
#define SWIG_POINTER_NO_NULL       0x4
#define SWIG_POINTER_CLEAR         0x8
#define SWIG_POINTER_RELEASE       (SWIG_POINTER_CLEAR | SWIG_POINTER_DISOWN)

/* Flags for new pointer objects */
#define SWIG_POINTER_OWN           0x1


/*
   Flags/methods for returning states.

   The SWIG conversion methods, as ConvertPtr, return an integer
   that tells if the conversion was successful or not. And if not,
   an error code can be returned (see swigerrors.swg for the codes).

   Use the following macros/flags to set or process the returning
   states.

   In old versions of SWIG, code such as the following was usually written:

     if (SWIG_ConvertPtr(obj,vptr,ty.flags) != -1) {
       // success code
     } else {
       //fail code
     }

   Now you can be more explicit:

    int res = SWIG_ConvertPtr(obj,vptr,ty.flags);
    if (SWIG_IsOK(res)) {
      // success code
    } else {
      // fail code
    }

   which is the same really, but now you can also do

    Type *ptr;
    int res = SWIG_ConvertPtr(obj,(void **)(&ptr),ty.flags);
    if (SWIG_IsOK(res)) {
      // success code
      if (SWIG_IsNewObj(res) {
        ...
	delete *ptr;
      } else {
        ...
      }
    } else {
      // fail code
    }

   I.e., now SWIG_ConvertPtr can return new objects and you can
   identify the case and take care of the deallocation. Of course that
   also requires SWIG_ConvertPtr to return new result values, such as

      int SWIG_ConvertPtr(obj, ptr,...) {
        if (<obj is ok>) {
          if (<need new object>) {
            *ptr = <ptr to new allocated object>;
            return SWIG_NEWOBJ;
          } else {
            *ptr = <ptr to old object>;
            return SWIG_OLDOBJ;
          }
        } else {
          return SWIG_BADOBJ;
        }
      }

   Of course, returning the plain '0(success)/-1(fail)' still works, but you can be
   more explicit by returning SWIG_BADOBJ, SWIG_ERROR or any of the
   SWIG errors code.

   Finally, if the SWIG_CASTRANK_MODE is enabled, the result code
   allows returning the 'cast rank', for example, if you have this

       int food(double)
       int fooi(int);

   and you call

      food(1)   // cast rank '1'  (1 -> 1.0)
      fooi(1)   // cast rank '0'

   just use the SWIG_AddCast()/SWIG_CheckState()
*/

#define SWIG_OK                    (0)
/* Runtime errors are < 0 */
#define SWIG_ERROR                 (-1)
/* Errors in range -1 to -99 are in swigerrors.swg (errors for all languages including those not using the runtime) */
/* Errors in range -100 to -199 are language specific errors defined in *errors.swg */
/* Errors < -200 are generic runtime specific errors */
#define SWIG_ERROR_RELEASE_NOT_OWNED (-200)

#define SWIG_IsOK(r)               (r >= 0)
#define SWIG_ArgError(r)           ((r != SWIG_ERROR) ? r : SWIG_TypeError)

/* The CastRankLimit says how many bits are used for the cast rank */
#define SWIG_CASTRANKLIMIT         (1 << 8)
/* The NewMask denotes the object was created (using new/malloc) */
#define SWIG_NEWOBJMASK            (SWIG_CASTRANKLIMIT  << 1)
/* The TmpMask is for in/out typemaps that use temporary objects */
#define SWIG_TMPOBJMASK            (SWIG_NEWOBJMASK << 1)
/* Simple returning values */
#define SWIG_BADOBJ                (SWIG_ERROR)
#define SWIG_OLDOBJ                (SWIG_OK)
#define SWIG_NEWOBJ                (SWIG_OK | SWIG_NEWOBJMASK)
#define SWIG_TMPOBJ                (SWIG_OK | SWIG_TMPOBJMASK)
/* Check, add and del object mask methods */
#define SWIG_AddNewMask(r)         (SWIG_IsOK(r) ? (r | SWIG_NEWOBJMASK) : r)
#define SWIG_DelNewMask(r)         (SWIG_IsOK(r) ? (r & ~SWIG_NEWOBJMASK) : r)
#define SWIG_IsNewObj(r)           (SWIG_IsOK(r) && (r & SWIG_NEWOBJMASK))
#define SWIG_AddTmpMask(r)         (SWIG_IsOK(r) ? (r | SWIG_TMPOBJMASK) : r)
#define SWIG_DelTmpMask(r)         (SWIG_IsOK(r) ? (r & ~SWIG_TMPOBJMASK) : r)
#define SWIG_IsTmpObj(r)           (SWIG_IsOK(r) && (r & SWIG_TMPOBJMASK))

/* Cast-Rank Mode */
#if defined(SWIG_CASTRANK_MODE)
#  ifndef SWIG_TypeRank
#    define SWIG_TypeRank             unsigned long
#  endif
#  ifndef SWIG_MAXCASTRANK            /* Default cast allowed */
#    define SWIG_MAXCASTRANK          (2)
#  endif
#  define SWIG_CASTRANKMASK          ((SWIG_CASTRANKLIMIT) -1)
#  define SWIG_CastRank(r)           (r & SWIG_CASTRANKMASK)
SWIGINTERNINLINE int SWIG_AddCast(int r) {
  return SWIG_IsOK(r) ? ((SWIG_CastRank(r) < SWIG_MAXCASTRANK) ? (r + 1) : SWIG_ERROR) : r;
}
SWIGINTERNINLINE int SWIG_CheckState(int r) {
  return SWIG_IsOK(r) ? SWIG_CastRank(r) + 1 : 0;
}
#else /* no cast-rank mode */
#  define SWIG_AddCast(r) (r)
#  define SWIG_CheckState(r) (SWIG_IsOK(r) ? 1 : 0)
#endif


#include <string.h>

#ifdef __cplusplus
extern "C" {
#endif

typedef void *(*swig_converter_func)(void *, int *);
typedef struct swig_type_info *(*swig_dycast_func)(void **);

/* Structure to store information on one type */
typedef struct swig_type_info {
  const char             *name;			/* mangled name of this type */
  const char             *str;			/* human readable name of this type */
  swig_dycast_func        dcast;		/* dynamic cast function down a hierarchy */
  struct swig_cast_info  *cast;			/* linked list of types that can cast into this type */
  void                   *clientdata;		/* language specific type data */
  int                    owndata;		/* flag if the structure owns the clientdata */
} swig_type_info;

/* Structure to store a type and conversion function used for casting */
typedef struct swig_cast_info {
  swig_type_info         *type;			/* pointer to type that is equivalent to this type */
  swig_converter_func     converter;		/* function to cast the void pointers */
  struct swig_cast_info  *next;			/* pointer to next cast in linked list */
  struct swig_cast_info  *prev;			/* pointer to the previous cast */
} swig_cast_info;

/* Structure used to store module information
 * Each module generates one structure like this, and the runtime collects
 * all of these structures and stores them in a circularly linked list.*/
typedef struct swig_module_info {
  swig_type_info         **types;		/* Array of pointers to swig_type_info structures that are in this module */
  size_t                 size;		        /* Number of types in this module */
  struct swig_module_info *next;		/* Pointer to next element in circularly linked list */
  swig_type_info         **type_initial;	/* Array of initially generated type structures */
  swig_cast_info         **cast_initial;	/* Array of initially generated casting structures */
  void                    *clientdata;		/* Language specific module data */
} swig_module_info;

/*
  Compare two type names skipping the space characters, therefore
  "char*" == "char *" and "Class<int>" == "Class<int >", etc.

  Return 0 when the two name types are equivalent, as in
  strncmp, but skipping ' '.
*/
SWIGRUNTIME int
SWIG_TypeNameComp(const char *f1, const char *l1,
		  const char *f2, const char *l2) {
  for (;(f1 != l1) && (f2 != l2); ++f1, ++f2) {
    while ((*f1 == ' ') && (f1 != l1)) ++f1;
    while ((*f2 == ' ') && (f2 != l2)) ++f2;
    if (*f1 != *f2) return (*f1 > *f2) ? 1 : -1;
  }
  return (int)((l1 - f1) - (l2 - f2));
}

/*
  Check type equivalence in a name list like <name1>|<name2>|...
  Return 0 if equal, -1 if nb < tb, 1 if nb > tb
*/
SWIGRUNTIME int
SWIG_TypeCmp(const char *nb, const char *tb) {
  int equiv = 1;
  const char* te = tb + strlen(tb);
  const char* ne = nb;
  while (equiv != 0 && *ne) {
    for (nb = ne; *ne; ++ne) {
      if (*ne == '|') break;
    }
    equiv = SWIG_TypeNameComp(nb, ne, tb, te);
    if (*ne) ++ne;
  }
  return equiv;
}

/*
  Check type equivalence in a name list like <name1>|<name2>|...
  Return 0 if not equal, 1 if equal
*/
SWIGRUNTIME int
SWIG_TypeEquiv(const char *nb, const char *tb) {
  return SWIG_TypeCmp(nb, tb) == 0 ? 1 : 0;
}

/*
  Check the typename
*/
SWIGRUNTIME swig_cast_info *
SWIG_TypeCheck(const char *c, swig_type_info *ty) {
  if (ty) {
    swig_cast_info *iter = ty->cast;
    while (iter) {
      if (strcmp(iter->type->name, c) == 0) {
        if (iter == ty->cast)
          return iter;
        /* Move iter to the top of the linked list */
        iter->prev->next = iter->next;
        if (iter->next)
          iter->next->prev = iter->prev;
        iter->next = ty->cast;
        iter->prev = 0;
        if (ty->cast) ty->cast->prev = iter;
        ty->cast = iter;
        return iter;
      }
      iter = iter->next;
    }
  }
  return 0;
}

/*
  Identical to SWIG_TypeCheck, except strcmp is replaced with a pointer comparison
*/
SWIGRUNTIME swig_cast_info *
SWIG_TypeCheckStruct(const swig_type_info *from, swig_type_info *ty) {
  if (ty) {
    swig_cast_info *iter = ty->cast;
    while (iter) {
      if (iter->type == from) {
        if (iter == ty->cast)
          return iter;
        /* Move iter to the top of the linked list */
        iter->prev->next = iter->next;
        if (iter->next)
          iter->next->prev = iter->prev;
        iter->next = ty->cast;
        iter->prev = 0;
        if (ty->cast) ty->cast->prev = iter;
        ty->cast = iter;
        return iter;
      }
      iter = iter->next;
    }
  }
  return 0;
}

/*
  Cast a pointer up an inheritance hierarchy
*/
SWIGRUNTIMEINLINE void *
SWIG_TypeCast(swig_cast_info *ty, void *ptr, int *newmemory) {
  return ((!ty) || (!ty->converter)) ? ptr : (*ty->converter)(ptr, newmemory);
}

/*
   Dynamic pointer casting. Down an inheritance hierarchy
*/
SWIGRUNTIME swig_type_info *
SWIG_TypeDynamicCast(swig_type_info *ty, void **ptr) {
  swig_type_info *lastty = ty;
  if (!ty || !ty->dcast) return ty;
  while (ty && (ty->dcast)) {
    ty = (*ty->dcast)(ptr);
    if (ty) lastty = ty;
  }
  return lastty;
}

/*
  Return the name associated with this type
*/
SWIGRUNTIMEINLINE const char *
SWIG_TypeName(const swig_type_info *ty) {
  return ty->name;
}

/*
  Return the pretty name associated with this type,
  that is an unmangled type name in a form presentable to the user.
*/
SWIGRUNTIME const char *
SWIG_TypePrettyName(const swig_type_info *type) {
  /* The "str" field contains the equivalent pretty names of the
     type, separated by vertical-bar characters.  Choose the last
     name. It should be the most specific; a fully resolved name
     but not necessarily with default template parameters expanded. */
  if (!type) return NULL;
  if (type->str != NULL) {
    const char *last_name = type->str;
    const char *s;
    for (s = type->str; *s; s++)
      if (*s == '|') last_name = s+1;
    return last_name;
  }
  else
    return type->name;
}

/*
   Set the clientdata field for a type
*/
SWIGRUNTIME void
SWIG_TypeClientData(swig_type_info *ti, void *clientdata) {
  swig_cast_info *cast = ti->cast;
  /* if (ti->clientdata == clientdata) return; */
  ti->clientdata = clientdata;

  while (cast) {
    if (!cast->converter) {
      swig_type_info *tc = cast->type;
      if (!tc->clientdata) {
	SWIG_TypeClientData(tc, clientdata);
      }
    }
    cast = cast->next;
  }
}
SWIGRUNTIME void
SWIG_TypeNewClientData(swig_type_info *ti, void *clientdata) {
  SWIG_TypeClientData(ti, clientdata);
  ti->owndata = 1;
}

/*
  Search for a swig_type_info structure only by mangled name
  Search is a O(log #types)

  We start searching at module start, and finish searching when start == end.
  Note: if start == end at the beginning of the function, we go all the way around
  the circular list.
*/
SWIGRUNTIME swig_type_info *
SWIG_MangledTypeQueryModule(swig_module_info *start,
                            swig_module_info *end,
		            const char *name) {
  swig_module_info *iter = start;
  do {
    if (iter->size) {
      size_t l = 0;
      size_t r = iter->size - 1;
      do {
	/* since l+r >= 0, we can (>> 1) instead (/ 2) */
	size_t i = (l + r) >> 1;
	const char *iname = iter->types[i]->name;
	if (iname) {
	  int compare = strcmp(name, iname);
	  if (compare == 0) {
	    return iter->types[i];
	  } else if (compare < 0) {
	    if (i) {
	      r = i - 1;
	    } else {
	      break;
	    }
	  } else if (compare > 0) {
	    l = i + 1;
	  }
	} else {
	  break; /* should never happen */
	}
      } while (l <= r);
    }
    iter = iter->next;
  } while (iter != end);
  return 0;
}

/*
  Search for a swig_type_info structure for either a mangled name or a human readable name.
  It first searches the mangled names of the types, which is a O(log #types)
  If a type is not found it then searches the human readable names, which is O(#types).

  We start searching at module start, and finish searching when start == end.
  Note: if start == end at the beginning of the function, we go all the way around
  the circular list.
*/
SWIGRUNTIME swig_type_info *
SWIG_TypeQueryModule(swig_module_info *start,
                     swig_module_info *end,
		     const char *name) {
  /* STEP 1: Search the name field using binary search */
  swig_type_info *ret = SWIG_MangledTypeQueryModule(start, end, name);
  if (ret) {
    return ret;
  } else {
    /* STEP 2: If the type hasn't been found, do a complete search
       of the str field (the human readable name) */
    swig_module_info *iter = start;
    do {
      size_t i = 0;
      for (; i < iter->size; ++i) {
	if (iter->types[i]->str && (SWIG_TypeEquiv(iter->types[i]->str, name)))
	  return iter->types[i];
      }
      iter = iter->next;
    } while (iter != end);
  }

  /* neither found a match */
  return 0;
}

/*
   Pack binary data into a string
*/
SWIGRUNTIME char *
SWIG_PackData(char *c, void *ptr, size_t sz) {
  static const char hex[17] = "0123456789abcdef";
  const unsigned char *u = (unsigned char *) ptr;
  const unsigned char *eu =  u + sz;
  for (; u != eu; ++u) {
    unsigned char uu = *u;
    *(c++) = hex[(uu & 0xf0) >> 4];
    *(c++) = hex[uu & 0xf];
  }
  return c;
}

/*
   Unpack binary data from a string
*/
SWIGRUNTIME const char *
SWIG_UnpackData(const char *c, void *ptr, size_t sz) {
  unsigned char *u = (unsigned char *) ptr;
  const unsigned char *eu = u + sz;
  for (; u != eu; ++u) {
    char d = *(c++);
    unsigned char uu;
    if ((d >= '0') && (d <= '9'))
      uu = (unsigned char)((d - '0') << 4);
    else if ((d >= 'a') && (d <= 'f'))
      uu = (unsigned char)((d - ('a'-10)) << 4);
    else
      return (char *) 0;
    d = *(c++);
    if ((d >= '0') && (d <= '9'))
      uu |= (unsigned char)(d - '0');
    else if ((d >= 'a') && (d <= 'f'))
      uu |= (unsigned char)(d - ('a'-10));
    else
      return (char *) 0;
    *u = uu;
  }
  return c;
}

/*
   Pack 'void *' into a string buffer.
*/
SWIGRUNTIME char *
SWIG_PackVoidPtr(char *buff, void *ptr, const char *name, size_t bsz) {
  char *r = buff;
  if ((2*sizeof(void *) + 2) > bsz) return 0;
  *(r++) = '_';
  r = SWIG_PackData(r,&ptr,sizeof(void *));
  if (strlen(name) + 1 > (bsz - (r - buff))) return 0;
  strcpy(r,name);
  return buff;
}

SWIGRUNTIME const char *
SWIG_UnpackVoidPtr(const char *c, void **ptr, const char *name) {
  if (*c != '_') {
    if (strcmp(c,"NULL") == 0) {
      *ptr = (void *) 0;
      return name;
    } else {
      return 0;
    }
  }
  return SWIG_UnpackData(++c,ptr,sizeof(void *));
}

SWIGRUNTIME char *
SWIG_PackDataName(char *buff, void *ptr, size_t sz, const char *name, size_t bsz) {
  char *r = buff;
  size_t lname = (name ? strlen(name) : 0);
  if ((2*sz + 2 + lname) > bsz) return 0;
  *(r++) = '_';
  r = SWIG_PackData(r,ptr,sz);
  if (lname) {
    strncpy(r,name,lname+1);
  } else {
    *r = 0;
  }
  return buff;
}

SWIGRUNTIME const char *
SWIG_UnpackDataName(const char *c, void *ptr, size_t sz, const char *name) {
  if (*c != '_') {
    if (strcmp(c,"NULL") == 0) {
      memset(ptr,0,sz);
      return name;
    } else {
      return 0;
    }
  }
  return SWIG_UnpackData(++c,ptr,sz);
}

#ifdef __cplusplus
}
#endif

/* SWIG Errors applicable to all language modules, values are reserved from -1 to -99 */
#define  SWIG_UnknownError    	   -1
#define  SWIG_IOError        	   -2
#define  SWIG_RuntimeError   	   -3
#define  SWIG_IndexError     	   -4
#define  SWIG_TypeError      	   -5
#define  SWIG_DivisionByZero 	   -6
#define  SWIG_OverflowError  	   -7
#define  SWIG_SyntaxError    	   -8
#define  SWIG_ValueError     	   -9
#define  SWIG_SystemError    	   -10
#define  SWIG_AttributeError 	   -11
#define  SWIG_MemoryError    	   -12
#define  SWIG_NullReferenceError   -13


/* ---------------------------------------------------------------------------
 * Error handling
 *
 * ---------------------------------------------------------------------------*/

/*
 * We support several forms:
 *
 * SWIG_Raise("Error message")
 * which creates an Error object with the error message
 *
 * SWIG_Raise(SWIG_TypeError, "Type error")
 * which creates the specified error type with the message
 *
 * SWIG_Raise(obj)
 * which throws the object itself
 *
 * SWIG_Raise(obj, "Exception const &", SWIGType_p_Exception)
 * which also throws the object itself and discards the unneeded extra type info
 *
 * These must be functions instead of macros to use the C++ overloading to
 * resolve the arguments
 */
#define SWIG_exception(code, msg)               SWIG_Error(code, msg)
#define SWIG_fail                               goto fail

#ifdef NAPI_CPP_EXCEPTIONS

#define SWIG_Error(code, msg)                   SWIG_NAPI_Raise(env, code, msg)
#define NAPI_CHECK_MAYBE(maybe)                 (maybe)
#define NAPI_CHECK_RESULT(maybe, result)        (result = maybe)

SWIGINTERN void SWIG_NAPI_Raise(Napi::Env env, const char *msg) {
  throw Napi::Error::New(env, msg);
}

SWIGINTERN void SWIG_NAPI_Raise(Napi::Env env, int type, const char *msg) {
  switch(type) {
    default:
    case SWIG_IOError:
    case SWIG_MemoryError:
    case SWIG_SystemError:
    case SWIG_RuntimeError:
    case SWIG_DivisionByZero:
    case SWIG_SyntaxError:
      throw Napi::Error::New(env, msg);
    case SWIG_OverflowError:
    case SWIG_IndexError:
      throw Napi::RangeError::New(env, msg);
    case SWIG_ValueError:
    case SWIG_TypeError:
      throw Napi::TypeError::New(env, msg);
  }
}

SWIGINTERN void SWIG_NAPI_Raise(Napi::Env env, Napi::Value obj,
        const char *msg = nullptr, swig_type_info *info = nullptr) {
  throw Napi::Error(env, obj);
}

#else

#define SWIG_Error(code, msg)     do { SWIG_NAPI_Raise(env, code, msg); SWIG_fail; } while (0)
#define NAPI_CHECK_MAYBE(maybe)   do { if (maybe.IsNothing()) SWIG_fail; } while (0)
#define NAPI_CHECK_RESULT(maybe, result)          \
        do {                                      \
                auto r = maybe;                   \
                if (r.IsNothing()) SWIG_fail;     \
                result = r.Unwrap();              \
        } while (0)

SWIGINTERN void SWIG_NAPI_Raise(Napi::Env env, const char *msg) {
  Napi::Error::New(env, msg).ThrowAsJavaScriptException();
}

SWIGINTERN void SWIG_NAPI_Raise(Napi::Env env, int type, const char *msg) {
  switch(type) {
    default:
    case SWIG_IOError:
    case SWIG_MemoryError:
    case SWIG_SystemError:
    case SWIG_RuntimeError:
    case SWIG_DivisionByZero:
    case SWIG_SyntaxError:
      Napi::Error::New(env, msg).ThrowAsJavaScriptException();
      return;
    case SWIG_OverflowError:
    case SWIG_IndexError:
      Napi::RangeError::New(env, msg).ThrowAsJavaScriptException();
      return;
    case SWIG_ValueError:
    case SWIG_TypeError:
      Napi::TypeError::New(env, msg).ThrowAsJavaScriptException();
      return;
  }
}

SWIGINTERN void SWIG_NAPI_Raise(Napi::Env env, Napi::Value obj,
        const char *msg = nullptr, swig_type_info *info = nullptr) {
  Napi::Error(env, obj).ThrowAsJavaScriptException();
}

#endif

void JS_veto_set_variable(const Napi::CallbackInfo &info) {
  SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}

struct EnvInstanceData {
  Napi::Env env;
  // Base class per-environment constructor, used to check
  // if a JS object is a SWIG wrapper
  Napi::FunctionReference *SWIG_NAPI_ObjectWrapCtor;
  // Per-environment wrapper constructors, indexed by the number in
  // swig_type->clientdata
  Napi::FunctionReference **ctor;
  swig_module_info *swig_module;
  EnvInstanceData(Napi::Env, swig_module_info *);
  ~EnvInstanceData();
};

typedef size_t SWIG_NAPI_ClientData;

// Base class for all wrapped objects,
// used mostly when unwrapping unknown objects
template <typename SWIG_OBJ_WRAP>
class SWIG_NAPI_ObjectWrap_templ : public Napi::ObjectWrap<SWIG_OBJ_WRAP> {
  public:
    void *self;
    bool owned;
    size_t size;
    swig_type_info *info;
    SWIG_NAPI_ObjectWrap_templ(const Napi::CallbackInfo &info);
    SWIG_NAPI_ObjectWrap_templ(bool, const Napi::CallbackInfo &info) :
        Napi::ObjectWrap<SWIG_OBJ_WRAP>(info),
        self(nullptr),
        owned(true),
        size(0),
        info(nullptr)
        {}
    virtual ~SWIG_NAPI_ObjectWrap_templ() {};

    Napi::Value ToString(const Napi::CallbackInfo &info);
};

template <typename SWIG_OBJ_WRAP>
SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>::SWIG_NAPI_ObjectWrap_templ(const Napi::CallbackInfo &info) :
        Napi::ObjectWrap<SWIG_OBJ_WRAP>(info), size(0), info(nullptr) { 
  Napi::Env env = info.Env();
  if (info.Length() == 1 && info[0].IsExternal()) {
    // This constructor has been called internally from C++/SWIG
    // to wrap an already existing C++ object of unknown type in JS
    this->self = info[0].As<Napi::External<void>>().Data();
    this->owned = false;
  } else {
    SWIG_Error(SWIG_ERROR, "This constructor is not accessible from JS");
  }
  return;
  goto fail;
fail:
  return;
}

template <typename SWIG_OBJ_WRAP>
Napi::Value SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>::ToString(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  static char repr[128];
  const char *name = SWIG_TypePrettyName(this->info);
  snprintf(repr, sizeof(repr), "{SwigObject %s (%s) at %p %s}",
    this->info ? this->info->name : "unknown",
    name ? name : "unknown",
    this->self,
    this->owned ? "[owned]" : "[copy]");
  return Napi::String::New(env, repr);
}

class SWIG_NAPI_ObjectWrap_inst : public SWIG_NAPI_ObjectWrap_templ<SWIG_NAPI_ObjectWrap_inst> {
public:
  using SWIG_NAPI_ObjectWrap_templ::SWIG_NAPI_ObjectWrap_templ;
  static Napi::Function GetClass(Napi::Env);
  static void GetMembers(
    Napi::Env,
    std::map<std::string, SWIG_NAPI_ObjectWrap_templ::PropertyDescriptor> &,
    std::map<std::string, SWIG_NAPI_ObjectWrap_templ::PropertyDescriptor> &
  );
};

void SWIG_NAPI_ObjectWrap_inst::GetMembers(
        Napi::Env env,
        std::map<std::string, SWIG_NAPI_ObjectWrap_templ::PropertyDescriptor> &members,
        std::map<std::string, SWIG_NAPI_ObjectWrap_templ::PropertyDescriptor> &
) {
  members.erase("toString");
  members.insert({"toString", SWIG_NAPI_ObjectWrap_templ::InstanceMethod("toString", &SWIG_NAPI_ObjectWrap_templ::ToString)});
}

Napi::Function SWIG_NAPI_ObjectWrap_inst::GetClass(Napi::Env env) {
  return Napi::ObjectWrap<SWIG_NAPI_ObjectWrap_inst>::DefineClass(env, "SwigObject", {});
}

SWIGRUNTIME int SWIG_NAPI_ConvertInstancePtr(Napi::Object objRef, void **ptr, swig_type_info *info, int flags) {
  SWIG_NAPI_ObjectWrap_inst *ow;
  Napi::Env env = objRef.Env();
  if(!objRef.IsObject()) return SWIG_ERROR;

  // Check if this is a SWIG wrapper
  Napi::FunctionReference *ctor = env.GetInstanceData<EnvInstanceData>()->SWIG_NAPI_ObjectWrapCtor;
  bool instanceOf;
  NAPI_CHECK_RESULT(objRef.InstanceOf(ctor->Value()), instanceOf);
  if (!instanceOf) {
    return SWIG_TypeError;
  }

  ow = Napi::ObjectWrap<SWIG_NAPI_ObjectWrap_inst>::Unwrap(objRef);

  // Now check if the SWIG type is compatible unless the types match exactly or the type is unknown
  if(info && ow->info != info && ow->info != nullptr) {
    swig_cast_info *tc = SWIG_TypeCheckStruct(ow->info, info);
    if (!tc && ow->info->name) {
      tc = SWIG_TypeCheck(ow->info->name, info);
    }
    bool type_valid = tc != 0;
    if(!type_valid) {
      return SWIG_TypeError;
    }
    int newmemory = 0;
    *ptr = SWIG_TypeCast(tc, ow->self, &newmemory);
    assert(!newmemory); /* newmemory handling not yet implemented */
  } else {
    *ptr = ow->self;
  }

  if (((flags & SWIG_POINTER_RELEASE) == SWIG_POINTER_RELEASE) && !ow->owned) {
    return SWIG_ERROR_RELEASE_NOT_OWNED;
  } else {
    if (flags & SWIG_POINTER_DISOWN) {
      ow->owned = false;
    }
    if (flags & SWIG_POINTER_CLEAR) {
      ow->self = nullptr;
    }
  }
  return SWIG_OK;
  goto fail;
fail:
  return SWIG_ERROR;
}


SWIGRUNTIME int SWIG_NAPI_GetInstancePtr(Napi::Value valRef, void **ptr) {
  SWIG_NAPI_ObjectWrap_inst *ow;
  if(!valRef.IsObject()) {
    return SWIG_TypeError;
  }
  Napi::Object objRef;
  NAPI_CHECK_RESULT(valRef.ToObject(), objRef);
  ow = Napi::ObjectWrap<SWIG_NAPI_ObjectWrap_inst>::Unwrap(objRef);

  if(ow->self == nullptr) {
    return SWIG_ERROR;
  }

  *ptr = ow->self;
  return SWIG_OK;
  goto fail;
fail:
  return SWIG_ERROR;
}


SWIGRUNTIME int SWIG_NAPI_ConvertPtr(Napi::Value valRef, void **ptr, swig_type_info *info, int flags) {
  // special case: JavaScript null => C NULL pointer
  if (valRef.IsNull()) {
    *ptr=0;
    return (flags & SWIG_POINTER_NO_NULL) ? SWIG_NullReferenceError : SWIG_OK;
  }

  if (!valRef.IsObject()) {
    return SWIG_TypeError;
  }

  Napi::Object objRef;
  NAPI_CHECK_RESULT(valRef.ToObject(), objRef);
  return SWIG_NAPI_ConvertInstancePtr(objRef, ptr, info, flags);
  goto fail;
fail:
  return SWIG_ERROR;
}

SWIGRUNTIME Napi::Value SWIG_NAPI_NewPointerObj(Napi::Env env, void *ptr, swig_type_info *info, int flags) {
  Napi::External<void> native;
  Napi::FunctionReference *ctor;

  if (ptr == nullptr) {
    return env.Null();
  }
  native = Napi::External<void>::New(env, ptr);

  size_t *idx = info != nullptr ?
        reinterpret_cast<SWIG_NAPI_ClientData *>(info->clientdata) :
        nullptr;
  if (idx == nullptr) {
    // This type does not have a dedicated wrapper
    ctor = env.GetInstanceData<EnvInstanceData>()->SWIG_NAPI_ObjectWrapCtor;
  } else {
    ctor = env.GetInstanceData<EnvInstanceData>()->ctor[*idx];
  }

  Napi::Value wrapped;
  NAPI_CHECK_RESULT(ctor->New({native}), wrapped);

  // Preserve the type even if using the generic wrapper
  if (idx == nullptr && info != nullptr) {
    Napi::Object obj;
    NAPI_CHECK_RESULT(wrapped.ToObject(), obj);
    Napi::ObjectWrap<SWIG_NAPI_ObjectWrap_inst>::Unwrap(obj)->info = info;
  }

  if ((flags & SWIG_POINTER_OWN) == SWIG_POINTER_OWN) {
    Napi::Object obj;
    NAPI_CHECK_RESULT(wrapped.ToObject(), obj);
    Napi::ObjectWrap<SWIG_NAPI_ObjectWrap_inst>::Unwrap(obj)->owned = true;
  }

  return wrapped;
  goto fail;
fail:
  return Napi::Value();
}

#define SWIG_ConvertPtr(obj, ptr, info, flags)          SWIG_NAPI_ConvertPtr(obj, ptr, info, flags)
#define SWIG_NewPointerObj(ptr, info, flags)            SWIG_NAPI_NewPointerObj(env, ptr, info, flags)

#define SWIG_ConvertInstance(obj, pptr, type, flags)    SWIG_NAPI_ConvertInstancePtr(obj, pptr, type, flags)
#define SWIG_NewInstanceObj(thisvalue, type, flags)     SWIG_NAPI_NewPointerObj(env, thisvalue, type, flags)

#define SWIG_ConvertFunctionPtr(obj, pptr, type)        SWIG_NAPI_ConvertPtr(obj, pptr, type, 0)
#define SWIG_NewFunctionPtrObj(ptr, type)               SWIG_NAPI_NewPointerObj(env, ptr, type, 0)

#define SWIG_GetInstancePtr(obj, ptr)                   SWIG_NAPI_GetInstancePtr(obj, ptr)

SWIGRUNTIME Napi::Value _SWIG_NAPI_wrap_equals(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  void *arg1 = (void *) 0 ;
  void *arg2 = (void *) 0 ;
  bool result;
  int res1;
  int res2;

  if(info.Length() != 1) SWIG_Error(SWIG_ERROR, "Illegal number of arguments for equals.");

  res1 = SWIG_GetInstancePtr(info.This(), &arg1);
  if (!SWIG_IsOK(res1)) {
    SWIG_Error(SWIG_ERROR, "Could not get pointer from 'this' object for equals.");
  }
  res2 = SWIG_GetInstancePtr(info[0], &arg2);
  if (!SWIG_IsOK(res2)) {
    SWIG_Error(SWIG_ArgError(res2), " in method '" "equals" "', argument " "1"" of type '" "void *""'");
  }

  result = (bool)(arg1 == arg2);
  jsresult = Napi::Boolean::New(env, result);

  return jsresult;
  goto fail;
fail:
  return Napi::Value();
}

SWIGRUNTIME Napi::Value _wrap_getCPtr(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  void *arg1 = (void *) 0 ;
  long result;
  int res1;

  res1 = SWIG_GetInstancePtr(info.This(), &arg1);
  if (!SWIG_IsOK(res1)) {
    SWIG_Error(SWIG_ArgError(res1), " in method '" "getCPtr" "', argument " "1"" of type '" "void *""'");
  }

  result = (long)arg1;
  jsresult = Napi::Number::New(env, result);

  return jsresult;
  goto fail;
fail:
  return Napi::Value();
}


/* ---------------------------------------------------------------------------
 * PackedData object
 * (objects visible to JS that do not have a dedicated wrapper but must preserve type)
 * ---------------------------------------------------------------------------*/

SWIGRUNTIME
Napi::Value SWIG_NAPI_NewPackedObj(Napi::Env env, void *data, size_t size, swig_type_info *type) {
  void *data_copy = new uint8_t[size];
  memcpy(data_copy, data, size);
  Napi::Value val = SWIG_NAPI_NewPointerObj(env, data_copy, type, SWIG_POINTER_OWN);
  Napi::Object obj;
  if (val.IsEmpty()) goto fail;

  NAPI_CHECK_RESULT(val.ToObject(), obj);
  Napi::ObjectWrap<SWIG_NAPI_ObjectWrap_inst>::Unwrap(obj)->size = size;

fail:
  return val;
}

SWIGRUNTIME
int SWIG_NAPI_ConvertPacked(Napi::Value valRef, void *ptr, size_t size, swig_type_info *type) {
  void *tmp;
  if (!SWIG_IsOK(SWIG_NAPI_ConvertPtr(valRef, &tmp, type, 0))) {
    return SWIG_ERROR;
  }
  memcpy(ptr, tmp, size);
  return SWIG_OK;
}

#define SWIG_ConvertMember(obj, ptr, sz, ty)            SWIG_NAPI_ConvertPacked(obj, ptr, sz, ty)
#define SWIG_NewMemberObj(ptr, sz, type)                SWIG_NAPI_NewPackedObj(env, ptr, sz, type)


/* ---------------------------------------------------------------------------
 * Support for IN/OUTPUT typemaps (see Lib/typemaps/inoutlist.swg)
 *
 * ---------------------------------------------------------------------------*/

SWIGRUNTIME

Napi::Value SWIG_NAPI_AppendOutput(Napi::Env env, Napi::Value result, Napi::Value obj) {
  if (result.IsUndefined()) {
    result = Napi::Array::New(env);
  } else if (!result.IsArray()) {
    Napi::Array tmparr = Napi::Array::New(env);
    tmparr.Set(static_cast<uint32_t>(0), result);
    result = tmparr;
  }

  Napi::Array arr = result.As<Napi::Array>();
  arr.Set(arr.Length(), obj);
  return arr;
}


/* -------- TYPES TABLE (BEGIN) -------- */

#define SWIGTYPE_p_ByteArray swig_types[0]
#define SWIGTYPE_p_HARDWARE_INFO swig_types[1]
#define SWIGTYPE_p_I32Array swig_types[2]
#define SWIGTYPE_p_LIN_MSG_ARRAY swig_types[3]
#define SWIGTYPE_p_U32Array swig_types[4]
#define SWIGTYPE_p_UINT8P swig_types[5]
#define SWIGTYPE_p__DEVICE_INFO swig_types[6]
#define SWIGTYPE_p__LIN_EX_MSG swig_types[7]
#define SWIGTYPE_p___int64 swig_types[8]
#define SWIGTYPE_p_char swig_types[9]
#define SWIGTYPE_p_float swig_types[10]
#define SWIGTYPE_p_int swig_types[11]
#define SWIGTYPE_p_long swig_types[12]
#define SWIGTYPE_p_long_long swig_types[13]
#define SWIGTYPE_p_p_char swig_types[14]
#define SWIGTYPE_p_p_unsigned_long swig_types[15]
#define SWIGTYPE_p_short swig_types[16]
#define SWIGTYPE_p_signed___int64 swig_types[17]
#define SWIGTYPE_p_signed_char swig_types[18]
#define SWIGTYPE_p_unsigned___int64 swig_types[19]
#define SWIGTYPE_p_unsigned_char swig_types[20]
#define SWIGTYPE_p_unsigned_int swig_types[21]
#define SWIGTYPE_p_unsigned_long swig_types[22]
#define SWIGTYPE_p_unsigned_long_long swig_types[23]
#define SWIGTYPE_p_unsigned_short swig_types[24]
static swig_type_info *swig_types[26];
static swig_module_info swig_module = {swig_types, 25, 0, 0, 0, 0};
#define SWIG_TypeQuery(name) SWIG_TypeQueryModule(&swig_module, &swig_module, name)
#define SWIG_MangledTypeQuery(name) SWIG_MangledTypeQueryModule(&swig_module, &swig_module, name)

/* -------- TYPES TABLE (END) -------- */



#ifdef __cplusplus
#include <utility>
/* SwigValueWrapper is described in swig.swg */
template<typename T> class SwigValueWrapper {
  struct SwigSmartPointer {
    T *ptr;
    SwigSmartPointer(T *p) : ptr(p) { }
    ~SwigSmartPointer() { delete ptr; }
    SwigSmartPointer& operator=(SwigSmartPointer& rhs) { T* oldptr = ptr; ptr = 0; delete oldptr; ptr = rhs.ptr; rhs.ptr = 0; return *this; }
    void reset(T *p) { T* oldptr = ptr; ptr = 0; delete oldptr; ptr = p; }
  } pointer;
  SwigValueWrapper& operator=(const SwigValueWrapper<T>& rhs);
  SwigValueWrapper(const SwigValueWrapper<T>& rhs);
public:
  SwigValueWrapper() : pointer(0) { }
  SwigValueWrapper& operator=(const T& t) { SwigSmartPointer tmp(new T(t)); pointer = tmp; return *this; }
#if __cplusplus >=201103L
  SwigValueWrapper& operator=(T&& t) { SwigSmartPointer tmp(new T(std::move(t))); pointer = tmp; return *this; }
  operator T&&() const { return std::move(*pointer.ptr); }
#else
  operator T&() const { return *pointer.ptr; }
#endif
  T *operator&() const { return pointer.ptr; }
  static void reset(SwigValueWrapper& t, T *p) { t.pointer.reset(p); }
};

/*
 * SwigValueInit() is a generic initialisation solution as the following approach:
 * 
 *       T c_result = T();
 * 
 * doesn't compile for all types for example:
 * 
 *       unsigned int c_result = unsigned int();
 */
template <typename T> T SwigValueInit() {
  return T();
}

#if __cplusplus >=201103L
# define SWIG_STD_MOVE(OBJ) std::move(OBJ)
#else
# define SWIG_STD_MOVE(OBJ) OBJ
#endif

#endif


#define SWIG_as_voidptr(a) const_cast< void * >(static_cast< const void * >(a)) 
#define SWIG_as_voidptrptr(a) ((void)SWIG_as_voidptr(*a),reinterpret_cast< void** >(a)) 


#include <stdexcept>


#include <assert.h>


#include <windows.h>
#include <stdlib.h>
#include "usb2lin_ex.h"
#include "usb_device.h"


#include <stdint.h>		// Use the C99 official header


typedef unsigned char UINT8P;

SWIGINTERN UINT8P *new_UINT8P(){
  return new unsigned char();
}

#include <limits.h>
#if !defined(SWIG_NO_LLONG_MAX)
# if !defined(LLONG_MAX) && defined(__GNUC__) && defined (__LONG_LONG_MAX__)
#   define LLONG_MAX __LONG_LONG_MAX__
#   define LLONG_MIN (-LLONG_MAX - 1LL)
#   define ULLONG_MAX (LLONG_MAX * 2ULL + 1ULL)
# endif
#endif


SWIGINTERN
int SWIG_AsVal_double (Napi::Value obj, double *val)
{
  if(!obj.IsNumber()) {
    return SWIG_TypeError;
  }

  if(val) {
    Napi::Number num;
    NAPI_CHECK_RESULT(obj.ToNumber(), num);
    *val = static_cast<double>(num.DoubleValue());
  }

  return SWIG_OK;
  goto fail;
fail:
  return SWIG_ERROR;
}


#include <float.h>


#include <math.h>


SWIGINTERNINLINE int
SWIG_CanCastAsInteger(double *d, double min, double max) {
  double x = *d;
  if ((min <= x && x <= max)) {
   double fx, cx, rd;
   errno = 0;
   fx = floor(x);
   cx = ceil(x);
   rd =  ((x - fx) < 0.5) ? fx : cx; /* simple rint */
   if ((errno == EDOM) || (errno == ERANGE)) {
     errno = 0;
   } else {
     double summ, reps, diff;
     if (rd < x) {
       diff = x - rd;
     } else if (rd > x) {
       diff = rd - x;
     } else {
       return 1;
     }
     summ = rd + x;
     reps = diff/summ;
     if (reps < 8*DBL_EPSILON) {
       *d = rd;
       return 1;
     }
   }
  }
  return 0;
}


SWIGINTERN
int SWIG_AsVal_unsigned_SS_long (Napi::Value obj, unsigned long *val)
{
  if(!obj.IsNumber()) {
    return SWIG_TypeError;
  }
  if (val) {
    Napi::Number num;
    NAPI_CHECK_RESULT(obj.ToNumber(), num);
    if (num.Int64Value() < 0) {
      return SWIG_TypeError;
    }
    *val = static_cast<unsigned long>(num.Int64Value());
  }
  return SWIG_OK;
  goto fail;
fail:
  return SWIG_ERROR;
}


SWIGINTERN int
SWIG_AsVal_unsigned_SS_char (Napi::Value obj, unsigned char *val)
{
  unsigned long v;
  int res = SWIG_AsVal_unsigned_SS_long (obj, &v);
  if (SWIG_IsOK(res)) {
    if ((v > UCHAR_MAX)) {
      return SWIG_OverflowError;
    } else {
      if (val) *val = static_cast< unsigned char >(v);
    }
  }  
  return res;
}

SWIGINTERN void UINT8P_assign(UINT8P *self,unsigned char value){
  *self = value;
}
SWIGINTERN unsigned char UINT8P_value(UINT8P *self){
  return *self;
}

SWIGINTERNINLINE Napi::Value
SWIG_From_unsigned_SS_char(Napi::Env env, unsigned char c)
{
  return Napi::Number::New(env, static_cast<double>(c));
}

SWIGINTERN unsigned char *UINT8P_cast(UINT8P *self){
  return self;
}
SWIGINTERN UINT8P *UINT8P_frompointer(unsigned char *t){
  return (UINT8P *) t;
}

typedef LIN_EX_MSG LIN_MSG_ARRAY;


#if defined(LLONG_MAX) && !defined(SWIG_LONG_LONG_AVAILABLE)
#  define SWIG_LONG_LONG_AVAILABLE
#endif


#ifdef SWIG_LONG_LONG_AVAILABLE
SWIGINTERN
int SWIG_AsVal_unsigned_SS_long_SS_long (Napi::Value obj, unsigned long long *val)
{
  if(!obj.IsNumber()) {
    return SWIG_TypeError;
  }
  if (obj.ToNumber().Int64Value() < 0) {
    return SWIG_TypeError;
  }
  if (val) {
    Napi::Number num;
    NAPI_CHECK_RESULT(obj.ToNumber(), num);
    *val = static_cast<unsigned long long>(num.Int64Value());
  }
  return SWIG_OK;
  goto fail;
fail:
  return SWIG_ERROR;
}
#endif


SWIGINTERNINLINE int
SWIG_AsVal_size_t (Napi::Value obj, size_t *val)
{
  int res = SWIG_TypeError;
#ifdef SWIG_LONG_LONG_AVAILABLE
  if (sizeof(size_t) <= sizeof(unsigned long)) {
#endif
    unsigned long v;
    res = SWIG_AsVal_unsigned_SS_long (obj, val ? &v : 0);
    if (SWIG_IsOK(res) && val) *val = static_cast< size_t >(v);
#ifdef SWIG_LONG_LONG_AVAILABLE
  } else if (sizeof(size_t) <= sizeof(unsigned long long)) {
    unsigned long long v;
    res = SWIG_AsVal_unsigned_SS_long_SS_long (obj, val ? &v : 0);
    if (SWIG_IsOK(res) && val) *val = static_cast< size_t >(v);
  }
#endif
  return res;
}

SWIGINTERN LIN_MSG_ARRAY *new_LIN_MSG_ARRAY(size_t nelements){
  return new LIN_EX_MSG[nelements]();
}
SWIGINTERN LIN_EX_MSG LIN_MSG_ARRAY_getitem(LIN_MSG_ARRAY *self,size_t index){
  return self[index];
}
SWIGINTERN void LIN_MSG_ARRAY_setitem(LIN_MSG_ARRAY *self,size_t index,LIN_EX_MSG value){
  self[index] = value;
}
SWIGINTERN LIN_EX_MSG *LIN_MSG_ARRAY_cast(LIN_MSG_ARRAY *self){
  return self;
}
SWIGINTERN LIN_MSG_ARRAY *LIN_MSG_ARRAY_frompointer(LIN_EX_MSG *t){
  return (LIN_MSG_ARRAY *) t;
}

typedef int I32Array;

SWIGINTERN I32Array *new_I32Array(size_t nelements){
  return new int[nelements]();
}
SWIGINTERN int I32Array_getitem(I32Array *self,size_t index){
  return self[index];
}

SWIGINTERN
Napi::Value SWIG_From_int(Napi::Env env, int val)
{
  return Napi::Number::New(env, val);
}


SWIGINTERN
int SWIG_AsVal_int (Napi::Value valRef, int* val)
{
  if (!valRef.IsNumber()) {
    return SWIG_TypeError;
  }
  if (val) {
    Napi::Number num;
    NAPI_CHECK_RESULT(valRef.ToNumber(), num);
    *val = static_cast<int>(num.Int32Value());
  }

  return SWIG_OK;
  goto fail;
fail:
  return SWIG_ERROR;
}

SWIGINTERN void I32Array_setitem(I32Array *self,size_t index,int value){
  self[index] = value;
}
SWIGINTERN int *I32Array_cast(I32Array *self){
  return self;
}
SWIGINTERN I32Array *I32Array_frompointer(int *t){
  return (I32Array *) t;
}

typedef unsigned int U32Array;

SWIGINTERN U32Array *new_U32Array(size_t nelements){
  return new unsigned int[nelements]();
}
SWIGINTERN unsigned int U32Array_getitem(U32Array *self,size_t index){
  return self[index];
}

SWIGINTERN
Napi::Value SWIG_From_unsigned_SS_int(Napi::Env env, unsigned int val)
{
  return Napi::Number::New(env, val);
}


SWIGINTERN
int SWIG_AsVal_unsigned_SS_int (Napi::Value valRef, unsigned int* val)
{
  if (!valRef.IsNumber()) {
    return SWIG_TypeError;
  }
  if (val) {
    Napi::Number num;
    NAPI_CHECK_RESULT(valRef.ToNumber(), num);
    if (num.Int64Value() < 0) {
      return SWIG_TypeError;
    }
    *val = static_cast<unsigned int>(num.Uint32Value());
  }

  return SWIG_OK;
  goto fail;
fail:
  return SWIG_ERROR;
}

SWIGINTERN void U32Array_setitem(U32Array *self,size_t index,unsigned int value){
  self[index] = value;
}
SWIGINTERN unsigned int *U32Array_cast(U32Array *self){
  return self;
}
SWIGINTERN U32Array *U32Array_frompointer(unsigned int *t){
  return (U32Array *) t;
}

typedef unsigned char ByteArray;

SWIGINTERN ByteArray *new_ByteArray(size_t nelements){
  return new unsigned char[nelements]();
}
SWIGINTERN unsigned char ByteArray_getitem(ByteArray *self,size_t index){
  return self[index];
}
SWIGINTERN void ByteArray_setitem(ByteArray *self,size_t index,unsigned char value){
  self[index] = value;
}
SWIGINTERN unsigned char *ByteArray_cast(ByteArray *self){
  return self;
}
SWIGINTERN ByteArray *ByteArray_frompointer(unsigned char *t){
  return (ByteArray *) t;
}

SWIGINTERN swig_type_info*
SWIG_pchar_descriptor(void)
{
  static int init = 0;
  static swig_type_info* info = 0;
  if (!init) {
    info = SWIG_TypeQuery("_p_char");
    init = 1;
  }
  return info;
}


SWIGINTERN int
SWIG_AsCharPtrAndSize(Napi::Value valRef, char** cptr, size_t* psize, int *alloc)
{
  if(valRef.IsString()) {
    Napi::String js_str;
    NAPI_CHECK_RESULT(valRef.ToString(), js_str);

    std::string str = js_str.Utf8Value();
    size_t len = str.size() + 1;
    char* cstr = (char*) (new char[len]());
    memcpy(cstr, str.data(), len);
    
    if(alloc) *alloc = SWIG_NEWOBJ;
    if(psize) *psize = len;
    if(cptr) *cptr = cstr;
    
    return SWIG_OK;
  } else {
    if(valRef.IsObject()) {
      swig_type_info* pchar_descriptor = SWIG_pchar_descriptor();
      Napi::Object obj;
      NAPI_CHECK_RESULT(valRef.ToObject(), obj);
      // try if the object is a wrapped char[]
      if (pchar_descriptor) {
        void* vptr = 0;
        if (SWIG_ConvertPtr(obj, &vptr, pchar_descriptor, 0) == SWIG_OK) {
          if (cptr) *cptr = (char *) vptr;
          if (psize) *psize = vptr ? (strlen((char *)vptr) + 1) : 0;
          if (alloc) *alloc = SWIG_OLDOBJ;
          return SWIG_OK;
        }
      }
    }
  }
  goto fail;
fail:
  return SWIG_TypeError;
}





SWIGINTERN int
SWIG_AsCharArray(Napi::Value obj, char *val, size_t size)
{ 
  char* cptr = 0; size_t csize = 0; int alloc = SWIG_OLDOBJ;
  int res = SWIG_AsCharPtrAndSize(obj, &cptr, &csize, &alloc);
  if (SWIG_IsOK(res)) {
    /* special case of single char conversion when we don't need space for NUL */
    if (size == 1 && csize == 2 && cptr && !cptr[1]) --csize;
    if (csize <= size) {
      if (val) {
	if (csize) memcpy(val, cptr, csize*sizeof(char));
	if (csize < size) memset(val + csize, 0, (size - csize)*sizeof(char));
      }
      if (alloc == SWIG_NEWOBJ) {
	delete[] cptr;
	res = SWIG_DelNewMask(res);
      }      
      return res;
    }
    if (alloc == SWIG_NEWOBJ) delete[] cptr;
  }
  return SWIG_TypeError;
}


SWIGINTERN
int SWIG_AsVal_long (Napi::Value obj, long* val)
{
  if (!obj.IsNumber()) {
    return SWIG_TypeError;
  }
  if (val) {
    Napi::Number num;
    NAPI_CHECK_RESULT(obj.ToNumber(), num);
    *val = static_cast<long>(num.Int64Value());
  }

  return SWIG_OK;
  goto fail;
fail:
  return SWIG_ERROR;
}


SWIGINTERN int
SWIG_AsVal_char (Napi::Value obj, char *val)
{    
  int res = SWIG_AsCharArray(obj, val, 1);
  if (!SWIG_IsOK(res)) {
    long v;
    res = SWIG_AddCast(SWIG_AsVal_long (obj, &v));
    if (SWIG_IsOK(res)) {
      if ((CHAR_MIN <= v) && (v <= CHAR_MAX)) {
	if (val) *val = static_cast< char >(v);
      } else {
	res = SWIG_OverflowError;
      }
    }
  }
  return res;
}


#ifdef SWIG_LONG_LONG_AVAILABLE
SWIGINTERN
Napi::Value SWIG_From_long_SS_long(Napi::Env env, long long val)
{
  return Napi::Number::New(env, val);
}
#endif


SWIGINTERNINLINE Napi::Value
SWIG_Env_FromCharPtrAndSize(Napi::Env env, const char* carray, size_t size)
{
  if (carray) {
    Napi::String js_str = Napi::String::New(env, carray, size);
    return js_str;
  } else {
    return env.Undefined();
  }
}


SWIGINTERN size_t
SWIG_strnlen(const char* s, size_t maxlen)
{
  const char *p;
  for (p = s; maxlen-- && *p; p++)
    ;
  return p - s;
}


void LoadDll(const char* path) {
  SetDllDirectory(path);
}



#define SWIG_NAPI_INIT xmlpp_initialize


// jsnapi_class_prologue_template
template <typename SWIG_OBJ_WRAP>
class _exports_UINT8P_templ : public SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP> {
public:
  _exports_UINT8P_templ(const Napi::CallbackInfo &);
_exports_UINT8P_templ(bool, const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_new_UINT8P(const Napi::CallbackInfo &);
virtual ~_exports_UINT8P_templ();
// jsnapi_class_method_declaration
Napi::Value _wrap_UINT8P_assign(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_UINT8P_value(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_UINT8P_cast(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
static Napi::Value _wrap_UINT8P_frompointer(const Napi::CallbackInfo &);
// jsnapi_class_epilogue_template
static void JS_veto_set_static_variable(const Napi::CallbackInfo &, const Napi::Value &);
void JS_veto_set_variable(const Napi::CallbackInfo &, const Napi::Value &);
};

template <typename SWIG_OBJ_WRAP>
void _exports_UINT8P_templ<SWIG_OBJ_WRAP>::JS_veto_set_static_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}

template <typename SWIG_OBJ_WRAP>
void _exports_UINT8P_templ<SWIG_OBJ_WRAP>::JS_veto_set_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}
// jsnapi_class_instance
class _exports_UINT8P_inst : public _exports_UINT8P_templ<_exports_UINT8P_inst> {
public:
  using _exports_UINT8P_templ::_exports_UINT8P_templ;
  virtual ~_exports_UINT8P_inst() {
    
  };
  static void GetMembers(
    Napi::Env,
    std::map<std::string, _exports_UINT8P_templ::PropertyDescriptor> &,
    std::map<std::string, _exports_UINT8P_templ::PropertyDescriptor> &
    );
  static Napi::Function GetClass(Napi::Env);
};
/* Class: UINT8P (_exports_UINT8P) */
// jsnapi_getclass
Napi::Function _exports_UINT8P_inst::GetClass(Napi::Env env) {
  std::map<std::string, _exports_UINT8P_templ::PropertyDescriptor> members, staticMembers;
  GetMembers(env, members, staticMembers);
  
  std::vector<_exports_UINT8P_inst::PropertyDescriptor> symbolTable;
  for (auto it = members.begin(); it != members.end(); it++)
  symbolTable.push_back(it->second);
  for (auto it = staticMembers.begin(); it != staticMembers.end(); it++)
  symbolTable.push_back(it->second);
  
  return Napi::ObjectWrap<_exports_UINT8P_inst>::DefineClass(env, "UINT8P", symbolTable);
}

void _exports_UINT8P_inst::GetMembers(
  Napi::Env env,
  std::map<std::string, _exports_UINT8P_templ::PropertyDescriptor> &members,
  std::map<std::string, _exports_UINT8P_templ::PropertyDescriptor> &staticMembers
  ) {
  std::map<std::string, SWIG_NAPI_ObjectWrap_templ<SWIG_NAPI_ObjectWrap_inst>::PropertyDescriptor> baseMembers, baseStaticMembers;
  SWIG_NAPI_ObjectWrap_inst::GetMembers(env, baseMembers, baseStaticMembers);
  members.insert(baseMembers.begin(), baseMembers.end());
  staticMembers.insert(staticMembers.begin(), staticMembers.end());
  
  /* register wrapper functions */
  // jsnapi_member_function_descriptor
  members.erase("assign");
  members.insert({
    "assign",
      _exports_UINT8P_templ::InstanceMethod("assign",
        &_exports_UINT8P_templ::_wrap_UINT8P_assign,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  // jsnapi_member_function_descriptor
  members.erase("value");
  members.insert({
    "value",
      _exports_UINT8P_templ::InstanceMethod("value",
        &_exports_UINT8P_templ::_wrap_UINT8P_value,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  // jsnapi_member_function_descriptor
  members.erase("cast");
  members.insert({
    "cast",
      _exports_UINT8P_templ::InstanceMethod("cast",
        &_exports_UINT8P_templ::_wrap_UINT8P_cast,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  
  /* add static class functions and variables */
  // jsnapi_register_static_function
  staticMembers.erase("frompointer");
  staticMembers.insert({
    "frompointer",
      StaticMethod("frompointer",
        &_exports_UINT8P_templ::_wrap_UINT8P_frompointer,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  
}
// jsnapi_class_prologue_template
template <typename SWIG_OBJ_WRAP>
class _exports_LIN_MSG_ARRAY_templ : public SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP> {
public:
  _exports_LIN_MSG_ARRAY_templ(const Napi::CallbackInfo &);
_exports_LIN_MSG_ARRAY_templ(bool, const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_new_LIN_MSG_ARRAY(const Napi::CallbackInfo &);
virtual ~_exports_LIN_MSG_ARRAY_templ();
// jsnapi_class_method_declaration
Napi::Value _wrap_LIN_MSG_ARRAY_getitem(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_LIN_MSG_ARRAY_setitem(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_LIN_MSG_ARRAY_cast(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
static Napi::Value _wrap_LIN_MSG_ARRAY_frompointer(const Napi::CallbackInfo &);
// jsnapi_class_epilogue_template
static void JS_veto_set_static_variable(const Napi::CallbackInfo &, const Napi::Value &);
void JS_veto_set_variable(const Napi::CallbackInfo &, const Napi::Value &);
};

template <typename SWIG_OBJ_WRAP>
void _exports_LIN_MSG_ARRAY_templ<SWIG_OBJ_WRAP>::JS_veto_set_static_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}

template <typename SWIG_OBJ_WRAP>
void _exports_LIN_MSG_ARRAY_templ<SWIG_OBJ_WRAP>::JS_veto_set_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}
// jsnapi_class_instance
class _exports_LIN_MSG_ARRAY_inst : public _exports_LIN_MSG_ARRAY_templ<_exports_LIN_MSG_ARRAY_inst> {
public:
  using _exports_LIN_MSG_ARRAY_templ::_exports_LIN_MSG_ARRAY_templ;
  virtual ~_exports_LIN_MSG_ARRAY_inst() {
    
  };
  static void GetMembers(
    Napi::Env,
    std::map<std::string, _exports_LIN_MSG_ARRAY_templ::PropertyDescriptor> &,
    std::map<std::string, _exports_LIN_MSG_ARRAY_templ::PropertyDescriptor> &
    );
  static Napi::Function GetClass(Napi::Env);
};
/* Class: LIN_MSG_ARRAY (_exports_LIN_MSG_ARRAY) */
// jsnapi_getclass
Napi::Function _exports_LIN_MSG_ARRAY_inst::GetClass(Napi::Env env) {
  std::map<std::string, _exports_LIN_MSG_ARRAY_templ::PropertyDescriptor> members, staticMembers;
  GetMembers(env, members, staticMembers);
  
  std::vector<_exports_LIN_MSG_ARRAY_inst::PropertyDescriptor> symbolTable;
  for (auto it = members.begin(); it != members.end(); it++)
  symbolTable.push_back(it->second);
  for (auto it = staticMembers.begin(); it != staticMembers.end(); it++)
  symbolTable.push_back(it->second);
  
  return Napi::ObjectWrap<_exports_LIN_MSG_ARRAY_inst>::DefineClass(env, "LIN_MSG_ARRAY", symbolTable);
}

void _exports_LIN_MSG_ARRAY_inst::GetMembers(
  Napi::Env env,
  std::map<std::string, _exports_LIN_MSG_ARRAY_templ::PropertyDescriptor> &members,
  std::map<std::string, _exports_LIN_MSG_ARRAY_templ::PropertyDescriptor> &staticMembers
  ) {
  std::map<std::string, SWIG_NAPI_ObjectWrap_templ<SWIG_NAPI_ObjectWrap_inst>::PropertyDescriptor> baseMembers, baseStaticMembers;
  SWIG_NAPI_ObjectWrap_inst::GetMembers(env, baseMembers, baseStaticMembers);
  members.insert(baseMembers.begin(), baseMembers.end());
  staticMembers.insert(staticMembers.begin(), staticMembers.end());
  
  /* register wrapper functions */
  // jsnapi_member_function_descriptor
  members.erase("getitem");
  members.insert({
    "getitem",
      _exports_LIN_MSG_ARRAY_templ::InstanceMethod("getitem",
        &_exports_LIN_MSG_ARRAY_templ::_wrap_LIN_MSG_ARRAY_getitem,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  // jsnapi_member_function_descriptor
  members.erase("setitem");
  members.insert({
    "setitem",
      _exports_LIN_MSG_ARRAY_templ::InstanceMethod("setitem",
        &_exports_LIN_MSG_ARRAY_templ::_wrap_LIN_MSG_ARRAY_setitem,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  // jsnapi_member_function_descriptor
  members.erase("cast");
  members.insert({
    "cast",
      _exports_LIN_MSG_ARRAY_templ::InstanceMethod("cast",
        &_exports_LIN_MSG_ARRAY_templ::_wrap_LIN_MSG_ARRAY_cast,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  
  /* add static class functions and variables */
  // jsnapi_register_static_function
  staticMembers.erase("frompointer");
  staticMembers.insert({
    "frompointer",
      StaticMethod("frompointer",
        &_exports_LIN_MSG_ARRAY_templ::_wrap_LIN_MSG_ARRAY_frompointer,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  
}
// jsnapi_class_prologue_template
template <typename SWIG_OBJ_WRAP>
class _exports_I32Array_templ : public SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP> {
public:
  _exports_I32Array_templ(const Napi::CallbackInfo &);
_exports_I32Array_templ(bool, const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_new_I32Array(const Napi::CallbackInfo &);
virtual ~_exports_I32Array_templ();
// jsnapi_class_method_declaration
Napi::Value _wrap_I32Array_getitem(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_I32Array_setitem(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_I32Array_cast(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
static Napi::Value _wrap_I32Array_frompointer(const Napi::CallbackInfo &);
// jsnapi_class_epilogue_template
static void JS_veto_set_static_variable(const Napi::CallbackInfo &, const Napi::Value &);
void JS_veto_set_variable(const Napi::CallbackInfo &, const Napi::Value &);
};

template <typename SWIG_OBJ_WRAP>
void _exports_I32Array_templ<SWIG_OBJ_WRAP>::JS_veto_set_static_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}

template <typename SWIG_OBJ_WRAP>
void _exports_I32Array_templ<SWIG_OBJ_WRAP>::JS_veto_set_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}
// jsnapi_class_instance
class _exports_I32Array_inst : public _exports_I32Array_templ<_exports_I32Array_inst> {
public:
  using _exports_I32Array_templ::_exports_I32Array_templ;
  virtual ~_exports_I32Array_inst() {
    
  };
  static void GetMembers(
    Napi::Env,
    std::map<std::string, _exports_I32Array_templ::PropertyDescriptor> &,
    std::map<std::string, _exports_I32Array_templ::PropertyDescriptor> &
    );
  static Napi::Function GetClass(Napi::Env);
};
/* Class: I32Array (_exports_I32Array) */
// jsnapi_getclass
Napi::Function _exports_I32Array_inst::GetClass(Napi::Env env) {
  std::map<std::string, _exports_I32Array_templ::PropertyDescriptor> members, staticMembers;
  GetMembers(env, members, staticMembers);
  
  std::vector<_exports_I32Array_inst::PropertyDescriptor> symbolTable;
  for (auto it = members.begin(); it != members.end(); it++)
  symbolTable.push_back(it->second);
  for (auto it = staticMembers.begin(); it != staticMembers.end(); it++)
  symbolTable.push_back(it->second);
  
  return Napi::ObjectWrap<_exports_I32Array_inst>::DefineClass(env, "I32Array", symbolTable);
}

void _exports_I32Array_inst::GetMembers(
  Napi::Env env,
  std::map<std::string, _exports_I32Array_templ::PropertyDescriptor> &members,
  std::map<std::string, _exports_I32Array_templ::PropertyDescriptor> &staticMembers
  ) {
  std::map<std::string, SWIG_NAPI_ObjectWrap_templ<SWIG_NAPI_ObjectWrap_inst>::PropertyDescriptor> baseMembers, baseStaticMembers;
  SWIG_NAPI_ObjectWrap_inst::GetMembers(env, baseMembers, baseStaticMembers);
  members.insert(baseMembers.begin(), baseMembers.end());
  staticMembers.insert(staticMembers.begin(), staticMembers.end());
  
  /* register wrapper functions */
  // jsnapi_member_function_descriptor
  members.erase("getitem");
  members.insert({
    "getitem",
      _exports_I32Array_templ::InstanceMethod("getitem",
        &_exports_I32Array_templ::_wrap_I32Array_getitem,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  // jsnapi_member_function_descriptor
  members.erase("setitem");
  members.insert({
    "setitem",
      _exports_I32Array_templ::InstanceMethod("setitem",
        &_exports_I32Array_templ::_wrap_I32Array_setitem,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  // jsnapi_member_function_descriptor
  members.erase("cast");
  members.insert({
    "cast",
      _exports_I32Array_templ::InstanceMethod("cast",
        &_exports_I32Array_templ::_wrap_I32Array_cast,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  
  /* add static class functions and variables */
  // jsnapi_register_static_function
  staticMembers.erase("frompointer");
  staticMembers.insert({
    "frompointer",
      StaticMethod("frompointer",
        &_exports_I32Array_templ::_wrap_I32Array_frompointer,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  
}
// jsnapi_class_prologue_template
template <typename SWIG_OBJ_WRAP>
class _exports_U32Array_templ : public SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP> {
public:
  _exports_U32Array_templ(const Napi::CallbackInfo &);
_exports_U32Array_templ(bool, const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_new_U32Array(const Napi::CallbackInfo &);
virtual ~_exports_U32Array_templ();
// jsnapi_class_method_declaration
Napi::Value _wrap_U32Array_getitem(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_U32Array_setitem(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_U32Array_cast(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
static Napi::Value _wrap_U32Array_frompointer(const Napi::CallbackInfo &);
// jsnapi_class_epilogue_template
static void JS_veto_set_static_variable(const Napi::CallbackInfo &, const Napi::Value &);
void JS_veto_set_variable(const Napi::CallbackInfo &, const Napi::Value &);
};

template <typename SWIG_OBJ_WRAP>
void _exports_U32Array_templ<SWIG_OBJ_WRAP>::JS_veto_set_static_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}

template <typename SWIG_OBJ_WRAP>
void _exports_U32Array_templ<SWIG_OBJ_WRAP>::JS_veto_set_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}
// jsnapi_class_instance
class _exports_U32Array_inst : public _exports_U32Array_templ<_exports_U32Array_inst> {
public:
  using _exports_U32Array_templ::_exports_U32Array_templ;
  virtual ~_exports_U32Array_inst() {
    
  };
  static void GetMembers(
    Napi::Env,
    std::map<std::string, _exports_U32Array_templ::PropertyDescriptor> &,
    std::map<std::string, _exports_U32Array_templ::PropertyDescriptor> &
    );
  static Napi::Function GetClass(Napi::Env);
};
/* Class: U32Array (_exports_U32Array) */
// jsnapi_getclass
Napi::Function _exports_U32Array_inst::GetClass(Napi::Env env) {
  std::map<std::string, _exports_U32Array_templ::PropertyDescriptor> members, staticMembers;
  GetMembers(env, members, staticMembers);
  
  std::vector<_exports_U32Array_inst::PropertyDescriptor> symbolTable;
  for (auto it = members.begin(); it != members.end(); it++)
  symbolTable.push_back(it->second);
  for (auto it = staticMembers.begin(); it != staticMembers.end(); it++)
  symbolTable.push_back(it->second);
  
  return Napi::ObjectWrap<_exports_U32Array_inst>::DefineClass(env, "U32Array", symbolTable);
}

void _exports_U32Array_inst::GetMembers(
  Napi::Env env,
  std::map<std::string, _exports_U32Array_templ::PropertyDescriptor> &members,
  std::map<std::string, _exports_U32Array_templ::PropertyDescriptor> &staticMembers
  ) {
  std::map<std::string, SWIG_NAPI_ObjectWrap_templ<SWIG_NAPI_ObjectWrap_inst>::PropertyDescriptor> baseMembers, baseStaticMembers;
  SWIG_NAPI_ObjectWrap_inst::GetMembers(env, baseMembers, baseStaticMembers);
  members.insert(baseMembers.begin(), baseMembers.end());
  staticMembers.insert(staticMembers.begin(), staticMembers.end());
  
  /* register wrapper functions */
  // jsnapi_member_function_descriptor
  members.erase("getitem");
  members.insert({
    "getitem",
      _exports_U32Array_templ::InstanceMethod("getitem",
        &_exports_U32Array_templ::_wrap_U32Array_getitem,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  // jsnapi_member_function_descriptor
  members.erase("setitem");
  members.insert({
    "setitem",
      _exports_U32Array_templ::InstanceMethod("setitem",
        &_exports_U32Array_templ::_wrap_U32Array_setitem,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  // jsnapi_member_function_descriptor
  members.erase("cast");
  members.insert({
    "cast",
      _exports_U32Array_templ::InstanceMethod("cast",
        &_exports_U32Array_templ::_wrap_U32Array_cast,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  
  /* add static class functions and variables */
  // jsnapi_register_static_function
  staticMembers.erase("frompointer");
  staticMembers.insert({
    "frompointer",
      StaticMethod("frompointer",
        &_exports_U32Array_templ::_wrap_U32Array_frompointer,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  
}
// jsnapi_class_prologue_template
template <typename SWIG_OBJ_WRAP>
class _exports_ByteArray_templ : public SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP> {
public:
  _exports_ByteArray_templ(const Napi::CallbackInfo &);
_exports_ByteArray_templ(bool, const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_new_ByteArray(const Napi::CallbackInfo &);
virtual ~_exports_ByteArray_templ();
// jsnapi_class_method_declaration
Napi::Value _wrap_ByteArray_getitem(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_ByteArray_setitem(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_ByteArray_cast(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
static Napi::Value _wrap_ByteArray_frompointer(const Napi::CallbackInfo &);
// jsnapi_class_epilogue_template
static void JS_veto_set_static_variable(const Napi::CallbackInfo &, const Napi::Value &);
void JS_veto_set_variable(const Napi::CallbackInfo &, const Napi::Value &);
};

template <typename SWIG_OBJ_WRAP>
void _exports_ByteArray_templ<SWIG_OBJ_WRAP>::JS_veto_set_static_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}

template <typename SWIG_OBJ_WRAP>
void _exports_ByteArray_templ<SWIG_OBJ_WRAP>::JS_veto_set_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}
// jsnapi_class_instance
class _exports_ByteArray_inst : public _exports_ByteArray_templ<_exports_ByteArray_inst> {
public:
  using _exports_ByteArray_templ::_exports_ByteArray_templ;
  virtual ~_exports_ByteArray_inst() {
    
  };
  static void GetMembers(
    Napi::Env,
    std::map<std::string, _exports_ByteArray_templ::PropertyDescriptor> &,
    std::map<std::string, _exports_ByteArray_templ::PropertyDescriptor> &
    );
  static Napi::Function GetClass(Napi::Env);
};
/* Class: ByteArray (_exports_ByteArray) */
// jsnapi_getclass
Napi::Function _exports_ByteArray_inst::GetClass(Napi::Env env) {
  std::map<std::string, _exports_ByteArray_templ::PropertyDescriptor> members, staticMembers;
  GetMembers(env, members, staticMembers);
  
  std::vector<_exports_ByteArray_inst::PropertyDescriptor> symbolTable;
  for (auto it = members.begin(); it != members.end(); it++)
  symbolTable.push_back(it->second);
  for (auto it = staticMembers.begin(); it != staticMembers.end(); it++)
  symbolTable.push_back(it->second);
  
  return Napi::ObjectWrap<_exports_ByteArray_inst>::DefineClass(env, "ByteArray", symbolTable);
}

void _exports_ByteArray_inst::GetMembers(
  Napi::Env env,
  std::map<std::string, _exports_ByteArray_templ::PropertyDescriptor> &members,
  std::map<std::string, _exports_ByteArray_templ::PropertyDescriptor> &staticMembers
  ) {
  std::map<std::string, SWIG_NAPI_ObjectWrap_templ<SWIG_NAPI_ObjectWrap_inst>::PropertyDescriptor> baseMembers, baseStaticMembers;
  SWIG_NAPI_ObjectWrap_inst::GetMembers(env, baseMembers, baseStaticMembers);
  members.insert(baseMembers.begin(), baseMembers.end());
  staticMembers.insert(staticMembers.begin(), staticMembers.end());
  
  /* register wrapper functions */
  // jsnapi_member_function_descriptor
  members.erase("getitem");
  members.insert({
    "getitem",
      _exports_ByteArray_templ::InstanceMethod("getitem",
        &_exports_ByteArray_templ::_wrap_ByteArray_getitem,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  // jsnapi_member_function_descriptor
  members.erase("setitem");
  members.insert({
    "setitem",
      _exports_ByteArray_templ::InstanceMethod("setitem",
        &_exports_ByteArray_templ::_wrap_ByteArray_setitem,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  // jsnapi_member_function_descriptor
  members.erase("cast");
  members.insert({
    "cast",
      _exports_ByteArray_templ::InstanceMethod("cast",
        &_exports_ByteArray_templ::_wrap_ByteArray_cast,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  
  /* add static class functions and variables */
  // jsnapi_register_static_function
  staticMembers.erase("frompointer");
  staticMembers.insert({
    "frompointer",
      StaticMethod("frompointer",
        &_exports_ByteArray_templ::_wrap_ByteArray_frompointer,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  
}
// jsnapi_class_prologue_template
template <typename SWIG_OBJ_WRAP>
class _exports_LIN_EX_MSG_templ : public SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP> {
public:
  _exports_LIN_EX_MSG_templ(const Napi::CallbackInfo &);
_exports_LIN_EX_MSG_templ(bool, const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_LIN_EX_MSG_Timestamp_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_LIN_EX_MSG_Timestamp_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_LIN_EX_MSG_MsgType_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_LIN_EX_MSG_MsgType_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_LIN_EX_MSG_CheckType_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_LIN_EX_MSG_CheckType_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_LIN_EX_MSG_DataLen_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_LIN_EX_MSG_DataLen_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_LIN_EX_MSG_Sync_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_LIN_EX_MSG_Sync_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_LIN_EX_MSG_PID_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_LIN_EX_MSG_PID_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_LIN_EX_MSG_Data_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_LIN_EX_MSG_Data_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_LIN_EX_MSG_Check_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_LIN_EX_MSG_Check_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_LIN_EX_MSG_BreakBits_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_LIN_EX_MSG_BreakBits_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_LIN_EX_MSG_Reserve1_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_LIN_EX_MSG_Reserve1_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_new_LIN_EX_MSG(const Napi::CallbackInfo &);
virtual ~_exports_LIN_EX_MSG_templ();
// jsnapi_class_epilogue_template
static void JS_veto_set_static_variable(const Napi::CallbackInfo &, const Napi::Value &);
void JS_veto_set_variable(const Napi::CallbackInfo &, const Napi::Value &);
};

template <typename SWIG_OBJ_WRAP>
void _exports_LIN_EX_MSG_templ<SWIG_OBJ_WRAP>::JS_veto_set_static_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}

template <typename SWIG_OBJ_WRAP>
void _exports_LIN_EX_MSG_templ<SWIG_OBJ_WRAP>::JS_veto_set_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}
// jsnapi_class_instance
class _exports_LIN_EX_MSG_inst : public _exports_LIN_EX_MSG_templ<_exports_LIN_EX_MSG_inst> {
public:
  using _exports_LIN_EX_MSG_templ::_exports_LIN_EX_MSG_templ;
  virtual ~_exports_LIN_EX_MSG_inst() {
    
  };
  static void GetMembers(
    Napi::Env,
    std::map<std::string, _exports_LIN_EX_MSG_templ::PropertyDescriptor> &,
    std::map<std::string, _exports_LIN_EX_MSG_templ::PropertyDescriptor> &
    );
  static Napi::Function GetClass(Napi::Env);
};
/* Class: LIN_EX_MSG (_exports_LIN_EX_MSG) */
// jsnapi_getclass
Napi::Function _exports_LIN_EX_MSG_inst::GetClass(Napi::Env env) {
  std::map<std::string, _exports_LIN_EX_MSG_templ::PropertyDescriptor> members, staticMembers;
  GetMembers(env, members, staticMembers);
  
  std::vector<_exports_LIN_EX_MSG_inst::PropertyDescriptor> symbolTable;
  for (auto it = members.begin(); it != members.end(); it++)
  symbolTable.push_back(it->second);
  for (auto it = staticMembers.begin(); it != staticMembers.end(); it++)
  symbolTable.push_back(it->second);
  
  return Napi::ObjectWrap<_exports_LIN_EX_MSG_inst>::DefineClass(env, "LIN_EX_MSG", symbolTable);
}

void _exports_LIN_EX_MSG_inst::GetMembers(
  Napi::Env env,
  std::map<std::string, _exports_LIN_EX_MSG_templ::PropertyDescriptor> &members,
  std::map<std::string, _exports_LIN_EX_MSG_templ::PropertyDescriptor> &staticMembers
  ) {
  std::map<std::string, SWIG_NAPI_ObjectWrap_templ<SWIG_NAPI_ObjectWrap_inst>::PropertyDescriptor> baseMembers, baseStaticMembers;
  SWIG_NAPI_ObjectWrap_inst::GetMembers(env, baseMembers, baseStaticMembers);
  members.insert(baseMembers.begin(), baseMembers.end());
  staticMembers.insert(staticMembers.begin(), staticMembers.end());
  
  /* register wrapper functions */
  // jsnapi_register_member_variable
  members.erase("Timestamp");
  members.insert({
    "Timestamp",
      _exports_LIN_EX_MSG_templ::InstanceAccessor("Timestamp",
        &_exports_LIN_EX_MSG_templ::_wrap_LIN_EX_MSG_Timestamp_get,
        &_exports_LIN_EX_MSG_templ::_wrap_LIN_EX_MSG_Timestamp_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("MsgType");
  members.insert({
    "MsgType",
      _exports_LIN_EX_MSG_templ::InstanceAccessor("MsgType",
        &_exports_LIN_EX_MSG_templ::_wrap_LIN_EX_MSG_MsgType_get,
        &_exports_LIN_EX_MSG_templ::_wrap_LIN_EX_MSG_MsgType_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("CheckType");
  members.insert({
    "CheckType",
      _exports_LIN_EX_MSG_templ::InstanceAccessor("CheckType",
        &_exports_LIN_EX_MSG_templ::_wrap_LIN_EX_MSG_CheckType_get,
        &_exports_LIN_EX_MSG_templ::_wrap_LIN_EX_MSG_CheckType_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("DataLen");
  members.insert({
    "DataLen",
      _exports_LIN_EX_MSG_templ::InstanceAccessor("DataLen",
        &_exports_LIN_EX_MSG_templ::_wrap_LIN_EX_MSG_DataLen_get,
        &_exports_LIN_EX_MSG_templ::_wrap_LIN_EX_MSG_DataLen_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("Sync");
  members.insert({
    "Sync",
      _exports_LIN_EX_MSG_templ::InstanceAccessor("Sync",
        &_exports_LIN_EX_MSG_templ::_wrap_LIN_EX_MSG_Sync_get,
        &_exports_LIN_EX_MSG_templ::_wrap_LIN_EX_MSG_Sync_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("PID");
  members.insert({
    "PID",
      _exports_LIN_EX_MSG_templ::InstanceAccessor("PID",
        &_exports_LIN_EX_MSG_templ::_wrap_LIN_EX_MSG_PID_get,
        &_exports_LIN_EX_MSG_templ::_wrap_LIN_EX_MSG_PID_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("Data");
  members.insert({
    "Data",
      _exports_LIN_EX_MSG_templ::InstanceAccessor("Data",
        &_exports_LIN_EX_MSG_templ::_wrap_LIN_EX_MSG_Data_get,
        &_exports_LIN_EX_MSG_templ::_wrap_LIN_EX_MSG_Data_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("Check");
  members.insert({
    "Check",
      _exports_LIN_EX_MSG_templ::InstanceAccessor("Check",
        &_exports_LIN_EX_MSG_templ::_wrap_LIN_EX_MSG_Check_get,
        &_exports_LIN_EX_MSG_templ::_wrap_LIN_EX_MSG_Check_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("BreakBits");
  members.insert({
    "BreakBits",
      _exports_LIN_EX_MSG_templ::InstanceAccessor("BreakBits",
        &_exports_LIN_EX_MSG_templ::_wrap_LIN_EX_MSG_BreakBits_get,
        &_exports_LIN_EX_MSG_templ::_wrap_LIN_EX_MSG_BreakBits_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("Reserve1");
  members.insert({
    "Reserve1",
      _exports_LIN_EX_MSG_templ::InstanceAccessor("Reserve1",
        &_exports_LIN_EX_MSG_templ::_wrap_LIN_EX_MSG_Reserve1_get,
        &_exports_LIN_EX_MSG_templ::_wrap_LIN_EX_MSG_Reserve1_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  
  /* add static class functions and variables */
  
}
// jsnapi_class_prologue_template
template <typename SWIG_OBJ_WRAP>
class _exports_DEVICE_INFO_templ : public SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP> {
public:
  _exports_DEVICE_INFO_templ(const Napi::CallbackInfo &);
_exports_DEVICE_INFO_templ(bool, const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_DEVICE_INFO_FirmwareName_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_DEVICE_INFO_FirmwareName_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_DEVICE_INFO_BuildDate_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_DEVICE_INFO_BuildDate_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_DEVICE_INFO_HardwareVersion_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_DEVICE_INFO_HardwareVersion_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_DEVICE_INFO_FirmwareVersion_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_DEVICE_INFO_FirmwareVersion_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_DEVICE_INFO_SerialNumber_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_DEVICE_INFO_SerialNumber_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_DEVICE_INFO_Functions_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_DEVICE_INFO_Functions_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_new_DEVICE_INFO(const Napi::CallbackInfo &);
virtual ~_exports_DEVICE_INFO_templ();
// jsnapi_class_epilogue_template
static void JS_veto_set_static_variable(const Napi::CallbackInfo &, const Napi::Value &);
void JS_veto_set_variable(const Napi::CallbackInfo &, const Napi::Value &);
};

template <typename SWIG_OBJ_WRAP>
void _exports_DEVICE_INFO_templ<SWIG_OBJ_WRAP>::JS_veto_set_static_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}

template <typename SWIG_OBJ_WRAP>
void _exports_DEVICE_INFO_templ<SWIG_OBJ_WRAP>::JS_veto_set_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}
// jsnapi_class_instance
class _exports_DEVICE_INFO_inst : public _exports_DEVICE_INFO_templ<_exports_DEVICE_INFO_inst> {
public:
  using _exports_DEVICE_INFO_templ::_exports_DEVICE_INFO_templ;
  virtual ~_exports_DEVICE_INFO_inst() {
    
  };
  static void GetMembers(
    Napi::Env,
    std::map<std::string, _exports_DEVICE_INFO_templ::PropertyDescriptor> &,
    std::map<std::string, _exports_DEVICE_INFO_templ::PropertyDescriptor> &
    );
  static Napi::Function GetClass(Napi::Env);
};
/* Class: DEVICE_INFO (_exports_DEVICE_INFO) */
// jsnapi_getclass
Napi::Function _exports_DEVICE_INFO_inst::GetClass(Napi::Env env) {
  std::map<std::string, _exports_DEVICE_INFO_templ::PropertyDescriptor> members, staticMembers;
  GetMembers(env, members, staticMembers);
  
  std::vector<_exports_DEVICE_INFO_inst::PropertyDescriptor> symbolTable;
  for (auto it = members.begin(); it != members.end(); it++)
  symbolTable.push_back(it->second);
  for (auto it = staticMembers.begin(); it != staticMembers.end(); it++)
  symbolTable.push_back(it->second);
  
  return Napi::ObjectWrap<_exports_DEVICE_INFO_inst>::DefineClass(env, "DEVICE_INFO", symbolTable);
}

void _exports_DEVICE_INFO_inst::GetMembers(
  Napi::Env env,
  std::map<std::string, _exports_DEVICE_INFO_templ::PropertyDescriptor> &members,
  std::map<std::string, _exports_DEVICE_INFO_templ::PropertyDescriptor> &staticMembers
  ) {
  std::map<std::string, SWIG_NAPI_ObjectWrap_templ<SWIG_NAPI_ObjectWrap_inst>::PropertyDescriptor> baseMembers, baseStaticMembers;
  SWIG_NAPI_ObjectWrap_inst::GetMembers(env, baseMembers, baseStaticMembers);
  members.insert(baseMembers.begin(), baseMembers.end());
  staticMembers.insert(staticMembers.begin(), staticMembers.end());
  
  /* register wrapper functions */
  // jsnapi_register_member_variable
  members.erase("FirmwareName");
  members.insert({
    "FirmwareName",
      _exports_DEVICE_INFO_templ::InstanceAccessor("FirmwareName",
        &_exports_DEVICE_INFO_templ::_wrap_DEVICE_INFO_FirmwareName_get,
        &_exports_DEVICE_INFO_templ::_wrap_DEVICE_INFO_FirmwareName_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("BuildDate");
  members.insert({
    "BuildDate",
      _exports_DEVICE_INFO_templ::InstanceAccessor("BuildDate",
        &_exports_DEVICE_INFO_templ::_wrap_DEVICE_INFO_BuildDate_get,
        &_exports_DEVICE_INFO_templ::_wrap_DEVICE_INFO_BuildDate_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("HardwareVersion");
  members.insert({
    "HardwareVersion",
      _exports_DEVICE_INFO_templ::InstanceAccessor("HardwareVersion",
        &_exports_DEVICE_INFO_templ::_wrap_DEVICE_INFO_HardwareVersion_get,
        &_exports_DEVICE_INFO_templ::_wrap_DEVICE_INFO_HardwareVersion_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("FirmwareVersion");
  members.insert({
    "FirmwareVersion",
      _exports_DEVICE_INFO_templ::InstanceAccessor("FirmwareVersion",
        &_exports_DEVICE_INFO_templ::_wrap_DEVICE_INFO_FirmwareVersion_get,
        &_exports_DEVICE_INFO_templ::_wrap_DEVICE_INFO_FirmwareVersion_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("SerialNumber");
  members.insert({
    "SerialNumber",
      _exports_DEVICE_INFO_templ::InstanceAccessor("SerialNumber",
        &_exports_DEVICE_INFO_templ::_wrap_DEVICE_INFO_SerialNumber_get,
        &_exports_DEVICE_INFO_templ::_wrap_DEVICE_INFO_SerialNumber_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("Functions");
  members.insert({
    "Functions",
      _exports_DEVICE_INFO_templ::InstanceAccessor("Functions",
        &_exports_DEVICE_INFO_templ::_wrap_DEVICE_INFO_Functions_get,
        &_exports_DEVICE_INFO_templ::_wrap_DEVICE_INFO_Functions_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  
  /* add static class functions and variables */
  
}
// jsnapi_class_prologue_template
template <typename SWIG_OBJ_WRAP>
class _exports_HARDWARE_INFO_templ : public SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP> {
public:
  _exports_HARDWARE_INFO_templ(const Napi::CallbackInfo &);
_exports_HARDWARE_INFO_templ(bool, const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_HARDWARE_INFO_McuModel_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_HARDWARE_INFO_McuModel_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_HARDWARE_INFO_ProductModel_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_HARDWARE_INFO_ProductModel_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_HARDWARE_INFO_Version_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_HARDWARE_INFO_Version_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_HARDWARE_INFO_CANChannelNum_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_HARDWARE_INFO_CANChannelNum_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_HARDWARE_INFO_LINChannelNum_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_HARDWARE_INFO_LINChannelNum_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_HARDWARE_INFO_PWMChannelNum_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_HARDWARE_INFO_PWMChannelNum_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_HARDWARE_INFO_HaveCANFD_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_HARDWARE_INFO_HaveCANFD_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_HARDWARE_INFO_DIChannelNum_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_HARDWARE_INFO_DIChannelNum_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_HARDWARE_INFO_DOChannelNum_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_HARDWARE_INFO_DOChannelNum_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_HARDWARE_INFO_HaveIsolation_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_HARDWARE_INFO_HaveIsolation_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_HARDWARE_INFO_ExPowerSupply_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_HARDWARE_INFO_ExPowerSupply_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_HARDWARE_INFO_IsOEM_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_HARDWARE_INFO_IsOEM_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_HARDWARE_INFO_EECapacity_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_HARDWARE_INFO_EECapacity_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_HARDWARE_INFO_SPIFlashCapacity_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_HARDWARE_INFO_SPIFlashCapacity_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_HARDWARE_INFO_TFCardSupport_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_HARDWARE_INFO_TFCardSupport_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_HARDWARE_INFO_ProductionDate_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_HARDWARE_INFO_ProductionDate_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_HARDWARE_INFO_USBControl_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_HARDWARE_INFO_USBControl_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_HARDWARE_INFO_SerialControl_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_HARDWARE_INFO_SerialControl_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_HARDWARE_INFO_EthControl_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_HARDWARE_INFO_EthControl_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_HARDWARE_INFO_VbatChannel_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_HARDWARE_INFO_VbatChannel_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_new_HARDWARE_INFO(const Napi::CallbackInfo &);
virtual ~_exports_HARDWARE_INFO_templ();
// jsnapi_class_epilogue_template
static void JS_veto_set_static_variable(const Napi::CallbackInfo &, const Napi::Value &);
void JS_veto_set_variable(const Napi::CallbackInfo &, const Napi::Value &);
};

template <typename SWIG_OBJ_WRAP>
void _exports_HARDWARE_INFO_templ<SWIG_OBJ_WRAP>::JS_veto_set_static_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}

template <typename SWIG_OBJ_WRAP>
void _exports_HARDWARE_INFO_templ<SWIG_OBJ_WRAP>::JS_veto_set_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}
// jsnapi_class_instance
class _exports_HARDWARE_INFO_inst : public _exports_HARDWARE_INFO_templ<_exports_HARDWARE_INFO_inst> {
public:
  using _exports_HARDWARE_INFO_templ::_exports_HARDWARE_INFO_templ;
  virtual ~_exports_HARDWARE_INFO_inst() {
    
  };
  static void GetMembers(
    Napi::Env,
    std::map<std::string, _exports_HARDWARE_INFO_templ::PropertyDescriptor> &,
    std::map<std::string, _exports_HARDWARE_INFO_templ::PropertyDescriptor> &
    );
  static Napi::Function GetClass(Napi::Env);
};
/* Class: HARDWARE_INFO (_exports_HARDWARE_INFO) */
// jsnapi_getclass
Napi::Function _exports_HARDWARE_INFO_inst::GetClass(Napi::Env env) {
  std::map<std::string, _exports_HARDWARE_INFO_templ::PropertyDescriptor> members, staticMembers;
  GetMembers(env, members, staticMembers);
  
  std::vector<_exports_HARDWARE_INFO_inst::PropertyDescriptor> symbolTable;
  for (auto it = members.begin(); it != members.end(); it++)
  symbolTable.push_back(it->second);
  for (auto it = staticMembers.begin(); it != staticMembers.end(); it++)
  symbolTable.push_back(it->second);
  
  return Napi::ObjectWrap<_exports_HARDWARE_INFO_inst>::DefineClass(env, "HARDWARE_INFO", symbolTable);
}

void _exports_HARDWARE_INFO_inst::GetMembers(
  Napi::Env env,
  std::map<std::string, _exports_HARDWARE_INFO_templ::PropertyDescriptor> &members,
  std::map<std::string, _exports_HARDWARE_INFO_templ::PropertyDescriptor> &staticMembers
  ) {
  std::map<std::string, SWIG_NAPI_ObjectWrap_templ<SWIG_NAPI_ObjectWrap_inst>::PropertyDescriptor> baseMembers, baseStaticMembers;
  SWIG_NAPI_ObjectWrap_inst::GetMembers(env, baseMembers, baseStaticMembers);
  members.insert(baseMembers.begin(), baseMembers.end());
  staticMembers.insert(staticMembers.begin(), staticMembers.end());
  
  /* register wrapper functions */
  // jsnapi_register_member_variable
  members.erase("McuModel");
  members.insert({
    "McuModel",
      _exports_HARDWARE_INFO_templ::InstanceAccessor("McuModel",
        &_exports_HARDWARE_INFO_templ::_wrap_HARDWARE_INFO_McuModel_get,
        &_exports_HARDWARE_INFO_templ::_wrap_HARDWARE_INFO_McuModel_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("ProductModel");
  members.insert({
    "ProductModel",
      _exports_HARDWARE_INFO_templ::InstanceAccessor("ProductModel",
        &_exports_HARDWARE_INFO_templ::_wrap_HARDWARE_INFO_ProductModel_get,
        &_exports_HARDWARE_INFO_templ::_wrap_HARDWARE_INFO_ProductModel_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("Version");
  members.insert({
    "Version",
      _exports_HARDWARE_INFO_templ::InstanceAccessor("Version",
        &_exports_HARDWARE_INFO_templ::_wrap_HARDWARE_INFO_Version_get,
        &_exports_HARDWARE_INFO_templ::_wrap_HARDWARE_INFO_Version_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("CANChannelNum");
  members.insert({
    "CANChannelNum",
      _exports_HARDWARE_INFO_templ::InstanceAccessor("CANChannelNum",
        &_exports_HARDWARE_INFO_templ::_wrap_HARDWARE_INFO_CANChannelNum_get,
        &_exports_HARDWARE_INFO_templ::_wrap_HARDWARE_INFO_CANChannelNum_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("LINChannelNum");
  members.insert({
    "LINChannelNum",
      _exports_HARDWARE_INFO_templ::InstanceAccessor("LINChannelNum",
        &_exports_HARDWARE_INFO_templ::_wrap_HARDWARE_INFO_LINChannelNum_get,
        &_exports_HARDWARE_INFO_templ::_wrap_HARDWARE_INFO_LINChannelNum_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("PWMChannelNum");
  members.insert({
    "PWMChannelNum",
      _exports_HARDWARE_INFO_templ::InstanceAccessor("PWMChannelNum",
        &_exports_HARDWARE_INFO_templ::_wrap_HARDWARE_INFO_PWMChannelNum_get,
        &_exports_HARDWARE_INFO_templ::_wrap_HARDWARE_INFO_PWMChannelNum_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("HaveCANFD");
  members.insert({
    "HaveCANFD",
      _exports_HARDWARE_INFO_templ::InstanceAccessor("HaveCANFD",
        &_exports_HARDWARE_INFO_templ::_wrap_HARDWARE_INFO_HaveCANFD_get,
        &_exports_HARDWARE_INFO_templ::_wrap_HARDWARE_INFO_HaveCANFD_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("DIChannelNum");
  members.insert({
    "DIChannelNum",
      _exports_HARDWARE_INFO_templ::InstanceAccessor("DIChannelNum",
        &_exports_HARDWARE_INFO_templ::_wrap_HARDWARE_INFO_DIChannelNum_get,
        &_exports_HARDWARE_INFO_templ::_wrap_HARDWARE_INFO_DIChannelNum_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("DOChannelNum");
  members.insert({
    "DOChannelNum",
      _exports_HARDWARE_INFO_templ::InstanceAccessor("DOChannelNum",
        &_exports_HARDWARE_INFO_templ::_wrap_HARDWARE_INFO_DOChannelNum_get,
        &_exports_HARDWARE_INFO_templ::_wrap_HARDWARE_INFO_DOChannelNum_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("HaveIsolation");
  members.insert({
    "HaveIsolation",
      _exports_HARDWARE_INFO_templ::InstanceAccessor("HaveIsolation",
        &_exports_HARDWARE_INFO_templ::_wrap_HARDWARE_INFO_HaveIsolation_get,
        &_exports_HARDWARE_INFO_templ::_wrap_HARDWARE_INFO_HaveIsolation_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("ExPowerSupply");
  members.insert({
    "ExPowerSupply",
      _exports_HARDWARE_INFO_templ::InstanceAccessor("ExPowerSupply",
        &_exports_HARDWARE_INFO_templ::_wrap_HARDWARE_INFO_ExPowerSupply_get,
        &_exports_HARDWARE_INFO_templ::_wrap_HARDWARE_INFO_ExPowerSupply_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("IsOEM");
  members.insert({
    "IsOEM",
      _exports_HARDWARE_INFO_templ::InstanceAccessor("IsOEM",
        &_exports_HARDWARE_INFO_templ::_wrap_HARDWARE_INFO_IsOEM_get,
        &_exports_HARDWARE_INFO_templ::_wrap_HARDWARE_INFO_IsOEM_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("EECapacity");
  members.insert({
    "EECapacity",
      _exports_HARDWARE_INFO_templ::InstanceAccessor("EECapacity",
        &_exports_HARDWARE_INFO_templ::_wrap_HARDWARE_INFO_EECapacity_get,
        &_exports_HARDWARE_INFO_templ::_wrap_HARDWARE_INFO_EECapacity_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("SPIFlashCapacity");
  members.insert({
    "SPIFlashCapacity",
      _exports_HARDWARE_INFO_templ::InstanceAccessor("SPIFlashCapacity",
        &_exports_HARDWARE_INFO_templ::_wrap_HARDWARE_INFO_SPIFlashCapacity_get,
        &_exports_HARDWARE_INFO_templ::_wrap_HARDWARE_INFO_SPIFlashCapacity_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("TFCardSupport");
  members.insert({
    "TFCardSupport",
      _exports_HARDWARE_INFO_templ::InstanceAccessor("TFCardSupport",
        &_exports_HARDWARE_INFO_templ::_wrap_HARDWARE_INFO_TFCardSupport_get,
        &_exports_HARDWARE_INFO_templ::_wrap_HARDWARE_INFO_TFCardSupport_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("ProductionDate");
  members.insert({
    "ProductionDate",
      _exports_HARDWARE_INFO_templ::InstanceAccessor("ProductionDate",
        &_exports_HARDWARE_INFO_templ::_wrap_HARDWARE_INFO_ProductionDate_get,
        &_exports_HARDWARE_INFO_templ::_wrap_HARDWARE_INFO_ProductionDate_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("USBControl");
  members.insert({
    "USBControl",
      _exports_HARDWARE_INFO_templ::InstanceAccessor("USBControl",
        &_exports_HARDWARE_INFO_templ::_wrap_HARDWARE_INFO_USBControl_get,
        &_exports_HARDWARE_INFO_templ::_wrap_HARDWARE_INFO_USBControl_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("SerialControl");
  members.insert({
    "SerialControl",
      _exports_HARDWARE_INFO_templ::InstanceAccessor("SerialControl",
        &_exports_HARDWARE_INFO_templ::_wrap_HARDWARE_INFO_SerialControl_get,
        &_exports_HARDWARE_INFO_templ::_wrap_HARDWARE_INFO_SerialControl_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("EthControl");
  members.insert({
    "EthControl",
      _exports_HARDWARE_INFO_templ::InstanceAccessor("EthControl",
        &_exports_HARDWARE_INFO_templ::_wrap_HARDWARE_INFO_EthControl_get,
        &_exports_HARDWARE_INFO_templ::_wrap_HARDWARE_INFO_EthControl_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("VbatChannel");
  members.insert({
    "VbatChannel",
      _exports_HARDWARE_INFO_templ::InstanceAccessor("VbatChannel",
        &_exports_HARDWARE_INFO_templ::_wrap_HARDWARE_INFO_VbatChannel_get,
        &_exports_HARDWARE_INFO_templ::_wrap_HARDWARE_INFO_VbatChannel_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  
  /* add static class functions and variables */
  
}





template <typename SWIG_OBJ_WRAP>
// js_ctor
// This is the main constructor
_exports_UINT8P_templ<SWIG_OBJ_WRAP>::_exports_UINT8P_templ(const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  Napi::Env env = info.Env();
  
  this->info = SWIGTYPE_p_UINT8P;
  if (info.Length() == 1 && info[0].IsExternal()) {
    // This constructor has been called internally from C++/SWIG
    // to wrap an already existing C++ object in JS
    this->self = info[0].As<Napi::External<void>>().Data();
    this->owned = false;
    return;
  }
  this->owned = true;
  
  UINT8P *result;
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_new_UINT8P.");
  }
  result = (UINT8P *)new_UINT8P();
  
  
  this->self = result;
  return;
  goto fail;
fail:
  return;
}

// This is the bypass constructor to be used from child classes
template <typename SWIG_OBJ_WRAP>
_exports_UINT8P_templ<SWIG_OBJ_WRAP>::_exports_UINT8P_templ(bool, const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  
}

SWIGINTERN void delete_UINT8P(UINT8P *self){
  delete self;
}

// js_dtoroverride
template <typename SWIG_OBJ_WRAP>
_exports_UINT8P_templ<SWIG_OBJ_WRAP>::~_exports_UINT8P_templ() {
  auto arg1 = reinterpret_cast<UINT8P *>(this->self);
  if (this->owned && arg1) {
    delete_UINT8P(arg1);
    this->self = nullptr;
  }
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_UINT8P_templ<SWIG_OBJ_WRAP>::_wrap_UINT8P_assign(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  UINT8P *arg1 = (UINT8P *) 0 ;
  unsigned char arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_UINT8P_assign.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_UINT8P, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "UINT8P_assign" "', argument " "1"" of type '" "UINT8P *""'"); 
  }
  arg1 = reinterpret_cast< UINT8P * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(info[0], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "UINT8P_assign" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);UINT8P_assign(arg1,arg2);
  jsresult = env.Undefined();
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_UINT8P_templ<SWIG_OBJ_WRAP>::_wrap_UINT8P_value(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  UINT8P *arg1 = (UINT8P *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char result;
  
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_UINT8P_value.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_UINT8P, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "UINT8P_value" "', argument " "1"" of type '" "UINT8P *""'"); 
  }
  arg1 = reinterpret_cast< UINT8P * >(argp1);result = (unsigned char)UINT8P_value(arg1);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_UINT8P_templ<SWIG_OBJ_WRAP>::_wrap_UINT8P_cast(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  UINT8P *arg1 = (UINT8P *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char *result = 0 ;
  
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_UINT8P_cast.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_UINT8P, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "UINT8P_cast" "', argument " "1"" of type '" "UINT8P *""'"); 
  }
  arg1 = reinterpret_cast< UINT8P * >(argp1);result = (unsigned char *)UINT8P_cast(arg1);
  jsresult = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_unsigned_char, 0 |  0 );
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_UINT8P_templ<SWIG_OBJ_WRAP>::_wrap_UINT8P_frompointer(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  unsigned char *arg1 = (unsigned char *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  UINT8P *result = 0 ;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_UINT8P_frompointer.");
  }
  
  res1 = SWIG_ConvertPtr(info[0], &argp1,SWIGTYPE_p_unsigned_char, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "UINT8P_frompointer" "', argument " "1"" of type '" "unsigned char *""'"); 
  }
  arg1 = reinterpret_cast< unsigned char * >(argp1);result = (UINT8P *)UINT8P_frompointer(arg1);
  jsresult = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_UINT8P, 0 |  0 );
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


template <typename SWIG_OBJ_WRAP>
// js_ctor
// This is the main constructor
_exports_LIN_MSG_ARRAY_templ<SWIG_OBJ_WRAP>::_exports_LIN_MSG_ARRAY_templ(const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  Napi::Env env = info.Env();
  
  this->info = SWIGTYPE_p_LIN_MSG_ARRAY;
  if (info.Length() == 1 && info[0].IsExternal()) {
    // This constructor has been called internally from C++/SWIG
    // to wrap an already existing C++ object in JS
    this->self = info[0].As<Napi::External<void>>().Data();
    this->owned = false;
    return;
  }
  this->owned = true;
  
  size_t arg1 ;
  size_t val1 ;
  int ecode1 = 0 ;
  LIN_MSG_ARRAY *result;
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_new_LIN_MSG_ARRAY.");
  }
  ecode1 = SWIG_AsVal_size_t(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "new_LIN_MSG_ARRAY" "', argument " "1"" of type '" "size_t""'");
  } 
  arg1 = static_cast< size_t >(val1);result = (LIN_MSG_ARRAY *)new_LIN_MSG_ARRAY(SWIG_STD_MOVE(arg1));
  
  
  
  this->self = result;
  return;
  goto fail;
fail:
  return;
}

// This is the bypass constructor to be used from child classes
template <typename SWIG_OBJ_WRAP>
_exports_LIN_MSG_ARRAY_templ<SWIG_OBJ_WRAP>::_exports_LIN_MSG_ARRAY_templ(bool, const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  
}

SWIGINTERN void delete_LIN_MSG_ARRAY(LIN_MSG_ARRAY *self){
  delete [] self;
}

// js_dtoroverride
template <typename SWIG_OBJ_WRAP>
_exports_LIN_MSG_ARRAY_templ<SWIG_OBJ_WRAP>::~_exports_LIN_MSG_ARRAY_templ() {
  auto arg1 = reinterpret_cast<LIN_MSG_ARRAY *>(this->self);
  if (this->owned && arg1) {
    delete_LIN_MSG_ARRAY(arg1);
    this->self = nullptr;
  }
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_LIN_MSG_ARRAY_templ<SWIG_OBJ_WRAP>::_wrap_LIN_MSG_ARRAY_getitem(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  LIN_MSG_ARRAY *arg1 = (LIN_MSG_ARRAY *) 0 ;
  size_t arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  size_t val2 ;
  int ecode2 = 0 ;
  LIN_EX_MSG result;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_MSG_ARRAY_getitem.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_LIN_MSG_ARRAY, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LIN_MSG_ARRAY_getitem" "', argument " "1"" of type '" "LIN_MSG_ARRAY *""'"); 
  }
  arg1 = reinterpret_cast< LIN_MSG_ARRAY * >(argp1);ecode2 = SWIG_AsVal_size_t(info[0], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_MSG_ARRAY_getitem" "', argument " "2"" of type '" "size_t""'");
  } 
  arg2 = static_cast< size_t >(val2);result = LIN_MSG_ARRAY_getitem(arg1,SWIG_STD_MOVE(arg2));
  jsresult = SWIG_NewPointerObj((new LIN_EX_MSG(result)), SWIGTYPE_p__LIN_EX_MSG, SWIG_POINTER_OWN |  0 );
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_LIN_MSG_ARRAY_templ<SWIG_OBJ_WRAP>::_wrap_LIN_MSG_ARRAY_setitem(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  LIN_MSG_ARRAY *arg1 = (LIN_MSG_ARRAY *) 0 ;
  size_t arg2 ;
  LIN_EX_MSG arg3 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  size_t val2 ;
  int ecode2 = 0 ;
  void *argp3 ;
  int res3 = 0 ;
  
  if(static_cast<int>(info.Length()) < 2 || static_cast<int>(info.Length()) > 2) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_MSG_ARRAY_setitem.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_LIN_MSG_ARRAY, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LIN_MSG_ARRAY_setitem" "', argument " "1"" of type '" "LIN_MSG_ARRAY *""'"); 
  }
  arg1 = reinterpret_cast< LIN_MSG_ARRAY * >(argp1);ecode2 = SWIG_AsVal_size_t(info[0], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_MSG_ARRAY_setitem" "', argument " "2"" of type '" "size_t""'");
  } 
  arg2 = static_cast< size_t >(val2);{
    {
      res3 = SWIG_ConvertPtr(info[1], &argp3, SWIGTYPE_p__LIN_EX_MSG,  0 );
      if (!SWIG_IsOK(res3)) {
        SWIG_exception_fail(SWIG_ArgError(res3), "in method '" "LIN_MSG_ARRAY_setitem" "', argument " "3"" of type '" "LIN_EX_MSG""'"); 
      }  
      if (!argp3) {
        SWIG_exception_fail(SWIG_ValueError, "invalid null reference " "in method '" "LIN_MSG_ARRAY_setitem" "', argument " "3"" of type '" "LIN_EX_MSG""'");
      } else {
        arg3 = *(reinterpret_cast< LIN_EX_MSG * >(argp3));
      }
    }
  }
  LIN_MSG_ARRAY_setitem(arg1,SWIG_STD_MOVE(arg2),SWIG_STD_MOVE(arg3));
  jsresult = env.Undefined();
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_LIN_MSG_ARRAY_templ<SWIG_OBJ_WRAP>::_wrap_LIN_MSG_ARRAY_cast(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  LIN_MSG_ARRAY *arg1 = (LIN_MSG_ARRAY *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  LIN_EX_MSG *result = 0 ;
  
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_MSG_ARRAY_cast.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_LIN_MSG_ARRAY, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LIN_MSG_ARRAY_cast" "', argument " "1"" of type '" "LIN_MSG_ARRAY *""'"); 
  }
  arg1 = reinterpret_cast< LIN_MSG_ARRAY * >(argp1);result = (LIN_EX_MSG *)LIN_MSG_ARRAY_cast(arg1);
  jsresult = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p__LIN_EX_MSG, 0 |  0 );
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_LIN_MSG_ARRAY_templ<SWIG_OBJ_WRAP>::_wrap_LIN_MSG_ARRAY_frompointer(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  LIN_EX_MSG *arg1 = (LIN_EX_MSG *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  LIN_MSG_ARRAY *result = 0 ;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_MSG_ARRAY_frompointer.");
  }
  
  res1 = SWIG_ConvertPtr(info[0], &argp1,SWIGTYPE_p__LIN_EX_MSG, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LIN_MSG_ARRAY_frompointer" "', argument " "1"" of type '" "LIN_EX_MSG *""'"); 
  }
  arg1 = reinterpret_cast< LIN_EX_MSG * >(argp1);result = (LIN_MSG_ARRAY *)LIN_MSG_ARRAY_frompointer(arg1);
  jsresult = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_LIN_MSG_ARRAY, 0 |  0 );
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


template <typename SWIG_OBJ_WRAP>
// js_ctor
// This is the main constructor
_exports_I32Array_templ<SWIG_OBJ_WRAP>::_exports_I32Array_templ(const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  Napi::Env env = info.Env();
  
  this->info = SWIGTYPE_p_I32Array;
  if (info.Length() == 1 && info[0].IsExternal()) {
    // This constructor has been called internally from C++/SWIG
    // to wrap an already existing C++ object in JS
    this->self = info[0].As<Napi::External<void>>().Data();
    this->owned = false;
    return;
  }
  this->owned = true;
  
  size_t arg1 ;
  size_t val1 ;
  int ecode1 = 0 ;
  I32Array *result;
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_new_I32Array.");
  }
  ecode1 = SWIG_AsVal_size_t(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "new_I32Array" "', argument " "1"" of type '" "size_t""'");
  } 
  arg1 = static_cast< size_t >(val1);result = (I32Array *)new_I32Array(SWIG_STD_MOVE(arg1));
  
  
  
  this->self = result;
  return;
  goto fail;
fail:
  return;
}

// This is the bypass constructor to be used from child classes
template <typename SWIG_OBJ_WRAP>
_exports_I32Array_templ<SWIG_OBJ_WRAP>::_exports_I32Array_templ(bool, const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  
}

SWIGINTERN void delete_I32Array(I32Array *self){
  delete [] self;
}

// js_dtoroverride
template <typename SWIG_OBJ_WRAP>
_exports_I32Array_templ<SWIG_OBJ_WRAP>::~_exports_I32Array_templ() {
  auto arg1 = reinterpret_cast<I32Array *>(this->self);
  if (this->owned && arg1) {
    delete_I32Array(arg1);
    this->self = nullptr;
  }
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_I32Array_templ<SWIG_OBJ_WRAP>::_wrap_I32Array_getitem(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  I32Array *arg1 = (I32Array *) 0 ;
  size_t arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  size_t val2 ;
  int ecode2 = 0 ;
  int result;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_I32Array_getitem.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_I32Array, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "I32Array_getitem" "', argument " "1"" of type '" "I32Array *""'"); 
  }
  arg1 = reinterpret_cast< I32Array * >(argp1);ecode2 = SWIG_AsVal_size_t(info[0], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "I32Array_getitem" "', argument " "2"" of type '" "size_t""'");
  } 
  arg2 = static_cast< size_t >(val2);result = (int)I32Array_getitem(arg1,SWIG_STD_MOVE(arg2));
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_I32Array_templ<SWIG_OBJ_WRAP>::_wrap_I32Array_setitem(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  I32Array *arg1 = (I32Array *) 0 ;
  size_t arg2 ;
  int arg3 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  size_t val2 ;
  int ecode2 = 0 ;
  int val3 ;
  int ecode3 = 0 ;
  
  if(static_cast<int>(info.Length()) < 2 || static_cast<int>(info.Length()) > 2) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_I32Array_setitem.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_I32Array, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "I32Array_setitem" "', argument " "1"" of type '" "I32Array *""'"); 
  }
  arg1 = reinterpret_cast< I32Array * >(argp1);ecode2 = SWIG_AsVal_size_t(info[0], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "I32Array_setitem" "', argument " "2"" of type '" "size_t""'");
  } 
  arg2 = static_cast< size_t >(val2);ecode3 = SWIG_AsVal_int(info[1], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "I32Array_setitem" "', argument " "3"" of type '" "int""'");
  } 
  arg3 = static_cast< int >(val3);I32Array_setitem(arg1,SWIG_STD_MOVE(arg2),arg3);
  jsresult = env.Undefined();
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_I32Array_templ<SWIG_OBJ_WRAP>::_wrap_I32Array_cast(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  I32Array *arg1 = (I32Array *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int *result = 0 ;
  
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_I32Array_cast.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_I32Array, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "I32Array_cast" "', argument " "1"" of type '" "I32Array *""'"); 
  }
  arg1 = reinterpret_cast< I32Array * >(argp1);result = (int *)I32Array_cast(arg1);
  jsresult = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_int, 0 |  0 );
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_I32Array_templ<SWIG_OBJ_WRAP>::_wrap_I32Array_frompointer(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  int *arg1 = (int *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  I32Array *result = 0 ;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_I32Array_frompointer.");
  }
  
  res1 = SWIG_ConvertPtr(info[0], &argp1,SWIGTYPE_p_int, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "I32Array_frompointer" "', argument " "1"" of type '" "int *""'"); 
  }
  arg1 = reinterpret_cast< int * >(argp1);result = (I32Array *)I32Array_frompointer(arg1);
  jsresult = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_I32Array, 0 |  0 );
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


template <typename SWIG_OBJ_WRAP>
// js_ctor
// This is the main constructor
_exports_U32Array_templ<SWIG_OBJ_WRAP>::_exports_U32Array_templ(const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  Napi::Env env = info.Env();
  
  this->info = SWIGTYPE_p_U32Array;
  if (info.Length() == 1 && info[0].IsExternal()) {
    // This constructor has been called internally from C++/SWIG
    // to wrap an already existing C++ object in JS
    this->self = info[0].As<Napi::External<void>>().Data();
    this->owned = false;
    return;
  }
  this->owned = true;
  
  size_t arg1 ;
  size_t val1 ;
  int ecode1 = 0 ;
  U32Array *result;
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_new_U32Array.");
  }
  ecode1 = SWIG_AsVal_size_t(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "new_U32Array" "', argument " "1"" of type '" "size_t""'");
  } 
  arg1 = static_cast< size_t >(val1);result = (U32Array *)new_U32Array(SWIG_STD_MOVE(arg1));
  
  
  
  this->self = result;
  return;
  goto fail;
fail:
  return;
}

// This is the bypass constructor to be used from child classes
template <typename SWIG_OBJ_WRAP>
_exports_U32Array_templ<SWIG_OBJ_WRAP>::_exports_U32Array_templ(bool, const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  
}

SWIGINTERN void delete_U32Array(U32Array *self){
  delete [] self;
}

// js_dtoroverride
template <typename SWIG_OBJ_WRAP>
_exports_U32Array_templ<SWIG_OBJ_WRAP>::~_exports_U32Array_templ() {
  auto arg1 = reinterpret_cast<U32Array *>(this->self);
  if (this->owned && arg1) {
    delete_U32Array(arg1);
    this->self = nullptr;
  }
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_U32Array_templ<SWIG_OBJ_WRAP>::_wrap_U32Array_getitem(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  U32Array *arg1 = (U32Array *) 0 ;
  size_t arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  size_t val2 ;
  int ecode2 = 0 ;
  unsigned int result;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_U32Array_getitem.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_U32Array, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "U32Array_getitem" "', argument " "1"" of type '" "U32Array *""'"); 
  }
  arg1 = reinterpret_cast< U32Array * >(argp1);ecode2 = SWIG_AsVal_size_t(info[0], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "U32Array_getitem" "', argument " "2"" of type '" "size_t""'");
  } 
  arg2 = static_cast< size_t >(val2);result = (unsigned int)U32Array_getitem(arg1,SWIG_STD_MOVE(arg2));
  jsresult = SWIG_From_unsigned_SS_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned int >(result));
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_U32Array_templ<SWIG_OBJ_WRAP>::_wrap_U32Array_setitem(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  U32Array *arg1 = (U32Array *) 0 ;
  size_t arg2 ;
  unsigned int arg3 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  size_t val2 ;
  int ecode2 = 0 ;
  unsigned int val3 ;
  int ecode3 = 0 ;
  
  if(static_cast<int>(info.Length()) < 2 || static_cast<int>(info.Length()) > 2) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_U32Array_setitem.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_U32Array, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "U32Array_setitem" "', argument " "1"" of type '" "U32Array *""'"); 
  }
  arg1 = reinterpret_cast< U32Array * >(argp1);ecode2 = SWIG_AsVal_size_t(info[0], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "U32Array_setitem" "', argument " "2"" of type '" "size_t""'");
  } 
  arg2 = static_cast< size_t >(val2);ecode3 = SWIG_AsVal_unsigned_SS_int(info[1], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "U32Array_setitem" "', argument " "3"" of type '" "unsigned int""'");
  } 
  arg3 = static_cast< unsigned int >(val3);U32Array_setitem(arg1,SWIG_STD_MOVE(arg2),arg3);
  jsresult = env.Undefined();
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_U32Array_templ<SWIG_OBJ_WRAP>::_wrap_U32Array_cast(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  U32Array *arg1 = (U32Array *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned int *result = 0 ;
  
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_U32Array_cast.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_U32Array, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "U32Array_cast" "', argument " "1"" of type '" "U32Array *""'"); 
  }
  arg1 = reinterpret_cast< U32Array * >(argp1);result = (unsigned int *)U32Array_cast(arg1);
  jsresult = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_unsigned_int, 0 |  0 );
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_U32Array_templ<SWIG_OBJ_WRAP>::_wrap_U32Array_frompointer(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  unsigned int *arg1 = (unsigned int *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  U32Array *result = 0 ;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_U32Array_frompointer.");
  }
  
  res1 = SWIG_ConvertPtr(info[0], &argp1,SWIGTYPE_p_unsigned_int, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "U32Array_frompointer" "', argument " "1"" of type '" "unsigned int *""'"); 
  }
  arg1 = reinterpret_cast< unsigned int * >(argp1);result = (U32Array *)U32Array_frompointer(arg1);
  jsresult = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_U32Array, 0 |  0 );
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


template <typename SWIG_OBJ_WRAP>
// js_ctor
// This is the main constructor
_exports_ByteArray_templ<SWIG_OBJ_WRAP>::_exports_ByteArray_templ(const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  Napi::Env env = info.Env();
  
  this->info = SWIGTYPE_p_ByteArray;
  if (info.Length() == 1 && info[0].IsExternal()) {
    // This constructor has been called internally from C++/SWIG
    // to wrap an already existing C++ object in JS
    this->self = info[0].As<Napi::External<void>>().Data();
    this->owned = false;
    return;
  }
  this->owned = true;
  
  size_t arg1 ;
  size_t val1 ;
  int ecode1 = 0 ;
  ByteArray *result;
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_new_ByteArray.");
  }
  ecode1 = SWIG_AsVal_size_t(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "new_ByteArray" "', argument " "1"" of type '" "size_t""'");
  } 
  arg1 = static_cast< size_t >(val1);result = (ByteArray *)new_ByteArray(SWIG_STD_MOVE(arg1));
  
  
  
  this->self = result;
  return;
  goto fail;
fail:
  return;
}

// This is the bypass constructor to be used from child classes
template <typename SWIG_OBJ_WRAP>
_exports_ByteArray_templ<SWIG_OBJ_WRAP>::_exports_ByteArray_templ(bool, const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  
}

SWIGINTERN void delete_ByteArray(ByteArray *self){
  delete [] self;
}

// js_dtoroverride
template <typename SWIG_OBJ_WRAP>
_exports_ByteArray_templ<SWIG_OBJ_WRAP>::~_exports_ByteArray_templ() {
  auto arg1 = reinterpret_cast<ByteArray *>(this->self);
  if (this->owned && arg1) {
    delete_ByteArray(arg1);
    this->self = nullptr;
  }
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_ByteArray_templ<SWIG_OBJ_WRAP>::_wrap_ByteArray_getitem(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  ByteArray *arg1 = (ByteArray *) 0 ;
  size_t arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  size_t val2 ;
  int ecode2 = 0 ;
  unsigned char result;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_ByteArray_getitem.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_ByteArray, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "ByteArray_getitem" "', argument " "1"" of type '" "ByteArray *""'"); 
  }
  arg1 = reinterpret_cast< ByteArray * >(argp1);ecode2 = SWIG_AsVal_size_t(info[0], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "ByteArray_getitem" "', argument " "2"" of type '" "size_t""'");
  } 
  arg2 = static_cast< size_t >(val2);result = (unsigned char)ByteArray_getitem(arg1,SWIG_STD_MOVE(arg2));
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_ByteArray_templ<SWIG_OBJ_WRAP>::_wrap_ByteArray_setitem(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  ByteArray *arg1 = (ByteArray *) 0 ;
  size_t arg2 ;
  unsigned char arg3 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  size_t val2 ;
  int ecode2 = 0 ;
  unsigned char val3 ;
  int ecode3 = 0 ;
  
  if(static_cast<int>(info.Length()) < 2 || static_cast<int>(info.Length()) > 2) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_ByteArray_setitem.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_ByteArray, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "ByteArray_setitem" "', argument " "1"" of type '" "ByteArray *""'"); 
  }
  arg1 = reinterpret_cast< ByteArray * >(argp1);ecode2 = SWIG_AsVal_size_t(info[0], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "ByteArray_setitem" "', argument " "2"" of type '" "size_t""'");
  } 
  arg2 = static_cast< size_t >(val2);ecode3 = SWIG_AsVal_unsigned_SS_char(info[1], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "ByteArray_setitem" "', argument " "3"" of type '" "unsigned char""'");
  } 
  arg3 = static_cast< unsigned char >(val3);ByteArray_setitem(arg1,SWIG_STD_MOVE(arg2),arg3);
  jsresult = env.Undefined();
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_ByteArray_templ<SWIG_OBJ_WRAP>::_wrap_ByteArray_cast(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  ByteArray *arg1 = (ByteArray *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char *result = 0 ;
  
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_ByteArray_cast.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_ByteArray, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "ByteArray_cast" "', argument " "1"" of type '" "ByteArray *""'"); 
  }
  arg1 = reinterpret_cast< ByteArray * >(argp1);result = (unsigned char *)ByteArray_cast(arg1);
  jsresult = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_unsigned_char, 0 |  0 );
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_ByteArray_templ<SWIG_OBJ_WRAP>::_wrap_ByteArray_frompointer(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  unsigned char *arg1 = (unsigned char *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  ByteArray *result = 0 ;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_ByteArray_frompointer.");
  }
  
  res1 = SWIG_ConvertPtr(info[0], &argp1,SWIGTYPE_p_unsigned_char, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "ByteArray_frompointer" "', argument " "1"" of type '" "unsigned char *""'"); 
  }
  arg1 = reinterpret_cast< unsigned char * >(argp1);result = (ByteArray *)ByteArray_frompointer(arg1);
  jsresult = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_ByteArray, 0 |  0 );
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_EX_SUCCESS_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >((0)));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_EX_ERR_NOT_SUPPORT_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >((-1)));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_EX_ERR_USB_WRITE_FAIL_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >((-2)));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_EX_ERR_USB_READ_FAIL_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >((-3)));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_EX_ERR_CMD_FAIL_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >((-4)));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_EX_ERR_CH_NO_INIT_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >((-5)));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_EX_ERR_READ_DATA_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >((-6)));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_EX_ERR_PARAMETER_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >((-7)));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_EX_CHECK_STD_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(0));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_EX_CHECK_EXT_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(1));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_EX_CHECK_USER_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(2));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_EX_CHECK_NONE_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(3));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_EX_CHECK_ERROR_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(4));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_EX_MASTER_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(1));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_EX_SLAVE_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(0));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_EX_VBAT_0V_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(0));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_EX_VBAT_12V_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(1));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_EX_VBAT_5V_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(2));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_EX_MSG_TYPE_UN_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(0));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_EX_MSG_TYPE_MW_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(1));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_EX_MSG_TYPE_MR_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(2));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_EX_MSG_TYPE_SW_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(3));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_EX_MSG_TYPE_SR_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(4));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_EX_MSG_TYPE_BK_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(5));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_EX_MSG_TYPE_SY_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(6));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_EX_MSG_TYPE_ID_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(7));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_EX_MSG_TYPE_DT_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(8));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_EX_MSG_TYPE_CK_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(9));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_LIN_EX_MSG_templ<SWIG_OBJ_WRAP>::_wrap_LIN_EX_MSG_Timestamp_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  _LIN_EX_MSG *arg1 = (_LIN_EX_MSG *) 0 ;
  unsigned int arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned int val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p__LIN_EX_MSG, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LIN_EX_MSG_Timestamp_set" "', argument " "1"" of type '" "_LIN_EX_MSG *""'"); 
  }
  arg1 = reinterpret_cast< _LIN_EX_MSG * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_int(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_EX_MSG_Timestamp_set" "', argument " "2"" of type '" "unsigned int""'");
  } 
  arg2 = static_cast< unsigned int >(val2);if (arg1) (arg1)->Timestamp = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_LIN_EX_MSG_templ<SWIG_OBJ_WRAP>::_wrap_LIN_EX_MSG_Timestamp_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  _LIN_EX_MSG *arg1 = (_LIN_EX_MSG *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned int result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p__LIN_EX_MSG, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LIN_EX_MSG_Timestamp_get" "', argument " "1"" of type '" "_LIN_EX_MSG *""'"); 
  }
  arg1 = reinterpret_cast< _LIN_EX_MSG * >(argp1);result = (unsigned int) ((arg1)->Timestamp);
  jsresult = SWIG_From_unsigned_SS_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned int >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_LIN_EX_MSG_templ<SWIG_OBJ_WRAP>::_wrap_LIN_EX_MSG_MsgType_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  _LIN_EX_MSG *arg1 = (_LIN_EX_MSG *) 0 ;
  unsigned char arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p__LIN_EX_MSG, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LIN_EX_MSG_MsgType_set" "', argument " "1"" of type '" "_LIN_EX_MSG *""'"); 
  }
  arg1 = reinterpret_cast< _LIN_EX_MSG * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_EX_MSG_MsgType_set" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);if (arg1) (arg1)->MsgType = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_LIN_EX_MSG_templ<SWIG_OBJ_WRAP>::_wrap_LIN_EX_MSG_MsgType_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  _LIN_EX_MSG *arg1 = (_LIN_EX_MSG *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p__LIN_EX_MSG, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LIN_EX_MSG_MsgType_get" "', argument " "1"" of type '" "_LIN_EX_MSG *""'"); 
  }
  arg1 = reinterpret_cast< _LIN_EX_MSG * >(argp1);result = (unsigned char) ((arg1)->MsgType);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_LIN_EX_MSG_templ<SWIG_OBJ_WRAP>::_wrap_LIN_EX_MSG_CheckType_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  _LIN_EX_MSG *arg1 = (_LIN_EX_MSG *) 0 ;
  unsigned char arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p__LIN_EX_MSG, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LIN_EX_MSG_CheckType_set" "', argument " "1"" of type '" "_LIN_EX_MSG *""'"); 
  }
  arg1 = reinterpret_cast< _LIN_EX_MSG * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_EX_MSG_CheckType_set" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);if (arg1) (arg1)->CheckType = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_LIN_EX_MSG_templ<SWIG_OBJ_WRAP>::_wrap_LIN_EX_MSG_CheckType_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  _LIN_EX_MSG *arg1 = (_LIN_EX_MSG *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p__LIN_EX_MSG, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LIN_EX_MSG_CheckType_get" "', argument " "1"" of type '" "_LIN_EX_MSG *""'"); 
  }
  arg1 = reinterpret_cast< _LIN_EX_MSG * >(argp1);result = (unsigned char) ((arg1)->CheckType);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_LIN_EX_MSG_templ<SWIG_OBJ_WRAP>::_wrap_LIN_EX_MSG_DataLen_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  _LIN_EX_MSG *arg1 = (_LIN_EX_MSG *) 0 ;
  unsigned char arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p__LIN_EX_MSG, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LIN_EX_MSG_DataLen_set" "', argument " "1"" of type '" "_LIN_EX_MSG *""'"); 
  }
  arg1 = reinterpret_cast< _LIN_EX_MSG * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_EX_MSG_DataLen_set" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);if (arg1) (arg1)->DataLen = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_LIN_EX_MSG_templ<SWIG_OBJ_WRAP>::_wrap_LIN_EX_MSG_DataLen_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  _LIN_EX_MSG *arg1 = (_LIN_EX_MSG *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p__LIN_EX_MSG, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LIN_EX_MSG_DataLen_get" "', argument " "1"" of type '" "_LIN_EX_MSG *""'"); 
  }
  arg1 = reinterpret_cast< _LIN_EX_MSG * >(argp1);result = (unsigned char) ((arg1)->DataLen);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_LIN_EX_MSG_templ<SWIG_OBJ_WRAP>::_wrap_LIN_EX_MSG_Sync_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  _LIN_EX_MSG *arg1 = (_LIN_EX_MSG *) 0 ;
  unsigned char arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p__LIN_EX_MSG, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LIN_EX_MSG_Sync_set" "', argument " "1"" of type '" "_LIN_EX_MSG *""'"); 
  }
  arg1 = reinterpret_cast< _LIN_EX_MSG * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_EX_MSG_Sync_set" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);if (arg1) (arg1)->Sync = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_LIN_EX_MSG_templ<SWIG_OBJ_WRAP>::_wrap_LIN_EX_MSG_Sync_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  _LIN_EX_MSG *arg1 = (_LIN_EX_MSG *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p__LIN_EX_MSG, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LIN_EX_MSG_Sync_get" "', argument " "1"" of type '" "_LIN_EX_MSG *""'"); 
  }
  arg1 = reinterpret_cast< _LIN_EX_MSG * >(argp1);result = (unsigned char) ((arg1)->Sync);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_LIN_EX_MSG_templ<SWIG_OBJ_WRAP>::_wrap_LIN_EX_MSG_PID_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  _LIN_EX_MSG *arg1 = (_LIN_EX_MSG *) 0 ;
  unsigned char arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p__LIN_EX_MSG, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LIN_EX_MSG_PID_set" "', argument " "1"" of type '" "_LIN_EX_MSG *""'"); 
  }
  arg1 = reinterpret_cast< _LIN_EX_MSG * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_EX_MSG_PID_set" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);if (arg1) (arg1)->PID = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_LIN_EX_MSG_templ<SWIG_OBJ_WRAP>::_wrap_LIN_EX_MSG_PID_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  _LIN_EX_MSG *arg1 = (_LIN_EX_MSG *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p__LIN_EX_MSG, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LIN_EX_MSG_PID_get" "', argument " "1"" of type '" "_LIN_EX_MSG *""'"); 
  }
  arg1 = reinterpret_cast< _LIN_EX_MSG * >(argp1);result = (unsigned char) ((arg1)->PID);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_LIN_EX_MSG_templ<SWIG_OBJ_WRAP>::_wrap_LIN_EX_MSG_Data_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  _LIN_EX_MSG *arg1 = (_LIN_EX_MSG *) 0 ;
  unsigned char *arg2 = (unsigned char *) (unsigned char *)0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  void *argp2 = 0 ;
  int res2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p__LIN_EX_MSG, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LIN_EX_MSG_Data_set" "', argument " "1"" of type '" "_LIN_EX_MSG *""'"); 
  }
  arg1 = reinterpret_cast< _LIN_EX_MSG * >(argp1);res2 = SWIG_ConvertPtr(value, &argp2,SWIGTYPE_p_unsigned_char, 0 |  0 );
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "LIN_EX_MSG_Data_set" "', argument " "2"" of type '" "unsigned char [8]""'"); 
  } 
  arg2 = reinterpret_cast< unsigned char * >(argp2);{
    if (arg2) {
      size_t ii = 0;
      for (; ii < (size_t)8; ++ii) *(unsigned char *)&arg1->Data[ii] = *((unsigned char *)arg2 + ii);
    } else {
      SWIG_exception_fail(SWIG_ValueError, "invalid null reference " "in variable '""Data""' of type '""unsigned char [8]""'");
    }
  }
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_LIN_EX_MSG_templ<SWIG_OBJ_WRAP>::_wrap_LIN_EX_MSG_Data_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  _LIN_EX_MSG *arg1 = (_LIN_EX_MSG *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char *result = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p__LIN_EX_MSG, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LIN_EX_MSG_Data_get" "', argument " "1"" of type '" "_LIN_EX_MSG *""'"); 
  }
  arg1 = reinterpret_cast< _LIN_EX_MSG * >(argp1);result = (unsigned char *)(unsigned char *) ((arg1)->Data);
  jsresult = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_unsigned_char, 0 |  0 );
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_LIN_EX_MSG_templ<SWIG_OBJ_WRAP>::_wrap_LIN_EX_MSG_Check_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  _LIN_EX_MSG *arg1 = (_LIN_EX_MSG *) 0 ;
  unsigned char arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p__LIN_EX_MSG, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LIN_EX_MSG_Check_set" "', argument " "1"" of type '" "_LIN_EX_MSG *""'"); 
  }
  arg1 = reinterpret_cast< _LIN_EX_MSG * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_EX_MSG_Check_set" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);if (arg1) (arg1)->Check = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_LIN_EX_MSG_templ<SWIG_OBJ_WRAP>::_wrap_LIN_EX_MSG_Check_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  _LIN_EX_MSG *arg1 = (_LIN_EX_MSG *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p__LIN_EX_MSG, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LIN_EX_MSG_Check_get" "', argument " "1"" of type '" "_LIN_EX_MSG *""'"); 
  }
  arg1 = reinterpret_cast< _LIN_EX_MSG * >(argp1);result = (unsigned char) ((arg1)->Check);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_LIN_EX_MSG_templ<SWIG_OBJ_WRAP>::_wrap_LIN_EX_MSG_BreakBits_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  _LIN_EX_MSG *arg1 = (_LIN_EX_MSG *) 0 ;
  unsigned char arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p__LIN_EX_MSG, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LIN_EX_MSG_BreakBits_set" "', argument " "1"" of type '" "_LIN_EX_MSG *""'"); 
  }
  arg1 = reinterpret_cast< _LIN_EX_MSG * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_EX_MSG_BreakBits_set" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);if (arg1) (arg1)->BreakBits = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_LIN_EX_MSG_templ<SWIG_OBJ_WRAP>::_wrap_LIN_EX_MSG_BreakBits_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  _LIN_EX_MSG *arg1 = (_LIN_EX_MSG *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p__LIN_EX_MSG, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LIN_EX_MSG_BreakBits_get" "', argument " "1"" of type '" "_LIN_EX_MSG *""'"); 
  }
  arg1 = reinterpret_cast< _LIN_EX_MSG * >(argp1);result = (unsigned char) ((arg1)->BreakBits);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_LIN_EX_MSG_templ<SWIG_OBJ_WRAP>::_wrap_LIN_EX_MSG_Reserve1_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  _LIN_EX_MSG *arg1 = (_LIN_EX_MSG *) 0 ;
  unsigned char arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p__LIN_EX_MSG, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LIN_EX_MSG_Reserve1_set" "', argument " "1"" of type '" "_LIN_EX_MSG *""'"); 
  }
  arg1 = reinterpret_cast< _LIN_EX_MSG * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_EX_MSG_Reserve1_set" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);if (arg1) (arg1)->Reserve1 = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_LIN_EX_MSG_templ<SWIG_OBJ_WRAP>::_wrap_LIN_EX_MSG_Reserve1_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  _LIN_EX_MSG *arg1 = (_LIN_EX_MSG *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p__LIN_EX_MSG, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LIN_EX_MSG_Reserve1_get" "', argument " "1"" of type '" "_LIN_EX_MSG *""'"); 
  }
  arg1 = reinterpret_cast< _LIN_EX_MSG * >(argp1);result = (unsigned char) ((arg1)->Reserve1);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


template <typename SWIG_OBJ_WRAP>
// js_ctor
// This is the main constructor
_exports_LIN_EX_MSG_templ<SWIG_OBJ_WRAP>::_exports_LIN_EX_MSG_templ(const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  Napi::Env env = info.Env();
  
  this->info = SWIGTYPE_p__LIN_EX_MSG;
  if (info.Length() == 1 && info[0].IsExternal()) {
    // This constructor has been called internally from C++/SWIG
    // to wrap an already existing C++ object in JS
    this->self = info[0].As<Napi::External<void>>().Data();
    this->owned = false;
    return;
  }
  this->owned = true;
  
  _LIN_EX_MSG *result;
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_new_LIN_EX_MSG.");
  }
  result = (_LIN_EX_MSG *)new _LIN_EX_MSG();
  
  
  this->self = result;
  return;
  goto fail;
fail:
  return;
}

// This is the bypass constructor to be used from child classes
template <typename SWIG_OBJ_WRAP>
_exports_LIN_EX_MSG_templ<SWIG_OBJ_WRAP>::_exports_LIN_EX_MSG_templ(bool, const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  
}


// js_dtoroverride
template <typename SWIG_OBJ_WRAP>
_exports_LIN_EX_MSG_templ<SWIG_OBJ_WRAP>::~_exports_LIN_EX_MSG_templ() {
  auto arg1 = reinterpret_cast<_LIN_EX_MSG *>(this->self);
  if (this->owned && arg1) {
    delete arg1;
    this->self = nullptr;
  }
}


// js_global_function
Napi::Value _wrap_LIN_EX_Init(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  int arg1 ;
  unsigned char arg2 ;
  unsigned int arg3 ;
  unsigned char arg4 ;
  int val1 ;
  int ecode1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  unsigned int val3 ;
  int ecode3 = 0 ;
  unsigned char val4 ;
  int ecode4 = 0 ;
  int result;
  
  if(static_cast<int>(info.Length()) < 4 || static_cast<int>(info.Length()) > 4) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_EX_Init.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_EX_Init" "', argument " "1"" of type '" "int""'");
  } 
  arg1 = static_cast< int >(val1);ecode2 = SWIG_AsVal_unsigned_SS_char(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_EX_Init" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);ecode3 = SWIG_AsVal_unsigned_SS_int(info[2], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "LIN_EX_Init" "', argument " "3"" of type '" "unsigned int""'");
  } 
  arg3 = static_cast< unsigned int >(val3);ecode4 = SWIG_AsVal_unsigned_SS_char(info[3], &val4);
  if (!SWIG_IsOK(ecode4)) {
    SWIG_exception_fail(SWIG_ArgError(ecode4), "in method '" "LIN_EX_Init" "', argument " "4"" of type '" "unsigned char""'");
  } 
  arg4 = static_cast< unsigned char >(val4);result = (int)LIN_EX_Init(arg1,arg2,arg3,arg4);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_EX_Init2(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  int arg1 ;
  unsigned char arg2 ;
  unsigned int arg3 ;
  unsigned char arg4 ;
  int arg5 ;
  int arg6 ;
  int arg7 ;
  int val1 ;
  int ecode1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  unsigned int val3 ;
  int ecode3 = 0 ;
  unsigned char val4 ;
  int ecode4 = 0 ;
  int val5 ;
  int ecode5 = 0 ;
  int val6 ;
  int ecode6 = 0 ;
  int val7 ;
  int ecode7 = 0 ;
  int result;
  
  if(static_cast<int>(info.Length()) < 7 || static_cast<int>(info.Length()) > 7) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_EX_Init2.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_EX_Init2" "', argument " "1"" of type '" "int""'");
  } 
  arg1 = static_cast< int >(val1);ecode2 = SWIG_AsVal_unsigned_SS_char(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_EX_Init2" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);ecode3 = SWIG_AsVal_unsigned_SS_int(info[2], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "LIN_EX_Init2" "', argument " "3"" of type '" "unsigned int""'");
  } 
  arg3 = static_cast< unsigned int >(val3);ecode4 = SWIG_AsVal_unsigned_SS_char(info[3], &val4);
  if (!SWIG_IsOK(ecode4)) {
    SWIG_exception_fail(SWIG_ArgError(ecode4), "in method '" "LIN_EX_Init2" "', argument " "4"" of type '" "unsigned char""'");
  } 
  arg4 = static_cast< unsigned char >(val4);ecode5 = SWIG_AsVal_int(info[4], &val5);
  if (!SWIG_IsOK(ecode5)) {
    SWIG_exception_fail(SWIG_ArgError(ecode5), "in method '" "LIN_EX_Init2" "', argument " "5"" of type '" "int""'");
  } 
  arg5 = static_cast< int >(val5);ecode6 = SWIG_AsVal_int(info[5], &val6);
  if (!SWIG_IsOK(ecode6)) {
    SWIG_exception_fail(SWIG_ArgError(ecode6), "in method '" "LIN_EX_Init2" "', argument " "6"" of type '" "int""'");
  } 
  arg6 = static_cast< int >(val6);ecode7 = SWIG_AsVal_int(info[6], &val7);
  if (!SWIG_IsOK(ecode7)) {
    SWIG_exception_fail(SWIG_ArgError(ecode7), "in method '" "LIN_EX_Init2" "', argument " "7"" of type '" "int""'");
  } 
  arg7 = static_cast< int >(val7);result = (int)LIN_EX_Init2(arg1,arg2,arg3,arg4,arg5,arg6,arg7);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  
  
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_EX_MasterSync(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  int arg1 ;
  unsigned char arg2 ;
  LIN_EX_MSG *arg3 = (LIN_EX_MSG *) 0 ;
  LIN_EX_MSG *arg4 = (LIN_EX_MSG *) 0 ;
  unsigned int arg5 ;
  int val1 ;
  int ecode1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  void *argp3 = 0 ;
  int res3 = 0 ;
  void *argp4 = 0 ;
  int res4 = 0 ;
  unsigned int val5 ;
  int ecode5 = 0 ;
  int result;
  
  if(static_cast<int>(info.Length()) < 5 || static_cast<int>(info.Length()) > 5) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_EX_MasterSync.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_EX_MasterSync" "', argument " "1"" of type '" "int""'");
  } 
  arg1 = static_cast< int >(val1);ecode2 = SWIG_AsVal_unsigned_SS_char(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_EX_MasterSync" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);res3 = SWIG_ConvertPtr(info[2], &argp3,SWIGTYPE_p__LIN_EX_MSG, 0 |  0 );
  if (!SWIG_IsOK(res3)) {
    SWIG_exception_fail(SWIG_ArgError(res3), "in method '" "LIN_EX_MasterSync" "', argument " "3"" of type '" "LIN_EX_MSG *""'"); 
  }
  arg3 = reinterpret_cast< LIN_EX_MSG * >(argp3);res4 = SWIG_ConvertPtr(info[3], &argp4,SWIGTYPE_p__LIN_EX_MSG, 0 |  0 );
  if (!SWIG_IsOK(res4)) {
    SWIG_exception_fail(SWIG_ArgError(res4), "in method '" "LIN_EX_MasterSync" "', argument " "4"" of type '" "LIN_EX_MSG *""'"); 
  }
  arg4 = reinterpret_cast< LIN_EX_MSG * >(argp4);ecode5 = SWIG_AsVal_unsigned_SS_int(info[4], &val5);
  if (!SWIG_IsOK(ecode5)) {
    SWIG_exception_fail(SWIG_ArgError(ecode5), "in method '" "LIN_EX_MasterSync" "', argument " "5"" of type '" "unsigned int""'");
  } 
  arg5 = static_cast< unsigned int >(val5);result = (int)LIN_EX_MasterSync(arg1,arg2,arg3,arg4,arg5);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_EX_MasterBreak(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  int arg1 ;
  unsigned char arg2 ;
  int val1 ;
  int ecode1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  int result;
  
  if(static_cast<int>(info.Length()) < 2 || static_cast<int>(info.Length()) > 2) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_EX_MasterBreak.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_EX_MasterBreak" "', argument " "1"" of type '" "int""'");
  } 
  arg1 = static_cast< int >(val1);ecode2 = SWIG_AsVal_unsigned_SS_char(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_EX_MasterBreak" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);result = (int)LIN_EX_MasterBreak(arg1,arg2);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_EX_MasterWrite(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  int arg1 ;
  unsigned char arg2 ;
  unsigned char arg3 ;
  unsigned char *arg4 = (unsigned char *) 0 ;
  unsigned char arg5 ;
  unsigned char arg6 ;
  int val1 ;
  int ecode1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  unsigned char val3 ;
  int ecode3 = 0 ;
  void *argp4 = 0 ;
  int res4 = 0 ;
  unsigned char val5 ;
  int ecode5 = 0 ;
  unsigned char val6 ;
  int ecode6 = 0 ;
  int result;
  
  if(static_cast<int>(info.Length()) < 6 || static_cast<int>(info.Length()) > 6) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_EX_MasterWrite.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_EX_MasterWrite" "', argument " "1"" of type '" "int""'");
  } 
  arg1 = static_cast< int >(val1);ecode2 = SWIG_AsVal_unsigned_SS_char(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_EX_MasterWrite" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);ecode3 = SWIG_AsVal_unsigned_SS_char(info[2], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "LIN_EX_MasterWrite" "', argument " "3"" of type '" "unsigned char""'");
  } 
  arg3 = static_cast< unsigned char >(val3);res4 = SWIG_ConvertPtr(info[3], &argp4,SWIGTYPE_p_unsigned_char, 0 |  0 );
  if (!SWIG_IsOK(res4)) {
    SWIG_exception_fail(SWIG_ArgError(res4), "in method '" "LIN_EX_MasterWrite" "', argument " "4"" of type '" "unsigned char *""'"); 
  }
  arg4 = reinterpret_cast< unsigned char * >(argp4);ecode5 = SWIG_AsVal_unsigned_SS_char(info[4], &val5);
  if (!SWIG_IsOK(ecode5)) {
    SWIG_exception_fail(SWIG_ArgError(ecode5), "in method '" "LIN_EX_MasterWrite" "', argument " "5"" of type '" "unsigned char""'");
  } 
  arg5 = static_cast< unsigned char >(val5);ecode6 = SWIG_AsVal_unsigned_SS_char(info[5], &val6);
  if (!SWIG_IsOK(ecode6)) {
    SWIG_exception_fail(SWIG_ArgError(ecode6), "in method '" "LIN_EX_MasterWrite" "', argument " "6"" of type '" "unsigned char""'");
  } 
  arg6 = static_cast< unsigned char >(val6);result = (int)LIN_EX_MasterWrite(arg1,arg2,arg3,arg4,arg5,arg6);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_EX_MasterRead(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  int arg1 ;
  unsigned char arg2 ;
  unsigned char arg3 ;
  unsigned char *arg4 = (unsigned char *) 0 ;
  int val1 ;
  int ecode1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  unsigned char val3 ;
  int ecode3 = 0 ;
  void *argp4 = 0 ;
  int res4 = 0 ;
  int result;
  
  if(static_cast<int>(info.Length()) < 4 || static_cast<int>(info.Length()) > 4) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_EX_MasterRead.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_EX_MasterRead" "', argument " "1"" of type '" "int""'");
  } 
  arg1 = static_cast< int >(val1);ecode2 = SWIG_AsVal_unsigned_SS_char(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_EX_MasterRead" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);ecode3 = SWIG_AsVal_unsigned_SS_char(info[2], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "LIN_EX_MasterRead" "', argument " "3"" of type '" "unsigned char""'");
  } 
  arg3 = static_cast< unsigned char >(val3);res4 = SWIG_ConvertPtr(info[3], &argp4,SWIGTYPE_p_unsigned_char, 0 |  0 );
  if (!SWIG_IsOK(res4)) {
    SWIG_exception_fail(SWIG_ArgError(res4), "in method '" "LIN_EX_MasterRead" "', argument " "4"" of type '" "unsigned char *""'"); 
  }
  arg4 = reinterpret_cast< unsigned char * >(argp4);result = (int)LIN_EX_MasterRead(arg1,arg2,arg3,arg4);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_EX_SlaveSetIDMode(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  int arg1 ;
  unsigned char arg2 ;
  LIN_EX_MSG *arg3 = (LIN_EX_MSG *) 0 ;
  unsigned int arg4 ;
  int val1 ;
  int ecode1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  void *argp3 = 0 ;
  int res3 = 0 ;
  unsigned int val4 ;
  int ecode4 = 0 ;
  int result;
  
  if(static_cast<int>(info.Length()) < 4 || static_cast<int>(info.Length()) > 4) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_EX_SlaveSetIDMode.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_EX_SlaveSetIDMode" "', argument " "1"" of type '" "int""'");
  } 
  arg1 = static_cast< int >(val1);ecode2 = SWIG_AsVal_unsigned_SS_char(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_EX_SlaveSetIDMode" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);res3 = SWIG_ConvertPtr(info[2], &argp3,SWIGTYPE_p__LIN_EX_MSG, 0 |  0 );
  if (!SWIG_IsOK(res3)) {
    SWIG_exception_fail(SWIG_ArgError(res3), "in method '" "LIN_EX_SlaveSetIDMode" "', argument " "3"" of type '" "LIN_EX_MSG *""'"); 
  }
  arg3 = reinterpret_cast< LIN_EX_MSG * >(argp3);ecode4 = SWIG_AsVal_unsigned_SS_int(info[3], &val4);
  if (!SWIG_IsOK(ecode4)) {
    SWIG_exception_fail(SWIG_ArgError(ecode4), "in method '" "LIN_EX_SlaveSetIDMode" "', argument " "4"" of type '" "unsigned int""'");
  } 
  arg4 = static_cast< unsigned int >(val4);result = (int)LIN_EX_SlaveSetIDMode(arg1,arg2,arg3,arg4);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_EX_SlaveGetIDMode(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  int arg1 ;
  unsigned char arg2 ;
  LIN_EX_MSG *arg3 = (LIN_EX_MSG *) 0 ;
  int val1 ;
  int ecode1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  void *argp3 = 0 ;
  int res3 = 0 ;
  int result;
  
  if(static_cast<int>(info.Length()) < 3 || static_cast<int>(info.Length()) > 3) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_EX_SlaveGetIDMode.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_EX_SlaveGetIDMode" "', argument " "1"" of type '" "int""'");
  } 
  arg1 = static_cast< int >(val1);ecode2 = SWIG_AsVal_unsigned_SS_char(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_EX_SlaveGetIDMode" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);res3 = SWIG_ConvertPtr(info[2], &argp3,SWIGTYPE_p__LIN_EX_MSG, 0 |  0 );
  if (!SWIG_IsOK(res3)) {
    SWIG_exception_fail(SWIG_ArgError(res3), "in method '" "LIN_EX_SlaveGetIDMode" "', argument " "3"" of type '" "LIN_EX_MSG *""'"); 
  }
  arg3 = reinterpret_cast< LIN_EX_MSG * >(argp3);result = (int)LIN_EX_SlaveGetIDMode(arg1,arg2,arg3);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_EX_SlaveGetData(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  int arg1 ;
  unsigned char arg2 ;
  LIN_EX_MSG *arg3 = (LIN_EX_MSG *) 0 ;
  int val1 ;
  int ecode1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  void *argp3 = 0 ;
  int res3 = 0 ;
  int result;
  
  if(static_cast<int>(info.Length()) < 3 || static_cast<int>(info.Length()) > 3) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_EX_SlaveGetData.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_EX_SlaveGetData" "', argument " "1"" of type '" "int""'");
  } 
  arg1 = static_cast< int >(val1);ecode2 = SWIG_AsVal_unsigned_SS_char(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_EX_SlaveGetData" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);res3 = SWIG_ConvertPtr(info[2], &argp3,SWIGTYPE_p__LIN_EX_MSG, 0 |  0 );
  if (!SWIG_IsOK(res3)) {
    SWIG_exception_fail(SWIG_ArgError(res3), "in method '" "LIN_EX_SlaveGetData" "', argument " "3"" of type '" "LIN_EX_MSG *""'"); 
  }
  arg3 = reinterpret_cast< LIN_EX_MSG * >(argp3);result = (int)LIN_EX_SlaveGetData(arg1,arg2,arg3);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_EX_CtrlPowerOut(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  int arg1 ;
  unsigned char arg2 ;
  unsigned char arg3 ;
  int val1 ;
  int ecode1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  unsigned char val3 ;
  int ecode3 = 0 ;
  int result;
  
  if(static_cast<int>(info.Length()) < 3 || static_cast<int>(info.Length()) > 3) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_EX_CtrlPowerOut.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_EX_CtrlPowerOut" "', argument " "1"" of type '" "int""'");
  } 
  arg1 = static_cast< int >(val1);ecode2 = SWIG_AsVal_unsigned_SS_char(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_EX_CtrlPowerOut" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);ecode3 = SWIG_AsVal_unsigned_SS_char(info[2], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "LIN_EX_CtrlPowerOut" "', argument " "3"" of type '" "unsigned char""'");
  } 
  arg3 = static_cast< unsigned char >(val3);result = (int)LIN_EX_CtrlPowerOut(arg1,arg2,arg3);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_EX_GetVbatValue(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  int arg1 ;
  unsigned short *arg2 = (unsigned short *) 0 ;
  int val1 ;
  int ecode1 = 0 ;
  void *argp2 = 0 ;
  int res2 = 0 ;
  int result;
  
  if(static_cast<int>(info.Length()) < 2 || static_cast<int>(info.Length()) > 2) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_EX_GetVbatValue.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_EX_GetVbatValue" "', argument " "1"" of type '" "int""'");
  } 
  arg1 = static_cast< int >(val1);res2 = SWIG_ConvertPtr(info[1], &argp2,SWIGTYPE_p_unsigned_short, 0 |  0 );
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "LIN_EX_GetVbatValue" "', argument " "2"" of type '" "unsigned short *""'"); 
  }
  arg2 = reinterpret_cast< unsigned short * >(argp2);result = (int)LIN_EX_GetVbatValue(arg1,arg2);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_EX_MasterStartSch(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  int arg1 ;
  unsigned char arg2 ;
  LIN_EX_MSG *arg3 = (LIN_EX_MSG *) 0 ;
  unsigned int arg4 ;
  int val1 ;
  int ecode1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  void *argp3 = 0 ;
  int res3 = 0 ;
  unsigned int val4 ;
  int ecode4 = 0 ;
  int result;
  
  if(static_cast<int>(info.Length()) < 4 || static_cast<int>(info.Length()) > 4) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_EX_MasterStartSch.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_EX_MasterStartSch" "', argument " "1"" of type '" "int""'");
  } 
  arg1 = static_cast< int >(val1);ecode2 = SWIG_AsVal_unsigned_SS_char(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_EX_MasterStartSch" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);res3 = SWIG_ConvertPtr(info[2], &argp3,SWIGTYPE_p__LIN_EX_MSG, 0 |  0 );
  if (!SWIG_IsOK(res3)) {
    SWIG_exception_fail(SWIG_ArgError(res3), "in method '" "LIN_EX_MasterStartSch" "', argument " "3"" of type '" "LIN_EX_MSG *""'"); 
  }
  arg3 = reinterpret_cast< LIN_EX_MSG * >(argp3);ecode4 = SWIG_AsVal_unsigned_SS_int(info[3], &val4);
  if (!SWIG_IsOK(ecode4)) {
    SWIG_exception_fail(SWIG_ArgError(ecode4), "in method '" "LIN_EX_MasterStartSch" "', argument " "4"" of type '" "unsigned int""'");
  } 
  arg4 = static_cast< unsigned int >(val4);result = (int)LIN_EX_MasterStartSch(arg1,arg2,arg3,arg4);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_EX_MasterStopSch(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  int arg1 ;
  unsigned char arg2 ;
  int val1 ;
  int ecode1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  int result;
  
  if(static_cast<int>(info.Length()) < 2 || static_cast<int>(info.Length()) > 2) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_EX_MasterStopSch.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_EX_MasterStopSch" "', argument " "1"" of type '" "int""'");
  } 
  arg1 = static_cast< int >(val1);ecode2 = SWIG_AsVal_unsigned_SS_char(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_EX_MasterStopSch" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);result = (int)LIN_EX_MasterStopSch(arg1,arg2);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_EX_MasterGetSchState(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  int arg1 ;
  unsigned char arg2 ;
  int val1 ;
  int ecode1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  int result;
  
  if(static_cast<int>(info.Length()) < 2 || static_cast<int>(info.Length()) > 2) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_EX_MasterGetSchState.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_EX_MasterGetSchState" "', argument " "1"" of type '" "int""'");
  } 
  arg1 = static_cast< int >(val1);ecode2 = SWIG_AsVal_unsigned_SS_char(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_EX_MasterGetSchState" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);result = (int)LIN_EX_MasterGetSchState(arg1,arg2);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_EX_MasterGetSch(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  int arg1 ;
  unsigned char arg2 ;
  LIN_EX_MSG *arg3 = (LIN_EX_MSG *) 0 ;
  int val1 ;
  int ecode1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  void *argp3 = 0 ;
  int res3 = 0 ;
  int result;
  
  if(static_cast<int>(info.Length()) < 3 || static_cast<int>(info.Length()) > 3) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_EX_MasterGetSch.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_EX_MasterGetSch" "', argument " "1"" of type '" "int""'");
  } 
  arg1 = static_cast< int >(val1);ecode2 = SWIG_AsVal_unsigned_SS_char(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_EX_MasterGetSch" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);res3 = SWIG_ConvertPtr(info[2], &argp3,SWIGTYPE_p__LIN_EX_MSG, 0 |  0 );
  if (!SWIG_IsOK(res3)) {
    SWIG_exception_fail(SWIG_ArgError(res3), "in method '" "LIN_EX_MasterGetSch" "', argument " "3"" of type '" "LIN_EX_MSG *""'"); 
  }
  arg3 = reinterpret_cast< LIN_EX_MSG * >(argp3);result = (int)LIN_EX_MasterGetSch(arg1,arg2,arg3);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_EX_MasterSetSchRunTimes(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  int arg1 ;
  unsigned char arg2 ;
  unsigned int arg3 ;
  int val1 ;
  int ecode1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  unsigned int val3 ;
  int ecode3 = 0 ;
  int result;
  
  if(static_cast<int>(info.Length()) < 3 || static_cast<int>(info.Length()) > 3) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_EX_MasterSetSchRunTimes.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_EX_MasterSetSchRunTimes" "', argument " "1"" of type '" "int""'");
  } 
  arg1 = static_cast< int >(val1);ecode2 = SWIG_AsVal_unsigned_SS_char(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_EX_MasterSetSchRunTimes" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);ecode3 = SWIG_AsVal_unsigned_SS_int(info[2], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "LIN_EX_MasterSetSchRunTimes" "', argument " "3"" of type '" "unsigned int""'");
  } 
  arg3 = static_cast< unsigned int >(val3);result = (int)LIN_EX_MasterSetSchRunTimes(arg1,arg2,arg3);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_EX_DecodeListFile(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  char *arg1 = (char *) 0 ;
  char arg2 ;
  int arg3 ;
  char *arg4 = (char *) 0 ;
  char arg5 ;
  char *arg6 = (char *) 0 ;
  char arg7 ;
  int res1 ;
  char *buf1 = 0 ;
  int alloc1 = 0 ;
  char val2 ;
  int ecode2 = 0 ;
  int val3 ;
  int ecode3 = 0 ;
  int res4 ;
  char *buf4 = 0 ;
  int alloc4 = 0 ;
  char val5 ;
  int ecode5 = 0 ;
  int res6 ;
  char *buf6 = 0 ;
  int alloc6 = 0 ;
  char val7 ;
  int ecode7 = 0 ;
  int result;
  
  if(static_cast<int>(info.Length()) < 7 || static_cast<int>(info.Length()) > 7) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_EX_DecodeListFile.");
  }
  
  res1 = SWIG_AsCharPtrAndSize(info[0], &buf1, NULL, &alloc1);
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LIN_EX_DecodeListFile" "', argument " "1"" of type '" "char *""'");
  }
  arg1 = reinterpret_cast< char * >(buf1);ecode2 = SWIG_AsVal_char(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_EX_DecodeListFile" "', argument " "2"" of type '" "char""'");
  } 
  arg2 = static_cast< char >(val2);ecode3 = SWIG_AsVal_int(info[2], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "LIN_EX_DecodeListFile" "', argument " "3"" of type '" "int""'");
  } 
  arg3 = static_cast< int >(val3);res4 = SWIG_AsCharPtrAndSize(info[3], &buf4, NULL, &alloc4);
  if (!SWIG_IsOK(res4)) {
    SWIG_exception_fail(SWIG_ArgError(res4), "in method '" "LIN_EX_DecodeListFile" "', argument " "4"" of type '" "char *""'");
  }
  arg4 = reinterpret_cast< char * >(buf4);ecode5 = SWIG_AsVal_char(info[4], &val5);
  if (!SWIG_IsOK(ecode5)) {
    SWIG_exception_fail(SWIG_ArgError(ecode5), "in method '" "LIN_EX_DecodeListFile" "', argument " "5"" of type '" "char""'");
  } 
  arg5 = static_cast< char >(val5);res6 = SWIG_AsCharPtrAndSize(info[5], &buf6, NULL, &alloc6);
  if (!SWIG_IsOK(res6)) {
    SWIG_exception_fail(SWIG_ArgError(res6), "in method '" "LIN_EX_DecodeListFile" "', argument " "6"" of type '" "char *""'");
  }
  arg6 = reinterpret_cast< char * >(buf6);ecode7 = SWIG_AsVal_char(info[6], &val7);
  if (!SWIG_IsOK(ecode7)) {
    SWIG_exception_fail(SWIG_ArgError(ecode7), "in method '" "LIN_EX_DecodeListFile" "', argument " "7"" of type '" "char""'");
  } 
  arg7 = static_cast< char >(val7);result = (int)LIN_EX_DecodeListFile(arg1,arg2,arg3,arg4,arg5,arg6,arg7);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  if (alloc1 == SWIG_NEWOBJ) delete[] buf1;
  
  
  if (alloc4 == SWIG_NEWOBJ) delete[] buf4;
  
  if (alloc6 == SWIG_NEWOBJ) delete[] buf6;
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_EX_GetListFileMsg(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  int arg1 ;
  int arg2 ;
  LIN_EX_MSG *arg3 = (LIN_EX_MSG *) 0 ;
  int val1 ;
  int ecode1 = 0 ;
  int val2 ;
  int ecode2 = 0 ;
  void *argp3 = 0 ;
  int res3 = 0 ;
  int result;
  
  if(static_cast<int>(info.Length()) < 3 || static_cast<int>(info.Length()) > 3) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_EX_GetListFileMsg.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_EX_GetListFileMsg" "', argument " "1"" of type '" "int""'");
  } 
  arg1 = static_cast< int >(val1);ecode2 = SWIG_AsVal_int(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_EX_GetListFileMsg" "', argument " "2"" of type '" "int""'");
  } 
  arg2 = static_cast< int >(val2);res3 = SWIG_ConvertPtr(info[2], &argp3,SWIGTYPE_p__LIN_EX_MSG, 0 |  0 );
  if (!SWIG_IsOK(res3)) {
    SWIG_exception_fail(SWIG_ArgError(res3), "in method '" "LIN_EX_GetListFileMsg" "', argument " "3"" of type '" "LIN_EX_MSG *""'"); 
  }
  arg3 = reinterpret_cast< LIN_EX_MSG * >(argp3);result = (int)LIN_EX_GetListFileMsg(arg1,arg2,arg3);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_EX_GetStartTime(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  int arg1 ;
  unsigned char arg2 ;
  int val1 ;
  int ecode1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  long long result;
  
  if(static_cast<int>(info.Length()) < 2 || static_cast<int>(info.Length()) > 2) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_EX_GetStartTime.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_EX_GetStartTime" "', argument " "1"" of type '" "int""'");
  } 
  arg1 = static_cast< int >(val1);ecode2 = SWIG_AsVal_unsigned_SS_char(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_EX_GetStartTime" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);result = (long long)LIN_EX_GetStartTime(arg1,arg2);
  jsresult = SWIG_From_long_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< long long >(result));
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_EX_ResetStartTime(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  int arg1 ;
  unsigned char arg2 ;
  int val1 ;
  int ecode1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  int result;
  
  if(static_cast<int>(info.Length()) < 2 || static_cast<int>(info.Length()) > 2) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_EX_ResetStartTime.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_EX_ResetStartTime" "', argument " "1"" of type '" "int""'");
  } 
  arg1 = static_cast< int >(val1);ecode2 = SWIG_AsVal_unsigned_SS_char(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_EX_ResetStartTime" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);result = (int)LIN_EX_ResetStartTime(arg1,arg2);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_EX_Stop(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  int arg1 ;
  unsigned char arg2 ;
  int val1 ;
  int ecode1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  int result;
  
  if(static_cast<int>(info.Length()) < 2 || static_cast<int>(info.Length()) > 2) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_EX_Stop.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_EX_Stop" "', argument " "1"" of type '" "int""'");
  } 
  arg1 = static_cast< int >(val1);ecode2 = SWIG_AsVal_unsigned_SS_char(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_EX_Stop" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);result = (int)LIN_EX_Stop(arg1,arg2);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_DEVICE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_DEVICE_INFO_FirmwareName_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  _DEVICE_INFO *arg1 = (_DEVICE_INFO *) 0 ;
  char *arg2 = (char *) (char *)0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  char temp2[32] ;
  int res2 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p__DEVICE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "DEVICE_INFO_FirmwareName_set" "', argument " "1"" of type '" "_DEVICE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< _DEVICE_INFO * >(argp1);res2 = SWIG_AsCharArray(value, temp2, 32);
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "DEVICE_INFO_FirmwareName_set" "', argument " "2"" of type '" "char [32]""'");
  }
  arg2 = reinterpret_cast< char * >(temp2);if (arg2) memcpy(arg1->FirmwareName,arg2,32*sizeof(char));
  else memset(arg1->FirmwareName,0,32*sizeof(char));
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_DEVICE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_DEVICE_INFO_FirmwareName_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  _DEVICE_INFO *arg1 = (_DEVICE_INFO *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  char *result = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p__DEVICE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "DEVICE_INFO_FirmwareName_get" "', argument " "1"" of type '" "_DEVICE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< _DEVICE_INFO * >(argp1);result = (char *)(char *) ((arg1)->FirmwareName);
  {
    size_t size = SWIG_strnlen(result, 32);
    
    
    
    jsresult = SWIG_FromCharPtrAndSize(result, size);
  }
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_DEVICE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_DEVICE_INFO_BuildDate_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  _DEVICE_INFO *arg1 = (_DEVICE_INFO *) 0 ;
  char *arg2 = (char *) (char *)0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  char temp2[32] ;
  int res2 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p__DEVICE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "DEVICE_INFO_BuildDate_set" "', argument " "1"" of type '" "_DEVICE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< _DEVICE_INFO * >(argp1);res2 = SWIG_AsCharArray(value, temp2, 32);
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "DEVICE_INFO_BuildDate_set" "', argument " "2"" of type '" "char [32]""'");
  }
  arg2 = reinterpret_cast< char * >(temp2);if (arg2) memcpy(arg1->BuildDate,arg2,32*sizeof(char));
  else memset(arg1->BuildDate,0,32*sizeof(char));
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_DEVICE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_DEVICE_INFO_BuildDate_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  _DEVICE_INFO *arg1 = (_DEVICE_INFO *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  char *result = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p__DEVICE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "DEVICE_INFO_BuildDate_get" "', argument " "1"" of type '" "_DEVICE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< _DEVICE_INFO * >(argp1);result = (char *)(char *) ((arg1)->BuildDate);
  {
    size_t size = SWIG_strnlen(result, 32);
    
    
    
    jsresult = SWIG_FromCharPtrAndSize(result, size);
  }
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_DEVICE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_DEVICE_INFO_HardwareVersion_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  _DEVICE_INFO *arg1 = (_DEVICE_INFO *) 0 ;
  int arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p__DEVICE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "DEVICE_INFO_HardwareVersion_set" "', argument " "1"" of type '" "_DEVICE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< _DEVICE_INFO * >(argp1);ecode2 = SWIG_AsVal_int(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "DEVICE_INFO_HardwareVersion_set" "', argument " "2"" of type '" "int""'");
  } 
  arg2 = static_cast< int >(val2);if (arg1) (arg1)->HardwareVersion = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_DEVICE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_DEVICE_INFO_HardwareVersion_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  _DEVICE_INFO *arg1 = (_DEVICE_INFO *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p__DEVICE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "DEVICE_INFO_HardwareVersion_get" "', argument " "1"" of type '" "_DEVICE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< _DEVICE_INFO * >(argp1);result = (int) ((arg1)->HardwareVersion);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_DEVICE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_DEVICE_INFO_FirmwareVersion_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  _DEVICE_INFO *arg1 = (_DEVICE_INFO *) 0 ;
  int arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p__DEVICE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "DEVICE_INFO_FirmwareVersion_set" "', argument " "1"" of type '" "_DEVICE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< _DEVICE_INFO * >(argp1);ecode2 = SWIG_AsVal_int(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "DEVICE_INFO_FirmwareVersion_set" "', argument " "2"" of type '" "int""'");
  } 
  arg2 = static_cast< int >(val2);if (arg1) (arg1)->FirmwareVersion = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_DEVICE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_DEVICE_INFO_FirmwareVersion_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  _DEVICE_INFO *arg1 = (_DEVICE_INFO *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p__DEVICE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "DEVICE_INFO_FirmwareVersion_get" "', argument " "1"" of type '" "_DEVICE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< _DEVICE_INFO * >(argp1);result = (int) ((arg1)->FirmwareVersion);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_DEVICE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_DEVICE_INFO_SerialNumber_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  _DEVICE_INFO *arg1 = (_DEVICE_INFO *) 0 ;
  int *arg2 = (int *) (int *)0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  void *argp2 = 0 ;
  int res2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p__DEVICE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "DEVICE_INFO_SerialNumber_set" "', argument " "1"" of type '" "_DEVICE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< _DEVICE_INFO * >(argp1);res2 = SWIG_ConvertPtr(value, &argp2,SWIGTYPE_p_int, 0 |  0 );
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "DEVICE_INFO_SerialNumber_set" "', argument " "2"" of type '" "int [3]""'"); 
  } 
  arg2 = reinterpret_cast< int * >(argp2);{
    if (arg2) {
      size_t ii = 0;
      for (; ii < (size_t)3; ++ii) *(int *)&arg1->SerialNumber[ii] = *((int *)arg2 + ii);
    } else {
      SWIG_exception_fail(SWIG_ValueError, "invalid null reference " "in variable '""SerialNumber""' of type '""int [3]""'");
    }
  }
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_DEVICE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_DEVICE_INFO_SerialNumber_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  _DEVICE_INFO *arg1 = (_DEVICE_INFO *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int *result = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p__DEVICE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "DEVICE_INFO_SerialNumber_get" "', argument " "1"" of type '" "_DEVICE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< _DEVICE_INFO * >(argp1);result = (int *)(int *) ((arg1)->SerialNumber);
  jsresult = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_int, 0 |  0 );
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_DEVICE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_DEVICE_INFO_Functions_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  _DEVICE_INFO *arg1 = (_DEVICE_INFO *) 0 ;
  int arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p__DEVICE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "DEVICE_INFO_Functions_set" "', argument " "1"" of type '" "_DEVICE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< _DEVICE_INFO * >(argp1);ecode2 = SWIG_AsVal_int(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "DEVICE_INFO_Functions_set" "', argument " "2"" of type '" "int""'");
  } 
  arg2 = static_cast< int >(val2);if (arg1) (arg1)->Functions = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_DEVICE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_DEVICE_INFO_Functions_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  _DEVICE_INFO *arg1 = (_DEVICE_INFO *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p__DEVICE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "DEVICE_INFO_Functions_get" "', argument " "1"" of type '" "_DEVICE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< _DEVICE_INFO * >(argp1);result = (int) ((arg1)->Functions);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


template <typename SWIG_OBJ_WRAP>
// js_ctor
// This is the main constructor
_exports_DEVICE_INFO_templ<SWIG_OBJ_WRAP>::_exports_DEVICE_INFO_templ(const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  Napi::Env env = info.Env();
  
  this->info = SWIGTYPE_p__DEVICE_INFO;
  if (info.Length() == 1 && info[0].IsExternal()) {
    // This constructor has been called internally from C++/SWIG
    // to wrap an already existing C++ object in JS
    this->self = info[0].As<Napi::External<void>>().Data();
    this->owned = false;
    return;
  }
  this->owned = true;
  
  _DEVICE_INFO *result;
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_new_DEVICE_INFO.");
  }
  result = (_DEVICE_INFO *)new _DEVICE_INFO();
  
  
  this->self = result;
  return;
  goto fail;
fail:
  return;
}

// This is the bypass constructor to be used from child classes
template <typename SWIG_OBJ_WRAP>
_exports_DEVICE_INFO_templ<SWIG_OBJ_WRAP>::_exports_DEVICE_INFO_templ(bool, const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  
}


// js_dtoroverride
template <typename SWIG_OBJ_WRAP>
_exports_DEVICE_INFO_templ<SWIG_OBJ_WRAP>::~_exports_DEVICE_INFO_templ() {
  auto arg1 = reinterpret_cast<_DEVICE_INFO *>(this->self);
  if (this->owned && arg1) {
    delete arg1;
    this->self = nullptr;
  }
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_HARDWARE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_HARDWARE_INFO_McuModel_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HARDWARE_INFO *arg1 = (HARDWARE_INFO *) 0 ;
  unsigned char *arg2 = (unsigned char *) (unsigned char *)0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  void *argp2 = 0 ;
  int res2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HARDWARE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HARDWARE_INFO_McuModel_set" "', argument " "1"" of type '" "HARDWARE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< HARDWARE_INFO * >(argp1);res2 = SWIG_ConvertPtr(value, &argp2,SWIGTYPE_p_unsigned_char, 0 |  0 );
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "HARDWARE_INFO_McuModel_set" "', argument " "2"" of type '" "unsigned char [16]""'"); 
  } 
  arg2 = reinterpret_cast< unsigned char * >(argp2);{
    if (arg2) {
      size_t ii = 0;
      for (; ii < (size_t)16; ++ii) *(unsigned char *)&arg1->McuModel[ii] = *((unsigned char *)arg2 + ii);
    } else {
      SWIG_exception_fail(SWIG_ValueError, "invalid null reference " "in variable '""McuModel""' of type '""unsigned char [16]""'");
    }
  }
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_HARDWARE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_HARDWARE_INFO_McuModel_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HARDWARE_INFO *arg1 = (HARDWARE_INFO *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char *result = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HARDWARE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HARDWARE_INFO_McuModel_get" "', argument " "1"" of type '" "HARDWARE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< HARDWARE_INFO * >(argp1);result = (unsigned char *)(unsigned char *) ((arg1)->McuModel);
  jsresult = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_unsigned_char, 0 |  0 );
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_HARDWARE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_HARDWARE_INFO_ProductModel_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HARDWARE_INFO *arg1 = (HARDWARE_INFO *) 0 ;
  unsigned char *arg2 = (unsigned char *) (unsigned char *)0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  void *argp2 = 0 ;
  int res2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HARDWARE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HARDWARE_INFO_ProductModel_set" "', argument " "1"" of type '" "HARDWARE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< HARDWARE_INFO * >(argp1);res2 = SWIG_ConvertPtr(value, &argp2,SWIGTYPE_p_unsigned_char, 0 |  0 );
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "HARDWARE_INFO_ProductModel_set" "', argument " "2"" of type '" "unsigned char [16]""'"); 
  } 
  arg2 = reinterpret_cast< unsigned char * >(argp2);{
    if (arg2) {
      size_t ii = 0;
      for (; ii < (size_t)16; ++ii) *(unsigned char *)&arg1->ProductModel[ii] = *((unsigned char *)arg2 + ii);
    } else {
      SWIG_exception_fail(SWIG_ValueError, "invalid null reference " "in variable '""ProductModel""' of type '""unsigned char [16]""'");
    }
  }
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_HARDWARE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_HARDWARE_INFO_ProductModel_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HARDWARE_INFO *arg1 = (HARDWARE_INFO *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char *result = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HARDWARE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HARDWARE_INFO_ProductModel_get" "', argument " "1"" of type '" "HARDWARE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< HARDWARE_INFO * >(argp1);result = (unsigned char *)(unsigned char *) ((arg1)->ProductModel);
  jsresult = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_unsigned_char, 0 |  0 );
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_HARDWARE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_HARDWARE_INFO_Version_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HARDWARE_INFO *arg1 = (HARDWARE_INFO *) 0 ;
  unsigned int arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned int val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HARDWARE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HARDWARE_INFO_Version_set" "', argument " "1"" of type '" "HARDWARE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< HARDWARE_INFO * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_int(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "HARDWARE_INFO_Version_set" "', argument " "2"" of type '" "unsigned int""'");
  } 
  arg2 = static_cast< unsigned int >(val2);if (arg1) (arg1)->Version = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_HARDWARE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_HARDWARE_INFO_Version_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HARDWARE_INFO *arg1 = (HARDWARE_INFO *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned int result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HARDWARE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HARDWARE_INFO_Version_get" "', argument " "1"" of type '" "HARDWARE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< HARDWARE_INFO * >(argp1);result = (unsigned int) ((arg1)->Version);
  jsresult = SWIG_From_unsigned_SS_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned int >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_HARDWARE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_HARDWARE_INFO_CANChannelNum_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HARDWARE_INFO *arg1 = (HARDWARE_INFO *) 0 ;
  unsigned char arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HARDWARE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HARDWARE_INFO_CANChannelNum_set" "', argument " "1"" of type '" "HARDWARE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< HARDWARE_INFO * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "HARDWARE_INFO_CANChannelNum_set" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);if (arg1) (arg1)->CANChannelNum = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_HARDWARE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_HARDWARE_INFO_CANChannelNum_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HARDWARE_INFO *arg1 = (HARDWARE_INFO *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HARDWARE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HARDWARE_INFO_CANChannelNum_get" "', argument " "1"" of type '" "HARDWARE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< HARDWARE_INFO * >(argp1);result = (unsigned char) ((arg1)->CANChannelNum);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_HARDWARE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_HARDWARE_INFO_LINChannelNum_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HARDWARE_INFO *arg1 = (HARDWARE_INFO *) 0 ;
  unsigned char arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HARDWARE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HARDWARE_INFO_LINChannelNum_set" "', argument " "1"" of type '" "HARDWARE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< HARDWARE_INFO * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "HARDWARE_INFO_LINChannelNum_set" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);if (arg1) (arg1)->LINChannelNum = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_HARDWARE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_HARDWARE_INFO_LINChannelNum_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HARDWARE_INFO *arg1 = (HARDWARE_INFO *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HARDWARE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HARDWARE_INFO_LINChannelNum_get" "', argument " "1"" of type '" "HARDWARE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< HARDWARE_INFO * >(argp1);result = (unsigned char) ((arg1)->LINChannelNum);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_HARDWARE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_HARDWARE_INFO_PWMChannelNum_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HARDWARE_INFO *arg1 = (HARDWARE_INFO *) 0 ;
  unsigned char arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HARDWARE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HARDWARE_INFO_PWMChannelNum_set" "', argument " "1"" of type '" "HARDWARE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< HARDWARE_INFO * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "HARDWARE_INFO_PWMChannelNum_set" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);if (arg1) (arg1)->PWMChannelNum = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_HARDWARE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_HARDWARE_INFO_PWMChannelNum_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HARDWARE_INFO *arg1 = (HARDWARE_INFO *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HARDWARE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HARDWARE_INFO_PWMChannelNum_get" "', argument " "1"" of type '" "HARDWARE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< HARDWARE_INFO * >(argp1);result = (unsigned char) ((arg1)->PWMChannelNum);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_HARDWARE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_HARDWARE_INFO_HaveCANFD_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HARDWARE_INFO *arg1 = (HARDWARE_INFO *) 0 ;
  unsigned char arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HARDWARE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HARDWARE_INFO_HaveCANFD_set" "', argument " "1"" of type '" "HARDWARE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< HARDWARE_INFO * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "HARDWARE_INFO_HaveCANFD_set" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);if (arg1) (arg1)->HaveCANFD = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_HARDWARE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_HARDWARE_INFO_HaveCANFD_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HARDWARE_INFO *arg1 = (HARDWARE_INFO *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HARDWARE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HARDWARE_INFO_HaveCANFD_get" "', argument " "1"" of type '" "HARDWARE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< HARDWARE_INFO * >(argp1);result = (unsigned char) ((arg1)->HaveCANFD);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_HARDWARE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_HARDWARE_INFO_DIChannelNum_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HARDWARE_INFO *arg1 = (HARDWARE_INFO *) 0 ;
  unsigned char arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HARDWARE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HARDWARE_INFO_DIChannelNum_set" "', argument " "1"" of type '" "HARDWARE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< HARDWARE_INFO * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "HARDWARE_INFO_DIChannelNum_set" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);if (arg1) (arg1)->DIChannelNum = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_HARDWARE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_HARDWARE_INFO_DIChannelNum_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HARDWARE_INFO *arg1 = (HARDWARE_INFO *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HARDWARE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HARDWARE_INFO_DIChannelNum_get" "', argument " "1"" of type '" "HARDWARE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< HARDWARE_INFO * >(argp1);result = (unsigned char) ((arg1)->DIChannelNum);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_HARDWARE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_HARDWARE_INFO_DOChannelNum_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HARDWARE_INFO *arg1 = (HARDWARE_INFO *) 0 ;
  unsigned char arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HARDWARE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HARDWARE_INFO_DOChannelNum_set" "', argument " "1"" of type '" "HARDWARE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< HARDWARE_INFO * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "HARDWARE_INFO_DOChannelNum_set" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);if (arg1) (arg1)->DOChannelNum = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_HARDWARE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_HARDWARE_INFO_DOChannelNum_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HARDWARE_INFO *arg1 = (HARDWARE_INFO *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HARDWARE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HARDWARE_INFO_DOChannelNum_get" "', argument " "1"" of type '" "HARDWARE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< HARDWARE_INFO * >(argp1);result = (unsigned char) ((arg1)->DOChannelNum);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_HARDWARE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_HARDWARE_INFO_HaveIsolation_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HARDWARE_INFO *arg1 = (HARDWARE_INFO *) 0 ;
  unsigned char arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HARDWARE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HARDWARE_INFO_HaveIsolation_set" "', argument " "1"" of type '" "HARDWARE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< HARDWARE_INFO * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "HARDWARE_INFO_HaveIsolation_set" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);if (arg1) (arg1)->HaveIsolation = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_HARDWARE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_HARDWARE_INFO_HaveIsolation_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HARDWARE_INFO *arg1 = (HARDWARE_INFO *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HARDWARE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HARDWARE_INFO_HaveIsolation_get" "', argument " "1"" of type '" "HARDWARE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< HARDWARE_INFO * >(argp1);result = (unsigned char) ((arg1)->HaveIsolation);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_HARDWARE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_HARDWARE_INFO_ExPowerSupply_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HARDWARE_INFO *arg1 = (HARDWARE_INFO *) 0 ;
  unsigned char arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HARDWARE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HARDWARE_INFO_ExPowerSupply_set" "', argument " "1"" of type '" "HARDWARE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< HARDWARE_INFO * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "HARDWARE_INFO_ExPowerSupply_set" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);if (arg1) (arg1)->ExPowerSupply = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_HARDWARE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_HARDWARE_INFO_ExPowerSupply_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HARDWARE_INFO *arg1 = (HARDWARE_INFO *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HARDWARE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HARDWARE_INFO_ExPowerSupply_get" "', argument " "1"" of type '" "HARDWARE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< HARDWARE_INFO * >(argp1);result = (unsigned char) ((arg1)->ExPowerSupply);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_HARDWARE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_HARDWARE_INFO_IsOEM_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HARDWARE_INFO *arg1 = (HARDWARE_INFO *) 0 ;
  unsigned char arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HARDWARE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HARDWARE_INFO_IsOEM_set" "', argument " "1"" of type '" "HARDWARE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< HARDWARE_INFO * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "HARDWARE_INFO_IsOEM_set" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);if (arg1) (arg1)->IsOEM = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_HARDWARE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_HARDWARE_INFO_IsOEM_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HARDWARE_INFO *arg1 = (HARDWARE_INFO *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HARDWARE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HARDWARE_INFO_IsOEM_get" "', argument " "1"" of type '" "HARDWARE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< HARDWARE_INFO * >(argp1);result = (unsigned char) ((arg1)->IsOEM);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_HARDWARE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_HARDWARE_INFO_EECapacity_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HARDWARE_INFO *arg1 = (HARDWARE_INFO *) 0 ;
  unsigned char arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HARDWARE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HARDWARE_INFO_EECapacity_set" "', argument " "1"" of type '" "HARDWARE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< HARDWARE_INFO * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "HARDWARE_INFO_EECapacity_set" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);if (arg1) (arg1)->EECapacity = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_HARDWARE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_HARDWARE_INFO_EECapacity_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HARDWARE_INFO *arg1 = (HARDWARE_INFO *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HARDWARE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HARDWARE_INFO_EECapacity_get" "', argument " "1"" of type '" "HARDWARE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< HARDWARE_INFO * >(argp1);result = (unsigned char) ((arg1)->EECapacity);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_HARDWARE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_HARDWARE_INFO_SPIFlashCapacity_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HARDWARE_INFO *arg1 = (HARDWARE_INFO *) 0 ;
  unsigned char arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HARDWARE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HARDWARE_INFO_SPIFlashCapacity_set" "', argument " "1"" of type '" "HARDWARE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< HARDWARE_INFO * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "HARDWARE_INFO_SPIFlashCapacity_set" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);if (arg1) (arg1)->SPIFlashCapacity = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_HARDWARE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_HARDWARE_INFO_SPIFlashCapacity_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HARDWARE_INFO *arg1 = (HARDWARE_INFO *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HARDWARE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HARDWARE_INFO_SPIFlashCapacity_get" "', argument " "1"" of type '" "HARDWARE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< HARDWARE_INFO * >(argp1);result = (unsigned char) ((arg1)->SPIFlashCapacity);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_HARDWARE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_HARDWARE_INFO_TFCardSupport_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HARDWARE_INFO *arg1 = (HARDWARE_INFO *) 0 ;
  unsigned char arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HARDWARE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HARDWARE_INFO_TFCardSupport_set" "', argument " "1"" of type '" "HARDWARE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< HARDWARE_INFO * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "HARDWARE_INFO_TFCardSupport_set" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);if (arg1) (arg1)->TFCardSupport = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_HARDWARE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_HARDWARE_INFO_TFCardSupport_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HARDWARE_INFO *arg1 = (HARDWARE_INFO *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HARDWARE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HARDWARE_INFO_TFCardSupport_get" "', argument " "1"" of type '" "HARDWARE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< HARDWARE_INFO * >(argp1);result = (unsigned char) ((arg1)->TFCardSupport);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_HARDWARE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_HARDWARE_INFO_ProductionDate_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HARDWARE_INFO *arg1 = (HARDWARE_INFO *) 0 ;
  unsigned char *arg2 = (unsigned char *) (unsigned char *)0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  void *argp2 = 0 ;
  int res2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HARDWARE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HARDWARE_INFO_ProductionDate_set" "', argument " "1"" of type '" "HARDWARE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< HARDWARE_INFO * >(argp1);res2 = SWIG_ConvertPtr(value, &argp2,SWIGTYPE_p_unsigned_char, 0 |  0 );
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "HARDWARE_INFO_ProductionDate_set" "', argument " "2"" of type '" "unsigned char [12]""'"); 
  } 
  arg2 = reinterpret_cast< unsigned char * >(argp2);{
    if (arg2) {
      size_t ii = 0;
      for (; ii < (size_t)12; ++ii) *(unsigned char *)&arg1->ProductionDate[ii] = *((unsigned char *)arg2 + ii);
    } else {
      SWIG_exception_fail(SWIG_ValueError, "invalid null reference " "in variable '""ProductionDate""' of type '""unsigned char [12]""'");
    }
  }
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_HARDWARE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_HARDWARE_INFO_ProductionDate_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HARDWARE_INFO *arg1 = (HARDWARE_INFO *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char *result = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HARDWARE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HARDWARE_INFO_ProductionDate_get" "', argument " "1"" of type '" "HARDWARE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< HARDWARE_INFO * >(argp1);result = (unsigned char *)(unsigned char *) ((arg1)->ProductionDate);
  jsresult = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_unsigned_char, 0 |  0 );
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_HARDWARE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_HARDWARE_INFO_USBControl_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HARDWARE_INFO *arg1 = (HARDWARE_INFO *) 0 ;
  unsigned char arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HARDWARE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HARDWARE_INFO_USBControl_set" "', argument " "1"" of type '" "HARDWARE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< HARDWARE_INFO * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "HARDWARE_INFO_USBControl_set" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);if (arg1) (arg1)->USBControl = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_HARDWARE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_HARDWARE_INFO_USBControl_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HARDWARE_INFO *arg1 = (HARDWARE_INFO *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HARDWARE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HARDWARE_INFO_USBControl_get" "', argument " "1"" of type '" "HARDWARE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< HARDWARE_INFO * >(argp1);result = (unsigned char) ((arg1)->USBControl);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_HARDWARE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_HARDWARE_INFO_SerialControl_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HARDWARE_INFO *arg1 = (HARDWARE_INFO *) 0 ;
  unsigned char arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HARDWARE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HARDWARE_INFO_SerialControl_set" "', argument " "1"" of type '" "HARDWARE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< HARDWARE_INFO * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "HARDWARE_INFO_SerialControl_set" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);if (arg1) (arg1)->SerialControl = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_HARDWARE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_HARDWARE_INFO_SerialControl_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HARDWARE_INFO *arg1 = (HARDWARE_INFO *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HARDWARE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HARDWARE_INFO_SerialControl_get" "', argument " "1"" of type '" "HARDWARE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< HARDWARE_INFO * >(argp1);result = (unsigned char) ((arg1)->SerialControl);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_HARDWARE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_HARDWARE_INFO_EthControl_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HARDWARE_INFO *arg1 = (HARDWARE_INFO *) 0 ;
  unsigned char arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HARDWARE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HARDWARE_INFO_EthControl_set" "', argument " "1"" of type '" "HARDWARE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< HARDWARE_INFO * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "HARDWARE_INFO_EthControl_set" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);if (arg1) (arg1)->EthControl = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_HARDWARE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_HARDWARE_INFO_EthControl_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HARDWARE_INFO *arg1 = (HARDWARE_INFO *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HARDWARE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HARDWARE_INFO_EthControl_get" "', argument " "1"" of type '" "HARDWARE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< HARDWARE_INFO * >(argp1);result = (unsigned char) ((arg1)->EthControl);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_HARDWARE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_HARDWARE_INFO_VbatChannel_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HARDWARE_INFO *arg1 = (HARDWARE_INFO *) 0 ;
  unsigned char arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HARDWARE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HARDWARE_INFO_VbatChannel_set" "', argument " "1"" of type '" "HARDWARE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< HARDWARE_INFO * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "HARDWARE_INFO_VbatChannel_set" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);if (arg1) (arg1)->VbatChannel = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_HARDWARE_INFO_templ<SWIG_OBJ_WRAP>::_wrap_HARDWARE_INFO_VbatChannel_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HARDWARE_INFO *arg1 = (HARDWARE_INFO *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HARDWARE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HARDWARE_INFO_VbatChannel_get" "', argument " "1"" of type '" "HARDWARE_INFO *""'"); 
  }
  arg1 = reinterpret_cast< HARDWARE_INFO * >(argp1);result = (unsigned char) ((arg1)->VbatChannel);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


template <typename SWIG_OBJ_WRAP>
// js_ctor
// This is the main constructor
_exports_HARDWARE_INFO_templ<SWIG_OBJ_WRAP>::_exports_HARDWARE_INFO_templ(const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  Napi::Env env = info.Env();
  
  this->info = SWIGTYPE_p_HARDWARE_INFO;
  if (info.Length() == 1 && info[0].IsExternal()) {
    // This constructor has been called internally from C++/SWIG
    // to wrap an already existing C++ object in JS
    this->self = info[0].As<Napi::External<void>>().Data();
    this->owned = false;
    return;
  }
  this->owned = true;
  
  HARDWARE_INFO *result;
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_new_HARDWARE_INFO.");
  }
  result = (HARDWARE_INFO *)new HARDWARE_INFO();
  
  
  this->self = result;
  return;
  goto fail;
fail:
  return;
}

// This is the bypass constructor to be used from child classes
template <typename SWIG_OBJ_WRAP>
_exports_HARDWARE_INFO_templ<SWIG_OBJ_WRAP>::_exports_HARDWARE_INFO_templ(bool, const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  
}


// js_dtoroverride
template <typename SWIG_OBJ_WRAP>
_exports_HARDWARE_INFO_templ<SWIG_OBJ_WRAP>::~_exports_HARDWARE_INFO_templ() {
  auto arg1 = reinterpret_cast<HARDWARE_INFO *>(this->self);
  if (this->owned && arg1) {
    delete arg1;
    this->self = nullptr;
  }
}


// js_global_getter
Napi::Value exports_POWER_LEVEL_1V8_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(1));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_POWER_LEVEL_2V5_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(2));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_POWER_LEVEL_3V3_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(3));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_USB_ScanDevice(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  int *arg1 = (int *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int result;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_USB_ScanDevice.");
  }
  
  res1 = SWIG_ConvertPtr(info[0], &argp1,SWIGTYPE_p_int, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "USB_ScanDevice" "', argument " "1"" of type '" "int *""'"); 
  }
  arg1 = reinterpret_cast< int * >(argp1);result = (int)USB_ScanDevice(arg1);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_USB_OpenDevice(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  int arg1 ;
  int val1 ;
  int ecode1 = 0 ;
  unsigned char result;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_USB_OpenDevice.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "USB_OpenDevice" "', argument " "1"" of type '" "int""'");
  } 
  arg1 = static_cast< int >(val1);result = (unsigned char)USB_OpenDevice(arg1);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_USB_CloseDevice(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  int arg1 ;
  int val1 ;
  int ecode1 = 0 ;
  unsigned char result;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_USB_CloseDevice.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "USB_CloseDevice" "', argument " "1"" of type '" "int""'");
  } 
  arg1 = static_cast< int >(val1);result = (unsigned char)USB_CloseDevice(arg1);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_USB_ResetDevice(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  int arg1 ;
  int val1 ;
  int ecode1 = 0 ;
  unsigned char result;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_USB_ResetDevice.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "USB_ResetDevice" "', argument " "1"" of type '" "int""'");
  } 
  arg1 = static_cast< int >(val1);result = (unsigned char)USB_ResetDevice(arg1);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_USB_RetryConnect(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  int arg1 ;
  int val1 ;
  int ecode1 = 0 ;
  unsigned char result;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_USB_RetryConnect.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "USB_RetryConnect" "', argument " "1"" of type '" "int""'");
  } 
  arg1 = static_cast< int >(val1);result = (unsigned char)USB_RetryConnect(arg1);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_USB_WaitResume(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  int arg1 ;
  int arg2 ;
  int val1 ;
  int ecode1 = 0 ;
  int val2 ;
  int ecode2 = 0 ;
  unsigned char result;
  
  if(static_cast<int>(info.Length()) < 2 || static_cast<int>(info.Length()) > 2) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_USB_WaitResume.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "USB_WaitResume" "', argument " "1"" of type '" "int""'");
  } 
  arg1 = static_cast< int >(val1);ecode2 = SWIG_AsVal_int(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "USB_WaitResume" "', argument " "2"" of type '" "int""'");
  } 
  arg2 = static_cast< int >(val2);result = (unsigned char)USB_WaitResume(arg1,arg2);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_DEV_GetDeviceInfo(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  int arg1 ;
  DEVICE_INFO *arg2 = (DEVICE_INFO *) 0 ;
  char *arg3 = (char *) 0 ;
  int val1 ;
  int ecode1 = 0 ;
  void *argp2 = 0 ;
  int res2 = 0 ;
  int res3 ;
  char *buf3 = 0 ;
  int alloc3 = 0 ;
  unsigned char result;
  
  if(static_cast<int>(info.Length()) < 3 || static_cast<int>(info.Length()) > 3) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_DEV_GetDeviceInfo.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "DEV_GetDeviceInfo" "', argument " "1"" of type '" "int""'");
  } 
  arg1 = static_cast< int >(val1);res2 = SWIG_ConvertPtr(info[1], &argp2,SWIGTYPE_p__DEVICE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "DEV_GetDeviceInfo" "', argument " "2"" of type '" "DEVICE_INFO *""'"); 
  }
  arg2 = reinterpret_cast< DEVICE_INFO * >(argp2);res3 = SWIG_AsCharPtrAndSize(info[2], &buf3, NULL, &alloc3);
  if (!SWIG_IsOK(res3)) {
    SWIG_exception_fail(SWIG_ArgError(res3), "in method '" "DEV_GetDeviceInfo" "', argument " "3"" of type '" "char *""'");
  }
  arg3 = reinterpret_cast< char * >(buf3);result = (unsigned char)DEV_GetDeviceInfo(arg1,arg2,arg3);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  if (alloc3 == SWIG_NEWOBJ) delete[] buf3;
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_DEV_GetHardwareInfo(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  int arg1 ;
  HARDWARE_INFO *arg2 = (HARDWARE_INFO *) 0 ;
  int val1 ;
  int ecode1 = 0 ;
  void *argp2 = 0 ;
  int res2 = 0 ;
  unsigned char result;
  
  if(static_cast<int>(info.Length()) < 2 || static_cast<int>(info.Length()) > 2) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_DEV_GetHardwareInfo.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "DEV_GetHardwareInfo" "', argument " "1"" of type '" "int""'");
  } 
  arg1 = static_cast< int >(val1);res2 = SWIG_ConvertPtr(info[1], &argp2,SWIGTYPE_p_HARDWARE_INFO, 0 |  0 );
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "DEV_GetHardwareInfo" "', argument " "2"" of type '" "HARDWARE_INFO *""'"); 
  }
  arg2 = reinterpret_cast< HARDWARE_INFO * >(argp2);result = (unsigned char)DEV_GetHardwareInfo(arg1,arg2);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_DEV_EraseUserData(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  int arg1 ;
  int val1 ;
  int ecode1 = 0 ;
  unsigned char result;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_DEV_EraseUserData.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "DEV_EraseUserData" "', argument " "1"" of type '" "int""'");
  } 
  arg1 = static_cast< int >(val1);result = (unsigned char)DEV_EraseUserData(arg1);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_DEV_EraseUserDataSector(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  int arg1 ;
  int arg2 ;
  int val1 ;
  int ecode1 = 0 ;
  int val2 ;
  int ecode2 = 0 ;
  unsigned char result;
  
  if(static_cast<int>(info.Length()) < 2 || static_cast<int>(info.Length()) > 2) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_DEV_EraseUserDataSector.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "DEV_EraseUserDataSector" "', argument " "1"" of type '" "int""'");
  } 
  arg1 = static_cast< int >(val1);ecode2 = SWIG_AsVal_int(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "DEV_EraseUserDataSector" "', argument " "2"" of type '" "int""'");
  } 
  arg2 = static_cast< int >(val2);result = (unsigned char)DEV_EraseUserDataSector(arg1,arg2);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_DEV_WriteUserData(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  int arg1 ;
  int arg2 ;
  unsigned char *arg3 = (unsigned char *) 0 ;
  int arg4 ;
  int val1 ;
  int ecode1 = 0 ;
  int val2 ;
  int ecode2 = 0 ;
  void *argp3 = 0 ;
  int res3 = 0 ;
  int val4 ;
  int ecode4 = 0 ;
  unsigned char result;
  
  if(static_cast<int>(info.Length()) < 4 || static_cast<int>(info.Length()) > 4) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_DEV_WriteUserData.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "DEV_WriteUserData" "', argument " "1"" of type '" "int""'");
  } 
  arg1 = static_cast< int >(val1);ecode2 = SWIG_AsVal_int(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "DEV_WriteUserData" "', argument " "2"" of type '" "int""'");
  } 
  arg2 = static_cast< int >(val2);res3 = SWIG_ConvertPtr(info[2], &argp3,SWIGTYPE_p_unsigned_char, 0 |  0 );
  if (!SWIG_IsOK(res3)) {
    SWIG_exception_fail(SWIG_ArgError(res3), "in method '" "DEV_WriteUserData" "', argument " "3"" of type '" "unsigned char *""'"); 
  }
  arg3 = reinterpret_cast< unsigned char * >(argp3);ecode4 = SWIG_AsVal_int(info[3], &val4);
  if (!SWIG_IsOK(ecode4)) {
    SWIG_exception_fail(SWIG_ArgError(ecode4), "in method '" "DEV_WriteUserData" "', argument " "4"" of type '" "int""'");
  } 
  arg4 = static_cast< int >(val4);result = (unsigned char)DEV_WriteUserData(arg1,arg2,arg3,arg4);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_DEV_ReadUserData(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  int arg1 ;
  int arg2 ;
  unsigned char *arg3 = (unsigned char *) 0 ;
  int arg4 ;
  int val1 ;
  int ecode1 = 0 ;
  int val2 ;
  int ecode2 = 0 ;
  void *argp3 = 0 ;
  int res3 = 0 ;
  int val4 ;
  int ecode4 = 0 ;
  unsigned char result;
  
  if(static_cast<int>(info.Length()) < 4 || static_cast<int>(info.Length()) > 4) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_DEV_ReadUserData.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "DEV_ReadUserData" "', argument " "1"" of type '" "int""'");
  } 
  arg1 = static_cast< int >(val1);ecode2 = SWIG_AsVal_int(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "DEV_ReadUserData" "', argument " "2"" of type '" "int""'");
  } 
  arg2 = static_cast< int >(val2);res3 = SWIG_ConvertPtr(info[2], &argp3,SWIGTYPE_p_unsigned_char, 0 |  0 );
  if (!SWIG_IsOK(res3)) {
    SWIG_exception_fail(SWIG_ArgError(res3), "in method '" "DEV_ReadUserData" "', argument " "3"" of type '" "unsigned char *""'"); 
  }
  arg3 = reinterpret_cast< unsigned char * >(argp3);ecode4 = SWIG_AsVal_int(info[3], &val4);
  if (!SWIG_IsOK(ecode4)) {
    SWIG_exception_fail(SWIG_ArgError(ecode4), "in method '" "DEV_ReadUserData" "', argument " "4"" of type '" "int""'");
  } 
  arg4 = static_cast< int >(val4);result = (unsigned char)DEV_ReadUserData(arg1,arg2,arg3,arg4);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_DEV_SetPowerLevel(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  int arg1 ;
  char arg2 ;
  int val1 ;
  int ecode1 = 0 ;
  char val2 ;
  int ecode2 = 0 ;
  unsigned char result;
  
  if(static_cast<int>(info.Length()) < 2 || static_cast<int>(info.Length()) > 2) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_DEV_SetPowerLevel.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "DEV_SetPowerLevel" "', argument " "1"" of type '" "int""'");
  } 
  arg1 = static_cast< int >(val1);ecode2 = SWIG_AsVal_char(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "DEV_SetPowerLevel" "', argument " "2"" of type '" "char""'");
  } 
  arg2 = static_cast< char >(val2);result = (unsigned char)DEV_SetPowerLevel(arg1,arg2);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_DEV_GetTimestamp(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  int arg1 ;
  char arg2 ;
  unsigned int *arg3 = (unsigned int *) 0 ;
  int val1 ;
  int ecode1 = 0 ;
  char val2 ;
  int ecode2 = 0 ;
  void *argp3 = 0 ;
  int res3 = 0 ;
  unsigned char result;
  
  if(static_cast<int>(info.Length()) < 3 || static_cast<int>(info.Length()) > 3) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_DEV_GetTimestamp.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "DEV_GetTimestamp" "', argument " "1"" of type '" "int""'");
  } 
  arg1 = static_cast< int >(val1);ecode2 = SWIG_AsVal_char(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "DEV_GetTimestamp" "', argument " "2"" of type '" "char""'");
  } 
  arg2 = static_cast< char >(val2);res3 = SWIG_ConvertPtr(info[2], &argp3,SWIGTYPE_p_unsigned_int, 0 |  0 );
  if (!SWIG_IsOK(res3)) {
    SWIG_exception_fail(SWIG_ArgError(res3), "in method '" "DEV_GetTimestamp" "', argument " "3"" of type '" "unsigned int *""'"); 
  }
  arg3 = reinterpret_cast< unsigned int * >(argp3);result = (unsigned char)DEV_GetTimestamp(arg1,arg2,arg3);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_DEV_ResetTimestamp(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  int arg1 ;
  int val1 ;
  int ecode1 = 0 ;
  unsigned char result;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_DEV_ResetTimestamp.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "DEV_ResetTimestamp" "', argument " "1"" of type '" "int""'");
  } 
  arg1 = static_cast< int >(val1);result = (unsigned char)DEV_ResetTimestamp(arg1);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_DEV_GetDllBuildTime(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  char *arg1 = (char *) 0 ;
  int res1 ;
  char *buf1 = 0 ;
  int alloc1 = 0 ;
  unsigned char result;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_DEV_GetDllBuildTime.");
  }
  
  res1 = SWIG_AsCharPtrAndSize(info[0], &buf1, NULL, &alloc1);
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "DEV_GetDllBuildTime" "', argument " "1"" of type '" "char *""'");
  }
  arg1 = reinterpret_cast< char * >(buf1);result = (unsigned char)DEV_GetDllBuildTime(arg1);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  if (alloc1 == SWIG_NEWOBJ) delete[] buf1;
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LoadDll(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  char *arg1 = (char *) 0 ;
  int res1 ;
  char *buf1 = 0 ;
  int alloc1 = 0 ;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LoadDll.");
  }
  
  res1 = SWIG_AsCharPtrAndSize(info[0], &buf1, NULL, &alloc1);
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LoadDll" "', argument " "1"" of type '" "char const *""'");
  }
  arg1 = reinterpret_cast< char * >(buf1);LoadDll((char const *)arg1);
  jsresult = env.Undefined();
  if (alloc1 == SWIG_NEWOBJ) delete[] buf1;
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


/* -------- TYPE CONVERSION AND EQUIVALENCE RULES (BEGIN) -------- */

static void *_p_ByteArrayTo_p_unsigned_char(void *x, int *SWIGUNUSEDPARM(newmemory)) {
    return (void *)((unsigned char *)  ((ByteArray *) x));
}
static void *_p_UINT8PTo_p_unsigned_char(void *x, int *SWIGUNUSEDPARM(newmemory)) {
    return (void *)((unsigned char *)  ((UINT8P *) x));
}
static void *_p_I32ArrayTo_p_int(void *x, int *SWIGUNUSEDPARM(newmemory)) {
    return (void *)((int *)  ((I32Array *) x));
}
static void *_p_U32ArrayTo_p_unsigned_int(void *x, int *SWIGUNUSEDPARM(newmemory)) {
    return (void *)((unsigned int *)  ((U32Array *) x));
}
static swig_type_info _swigt__p_ByteArray = {"_p_ByteArray", "p_ByteArray|ByteArray *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_HARDWARE_INFO = {"_p_HARDWARE_INFO", "HARDWARE_INFO *|p_HARDWARE_INFO", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_I32Array = {"_p_I32Array", "p_I32Array|I32Array *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_LIN_MSG_ARRAY = {"_p_LIN_MSG_ARRAY", "LIN_MSG_ARRAY *|p_LIN_MSG_ARRAY", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_U32Array = {"_p_U32Array", "p_U32Array|U32Array *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_UINT8P = {"_p_UINT8P", "p_UINT8P|UINT8P *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p__DEVICE_INFO = {"_p__DEVICE_INFO", "DEVICE_INFO *|p__DEVICE_INFO|_DEVICE_INFO *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p__LIN_EX_MSG = {"_p__LIN_EX_MSG", "LIN_EX_MSG *|_LIN_EX_MSG *|p__LIN_EX_MSG", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p___int64 = {"_p___int64", "LONG64 *|LONGLONG *|__int64 *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_char = {"_p_char", "CCHAR *|CHAR *|TCHAR *|char *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_float = {"_p_float", "FLOAT *|float *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_int = {"_p_int", "BOOL *|INT *|INT32 *|INT_PTR *|LONG32 *|int32_t *|int_fast16_t *|int_fast32_t *|int_least32_t *|intptr_t *|int *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_long = {"_p_long", "HRESULT *|LONG *|LONG_PTR *|SHANDLE_PTR *|SSIZE_T *|long *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_long_long = {"_p_long_long", "int64_t *|int_fast64_t *|int_least64_t *|intmax_t *|long long *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_p_char = {"_p_p_char", "LPCTSTR *|LPCUTSTR *|LPTCH *|LPTSTR *|LPUTSTR *|PCTSTR *|PCUTSTR *|PTCH *|PTSTR *|PUTSTR *|char **", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_p_unsigned_long = {"_p_p_unsigned_long", "PLCID *|unsigned long **", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_short = {"_p_short", "HALF_PTR *|INT16 *|SHORT *|int16_t *|int_least16_t *|short *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_signed___int64 = {"_p_signed___int64", "INT64 *|signed __int64 *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_signed_char = {"_p_signed_char", "INT8 *|int8_t *|int_fast8_t *|int_least8_t *|signed char *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_unsigned___int64 = {"_p_unsigned___int64", "DWORD64 *|DWORDLONG *|UINT64 *|ULONG64 *|ULONGLONG *|unsigned __int64 *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_unsigned_char = {"_p_unsigned_char", "BOOLEAN *|BYTE *|FCHAR *|TBYTE *|UCHAR *|UINT8 *|uint8_t *|uint_fast8_t *|uint_least8_t *|unsigned char *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_unsigned_int = {"_p_unsigned_int", "DWORD32 *|UINT *|UINT32 *|UINT_PTR *|ULONG32 *|uint32_t *|uint_fast16_t *|uint_fast32_t *|uint_least32_t *|uintptr_t *|unsigned int *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_unsigned_long = {"_p_unsigned_long", "DWORD *|DWORD_PTR *|FLONG *|HANDLE_PTR *|LCID *|SIZE_T *|ULONG *|ULONG_PTR *|unsigned long *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_unsigned_long_long = {"_p_unsigned_long_long", "uint64_t *|uint_fast64_t *|uint_least64_t *|uintmax_t *|unsigned long long *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_unsigned_short = {"_p_unsigned_short", "FSHORT *|LANGID *|UHALF_PTR *|UINT16 *|USHORT *|WORD *|uint16_t *|uint_least16_t *|unsigned short *", 0, 0, (void*)0, 0};

static swig_type_info *swig_type_initial[] = {
  &_swigt__p_ByteArray,
  &_swigt__p_HARDWARE_INFO,
  &_swigt__p_I32Array,
  &_swigt__p_LIN_MSG_ARRAY,
  &_swigt__p_U32Array,
  &_swigt__p_UINT8P,
  &_swigt__p__DEVICE_INFO,
  &_swigt__p__LIN_EX_MSG,
  &_swigt__p___int64,
  &_swigt__p_char,
  &_swigt__p_float,
  &_swigt__p_int,
  &_swigt__p_long,
  &_swigt__p_long_long,
  &_swigt__p_p_char,
  &_swigt__p_p_unsigned_long,
  &_swigt__p_short,
  &_swigt__p_signed___int64,
  &_swigt__p_signed_char,
  &_swigt__p_unsigned___int64,
  &_swigt__p_unsigned_char,
  &_swigt__p_unsigned_int,
  &_swigt__p_unsigned_long,
  &_swigt__p_unsigned_long_long,
  &_swigt__p_unsigned_short,
};

static swig_cast_info _swigc__p_ByteArray[] = {  {&_swigt__p_ByteArray, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_HARDWARE_INFO[] = {  {&_swigt__p_HARDWARE_INFO, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_I32Array[] = {  {&_swigt__p_I32Array, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_LIN_MSG_ARRAY[] = {  {&_swigt__p_LIN_MSG_ARRAY, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_U32Array[] = {  {&_swigt__p_U32Array, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_UINT8P[] = {  {&_swigt__p_UINT8P, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p__DEVICE_INFO[] = {  {&_swigt__p__DEVICE_INFO, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p__LIN_EX_MSG[] = {  {&_swigt__p__LIN_EX_MSG, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p___int64[] = {  {&_swigt__p___int64, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_char[] = {  {&_swigt__p_char, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_float[] = {  {&_swigt__p_float, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_int[] = {  {&_swigt__p_int, 0, 0, 0},  {&_swigt__p_I32Array, _p_I32ArrayTo_p_int, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_long[] = {  {&_swigt__p_long, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_long_long[] = {  {&_swigt__p_long_long, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_p_char[] = {  {&_swigt__p_p_char, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_p_unsigned_long[] = {  {&_swigt__p_p_unsigned_long, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_short[] = {  {&_swigt__p_short, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_signed___int64[] = {  {&_swigt__p_signed___int64, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_signed_char[] = {  {&_swigt__p_signed_char, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_unsigned___int64[] = {  {&_swigt__p_unsigned___int64, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_unsigned_char[] = {  {&_swigt__p_unsigned_char, 0, 0, 0},  {&_swigt__p_ByteArray, _p_ByteArrayTo_p_unsigned_char, 0, 0},  {&_swigt__p_UINT8P, _p_UINT8PTo_p_unsigned_char, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_unsigned_int[] = {  {&_swigt__p_unsigned_int, 0, 0, 0},  {&_swigt__p_U32Array, _p_U32ArrayTo_p_unsigned_int, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_unsigned_long[] = {  {&_swigt__p_unsigned_long, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_unsigned_long_long[] = {  {&_swigt__p_unsigned_long_long, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_unsigned_short[] = {  {&_swigt__p_unsigned_short, 0, 0, 0},{0, 0, 0, 0}};

static swig_cast_info *swig_cast_initial[] = {
  _swigc__p_ByteArray,
  _swigc__p_HARDWARE_INFO,
  _swigc__p_I32Array,
  _swigc__p_LIN_MSG_ARRAY,
  _swigc__p_U32Array,
  _swigc__p_UINT8P,
  _swigc__p__DEVICE_INFO,
  _swigc__p__LIN_EX_MSG,
  _swigc__p___int64,
  _swigc__p_char,
  _swigc__p_float,
  _swigc__p_int,
  _swigc__p_long,
  _swigc__p_long_long,
  _swigc__p_p_char,
  _swigc__p_p_unsigned_long,
  _swigc__p_short,
  _swigc__p_signed___int64,
  _swigc__p_signed_char,
  _swigc__p_unsigned___int64,
  _swigc__p_unsigned_char,
  _swigc__p_unsigned_int,
  _swigc__p_unsigned_long,
  _swigc__p_unsigned_long_long,
  _swigc__p_unsigned_short,
};


/* -------- TYPE CONVERSION AND EQUIVALENCE RULES (END) -------- */




EnvInstanceData::EnvInstanceData(Napi::Env env, swig_module_info *swig_module) :
env(env), SWIG_NAPI_ObjectWrapCtor(nullptr), ctor(nullptr), swig_module(swig_module) {
  ctor = new Napi::FunctionReference*[swig_module->size + 1];
  for (size_t i = 0; i <= swig_module->size; i++) {
    ctor[i] = nullptr;
  }
}

EnvInstanceData::~EnvInstanceData() {
  for (size_t i = 0; i <= swig_module->size; i++) {
    if (ctor[i] != nullptr)
      delete ctor[i];
    ctor[i] = nullptr;
  }
  delete [] ctor;
  delete SWIG_NAPI_ObjectWrapCtor;
}

SWIGRUNTIME void
SWIG_NAPI_SetModule(Napi::Env env, swig_module_info *swig_module) {
  auto data = new EnvInstanceData(env, swig_module);
  env.SetInstanceData(data);
}

SWIGRUNTIME swig_module_info *
SWIG_NAPI_GetModule(Napi::Env env) {
  auto data = env.GetInstanceData<EnvInstanceData>();
  if (data == nullptr) return nullptr;
  return data->swig_module;
}

#define SWIG_GetModule(clientdata)                SWIG_NAPI_GetModule(clientdata)
#define SWIG_SetModule(clientdata, pointer)       SWIG_NAPI_SetModule(clientdata, pointer)
#define SWIG_INIT_CLIENT_DATA_TYPE                Napi::Env


/* -----------------------------------------------------------------------------
 * Type initialization:
 * This problem is tough by the requirement that no dynamic
 * memory is used. Also, since swig_type_info structures store pointers to
 * swig_cast_info structures and swig_cast_info structures store pointers back
 * to swig_type_info structures, we need some lookup code at initialization.
 * The idea is that swig generates all the structures that are needed.
 * The runtime then collects these partially filled structures.
 * The SWIG_InitializeModule function takes these initial arrays out of
 * swig_module, and does all the lookup, filling in the swig_module.types
 * array with the correct data and linking the correct swig_cast_info
 * structures together.
 *
 * The generated swig_type_info structures are assigned statically to an initial
 * array. We just loop through that array, and handle each type individually.
 * First we lookup if this type has been already loaded, and if so, use the
 * loaded structure instead of the generated one. Then we have to fill in the
 * cast linked list. The cast data is initially stored in something like a
 * two-dimensional array. Each row corresponds to a type (there are the same
 * number of rows as there are in the swig_type_initial array). Each entry in
 * a column is one of the swig_cast_info structures for that type.
 * The cast_initial array is actually an array of arrays, because each row has
 * a variable number of columns. So to actually build the cast linked list,
 * we find the array of casts associated with the type, and loop through it
 * adding the casts to the list. The one last trick we need to do is making
 * sure the type pointer in the swig_cast_info struct is correct.
 *
 * First off, we lookup the cast->type name to see if it is already loaded.
 * There are three cases to handle:
 *  1) If the cast->type has already been loaded AND the type we are adding
 *     casting info to has not been loaded (it is in this module), THEN we
 *     replace the cast->type pointer with the type pointer that has already
 *     been loaded.
 *  2) If BOTH types (the one we are adding casting info to, and the
 *     cast->type) are loaded, THEN the cast info has already been loaded by
 *     the previous module so we just ignore it.
 *  3) Finally, if cast->type has not already been loaded, then we add that
 *     swig_cast_info to the linked list (because the cast->type) pointer will
 *     be correct.
 * ----------------------------------------------------------------------------- */

#ifdef __cplusplus
extern "C" {
#if 0
} /* c-mode */
#endif
#endif

#if 0
#define SWIGRUNTIME_DEBUG
#endif

#ifndef SWIG_INIT_CLIENT_DATA_TYPE
#define SWIG_INIT_CLIENT_DATA_TYPE void *
#endif

SWIGRUNTIME void
SWIG_InitializeModule(SWIG_INIT_CLIENT_DATA_TYPE clientdata) {
  size_t i;
  swig_module_info *module_head, *iter;
  int init;

  /* check to see if the circular list has been setup, if not, set it up */
  if (swig_module.next==0) {
    /* Initialize the swig_module */
    swig_module.type_initial = swig_type_initial;
    swig_module.cast_initial = swig_cast_initial;
    swig_module.next = &swig_module;
    init = 1;
  } else {
    init = 0;
  }

  /* Try and load any already created modules */
  module_head = SWIG_GetModule(clientdata);
  if (!module_head) {
    /* This is the first module loaded for this interpreter */
    /* so set the swig module into the interpreter */
    SWIG_SetModule(clientdata, &swig_module);
  } else {
    /* the interpreter has loaded a SWIG module, but has it loaded this one? */
    iter=module_head;
    do {
      if (iter==&swig_module) {
        /* Our module is already in the list, so there's nothing more to do. */
        return;
      }
      iter=iter->next;
    } while (iter!= module_head);

    /* otherwise we must add our module into the list */
    swig_module.next = module_head->next;
    module_head->next = &swig_module;
  }

  /* When multiple interpreters are used, a module could have already been initialized in
     a different interpreter, but not yet have a pointer in this interpreter.
     In this case, we do not want to continue adding types... everything should be
     set up already */
  if (init == 0) return;

  /* Now work on filling in swig_module.types */
#ifdef SWIGRUNTIME_DEBUG
  printf("SWIG_InitializeModule: size %lu\n", (unsigned long)swig_module.size);
#endif
  for (i = 0; i < swig_module.size; ++i) {
    swig_type_info *type = 0;
    swig_type_info *ret;
    swig_cast_info *cast;

#ifdef SWIGRUNTIME_DEBUG
    printf("SWIG_InitializeModule: type %lu %s\n", (unsigned long)i, swig_module.type_initial[i]->name);
#endif

    /* if there is another module already loaded */
    if (swig_module.next != &swig_module) {
      type = SWIG_MangledTypeQueryModule(swig_module.next, &swig_module, swig_module.type_initial[i]->name);
    }
    if (type) {
      /* Overwrite clientdata field */
#ifdef SWIGRUNTIME_DEBUG
      printf("SWIG_InitializeModule: found type %s\n", type->name);
#endif
      if (swig_module.type_initial[i]->clientdata) {
	type->clientdata = swig_module.type_initial[i]->clientdata;
#ifdef SWIGRUNTIME_DEBUG
      printf("SWIG_InitializeModule: found and overwrite type %s \n", type->name);
#endif
      }
    } else {
      type = swig_module.type_initial[i];
    }

    /* Insert casting types */
    cast = swig_module.cast_initial[i];
    while (cast->type) {

      /* Don't need to add information already in the list */
      ret = 0;
#ifdef SWIGRUNTIME_DEBUG
      printf("SWIG_InitializeModule: look cast %s\n", cast->type->name);
#endif
      if (swig_module.next != &swig_module) {
        ret = SWIG_MangledTypeQueryModule(swig_module.next, &swig_module, cast->type->name);
#ifdef SWIGRUNTIME_DEBUG
	if (ret) printf("SWIG_InitializeModule: found cast %s\n", ret->name);
#endif
      }
      if (ret) {
	if (type == swig_module.type_initial[i]) {
#ifdef SWIGRUNTIME_DEBUG
	  printf("SWIG_InitializeModule: skip old type %s\n", ret->name);
#endif
	  cast->type = ret;
	  ret = 0;
	} else {
	  /* Check for casting already in the list */
	  swig_cast_info *ocast = SWIG_TypeCheck(ret->name, type);
#ifdef SWIGRUNTIME_DEBUG
	  if (ocast) printf("SWIG_InitializeModule: skip old cast %s\n", ret->name);
#endif
	  if (!ocast) ret = 0;
	}
      }

      if (!ret) {
#ifdef SWIGRUNTIME_DEBUG
	printf("SWIG_InitializeModule: adding cast %s\n", cast->type->name);
#endif
        if (type->cast) {
          type->cast->prev = cast;
          cast->next = type->cast;
        }
        type->cast = cast;
      }
      cast++;
    }
    /* Set entry in modules->types array equal to the type */
    swig_module.types[i] = type;
  }
  swig_module.types[i] = 0;

#ifdef SWIGRUNTIME_DEBUG
  printf("**** SWIG_InitializeModule: Cast List ******\n");
  for (i = 0; i < swig_module.size; ++i) {
    int j = 0;
    swig_cast_info *cast = swig_module.cast_initial[i];
    printf("SWIG_InitializeModule: type %lu %s\n", (unsigned long)i, swig_module.type_initial[i]->name);
    while (cast->type) {
      printf("SWIG_InitializeModule: cast type %s\n", cast->type->name);
      cast++;
      ++j;
    }
  printf("---- Total casts: %d\n",j);
  }
  printf("**** SWIG_InitializeModule: Cast List ******\n");
#endif
}

/* This function will propagate the clientdata field of type to
* any new swig_type_info structures that have been added into the list
* of equivalent types.  It is like calling
* SWIG_TypeClientData(type, clientdata) a second time.
*/
SWIGRUNTIME void
SWIG_PropagateClientData(void) {
  size_t i;
  swig_cast_info *equiv;
  static int init_run = 0;

  if (init_run) return;
  init_run = 1;

  for (i = 0; i < swig_module.size; i++) {
    if (swig_module.types[i]->clientdata) {
      equiv = swig_module.types[i]->cast;
      while (equiv) {
        if (!equiv->converter) {
          if (equiv->type && !equiv->type->clientdata)
            SWIG_TypeClientData(equiv->type, swig_module.types[i]->clientdata);
        }
        equiv = equiv->next;
      }
    }
  }
}

#ifdef __cplusplus
#if 0
{ /* c-mode */
#endif
}
#endif


Napi::Object Init(Napi::Env env, Napi::Object exports) {
  SWIG_InitializeModule(env);



extern void CreateTSFN(const Napi::CallbackInfo &info);
extern void FreeTSFN(const Napi::CallbackInfo &info);
extern void SendLinMsg(const Napi::CallbackInfo &info);


do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("CreateTSFN", CreateTSFN);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);

do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("FreeTSFN", FreeTSFN);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
	pd
  }));
} while (0);

do{
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("SendLinMsg", SendLinMsg);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
	pd
  }));
} while (0);



  Napi::Function SWIG_NAPI_ObjectWrap_ctor = SWIG_NAPI_ObjectWrap_inst::GetClass(env);
  Napi::FunctionReference *SWIG_NAPI_ObjectWrap_ctor_ref = new Napi::FunctionReference();
  *SWIG_NAPI_ObjectWrap_ctor_ref = Napi::Persistent(SWIG_NAPI_ObjectWrap_ctor);
  env.GetInstanceData<EnvInstanceData>()->SWIG_NAPI_ObjectWrapCtor = SWIG_NAPI_ObjectWrap_ctor_ref;

  /* create objects for namespaces */
  

  /* register classes */
  /* Class: UINT8P (_exports_UINT8P) */
// jsnapi_registerclass
Napi::Function _exports_UINT8P_ctor = _exports_UINT8P_inst::GetClass(env);
exports.Set("UINT8P", _exports_UINT8P_ctor);
if (SWIGTYPE_p_UINT8P->clientdata == nullptr) {
  SWIGTYPE_p_UINT8P->clientdata = new size_t(0);
}
Napi::FunctionReference *_exports_UINT8P_ctor_ref = new Napi::FunctionReference();
*_exports_UINT8P_ctor_ref = Napi::Persistent(_exports_UINT8P_ctor);
env.GetInstanceData<EnvInstanceData>()->ctor[0] = _exports_UINT8P_ctor_ref;
/* Class: LIN_MSG_ARRAY (_exports_LIN_MSG_ARRAY) */
// jsnapi_registerclass
Napi::Function _exports_LIN_MSG_ARRAY_ctor = _exports_LIN_MSG_ARRAY_inst::GetClass(env);
exports.Set("LIN_MSG_ARRAY", _exports_LIN_MSG_ARRAY_ctor);
if (SWIGTYPE_p_LIN_MSG_ARRAY->clientdata == nullptr) {
  SWIGTYPE_p_LIN_MSG_ARRAY->clientdata = new size_t(1);
}
Napi::FunctionReference *_exports_LIN_MSG_ARRAY_ctor_ref = new Napi::FunctionReference();
*_exports_LIN_MSG_ARRAY_ctor_ref = Napi::Persistent(_exports_LIN_MSG_ARRAY_ctor);
env.GetInstanceData<EnvInstanceData>()->ctor[1] = _exports_LIN_MSG_ARRAY_ctor_ref;
/* Class: I32Array (_exports_I32Array) */
// jsnapi_registerclass
Napi::Function _exports_I32Array_ctor = _exports_I32Array_inst::GetClass(env);
exports.Set("I32Array", _exports_I32Array_ctor);
if (SWIGTYPE_p_I32Array->clientdata == nullptr) {
  SWIGTYPE_p_I32Array->clientdata = new size_t(2);
}
Napi::FunctionReference *_exports_I32Array_ctor_ref = new Napi::FunctionReference();
*_exports_I32Array_ctor_ref = Napi::Persistent(_exports_I32Array_ctor);
env.GetInstanceData<EnvInstanceData>()->ctor[2] = _exports_I32Array_ctor_ref;
/* Class: U32Array (_exports_U32Array) */
// jsnapi_registerclass
Napi::Function _exports_U32Array_ctor = _exports_U32Array_inst::GetClass(env);
exports.Set("U32Array", _exports_U32Array_ctor);
if (SWIGTYPE_p_U32Array->clientdata == nullptr) {
  SWIGTYPE_p_U32Array->clientdata = new size_t(3);
}
Napi::FunctionReference *_exports_U32Array_ctor_ref = new Napi::FunctionReference();
*_exports_U32Array_ctor_ref = Napi::Persistent(_exports_U32Array_ctor);
env.GetInstanceData<EnvInstanceData>()->ctor[3] = _exports_U32Array_ctor_ref;
/* Class: ByteArray (_exports_ByteArray) */
// jsnapi_registerclass
Napi::Function _exports_ByteArray_ctor = _exports_ByteArray_inst::GetClass(env);
exports.Set("ByteArray", _exports_ByteArray_ctor);
if (SWIGTYPE_p_ByteArray->clientdata == nullptr) {
  SWIGTYPE_p_ByteArray->clientdata = new size_t(4);
}
Napi::FunctionReference *_exports_ByteArray_ctor_ref = new Napi::FunctionReference();
*_exports_ByteArray_ctor_ref = Napi::Persistent(_exports_ByteArray_ctor);
env.GetInstanceData<EnvInstanceData>()->ctor[4] = _exports_ByteArray_ctor_ref;
/* Class: LIN_EX_MSG (_exports_LIN_EX_MSG) */
// jsnapi_registerclass
Napi::Function _exports_LIN_EX_MSG_ctor = _exports_LIN_EX_MSG_inst::GetClass(env);
exports.Set("LIN_EX_MSG", _exports_LIN_EX_MSG_ctor);
if (SWIGTYPE_p__LIN_EX_MSG->clientdata == nullptr) {
  SWIGTYPE_p__LIN_EX_MSG->clientdata = new size_t(5);
}
Napi::FunctionReference *_exports_LIN_EX_MSG_ctor_ref = new Napi::FunctionReference();
*_exports_LIN_EX_MSG_ctor_ref = Napi::Persistent(_exports_LIN_EX_MSG_ctor);
env.GetInstanceData<EnvInstanceData>()->ctor[5] = _exports_LIN_EX_MSG_ctor_ref;
/* Class: DEVICE_INFO (_exports_DEVICE_INFO) */
// jsnapi_registerclass
Napi::Function _exports_DEVICE_INFO_ctor = _exports_DEVICE_INFO_inst::GetClass(env);
exports.Set("DEVICE_INFO", _exports_DEVICE_INFO_ctor);
if (SWIGTYPE_p__DEVICE_INFO->clientdata == nullptr) {
  SWIGTYPE_p__DEVICE_INFO->clientdata = new size_t(6);
}
Napi::FunctionReference *_exports_DEVICE_INFO_ctor_ref = new Napi::FunctionReference();
*_exports_DEVICE_INFO_ctor_ref = Napi::Persistent(_exports_DEVICE_INFO_ctor);
env.GetInstanceData<EnvInstanceData>()->ctor[6] = _exports_DEVICE_INFO_ctor_ref;
/* Class: HARDWARE_INFO (_exports_HARDWARE_INFO) */
// jsnapi_registerclass
Napi::Function _exports_HARDWARE_INFO_ctor = _exports_HARDWARE_INFO_inst::GetClass(env);
exports.Set("HARDWARE_INFO", _exports_HARDWARE_INFO_ctor);
if (SWIGTYPE_p_HARDWARE_INFO->clientdata == nullptr) {
  SWIGTYPE_p_HARDWARE_INFO->clientdata = new size_t(7);
}
Napi::FunctionReference *_exports_HARDWARE_INFO_ctor_ref = new Napi::FunctionReference();
*_exports_HARDWARE_INFO_ctor_ref = Napi::Persistent(_exports_HARDWARE_INFO_ctor);
env.GetInstanceData<EnvInstanceData>()->ctor[7] = _exports_HARDWARE_INFO_ctor_ref;


  /* enable inheritance */
  
Napi::Value jsObjectValue, jsSetProtoValue;
Napi::Object jsObject;
Napi::Function setProto;
NAPI_CHECK_RESULT(env.Global().Get("Object"), jsObjectValue);
NAPI_CHECK_RESULT(jsObjectValue.ToObject(), jsObject);
NAPI_CHECK_RESULT(jsObject.Get("setPrototypeOf"), jsSetProtoValue);
setProto = jsSetProtoValue.As<Napi::Function>();



  /* setup inheritances */
  
// Inheritance for _exports_UINT8P (UINT8P) <- SWIG_NAPI_ObjectWrap
// jsnapi_setup_inheritance
do {
  Napi::Value protoBase, protoSub;
  NAPI_CHECK_RESULT(_exports_UINT8P_ctor.Get("prototype"), protoSub);
  NAPI_CHECK_RESULT(SWIG_NAPI_ObjectWrap_ctor.Get("prototype"), protoBase);
  NAPI_CHECK_MAYBE(setProto.Call({
    _exports_UINT8P_ctor, SWIG_NAPI_ObjectWrap_ctor
  }));
  NAPI_CHECK_MAYBE(setProto.Call({
    protoSub, protoBase
  }));
} while (0);


// Inheritance for _exports_LIN_MSG_ARRAY (LIN_MSG_ARRAY) <- SWIG_NAPI_ObjectWrap
// jsnapi_setup_inheritance
do {
  Napi::Value protoBase, protoSub;
  NAPI_CHECK_RESULT(_exports_LIN_MSG_ARRAY_ctor.Get("prototype"), protoSub);
  NAPI_CHECK_RESULT(SWIG_NAPI_ObjectWrap_ctor.Get("prototype"), protoBase);
  NAPI_CHECK_MAYBE(setProto.Call({
    _exports_LIN_MSG_ARRAY_ctor, SWIG_NAPI_ObjectWrap_ctor
  }));
  NAPI_CHECK_MAYBE(setProto.Call({
    protoSub, protoBase
  }));
} while (0);


// Inheritance for _exports_I32Array (I32Array) <- SWIG_NAPI_ObjectWrap
// jsnapi_setup_inheritance
do {
  Napi::Value protoBase, protoSub;
  NAPI_CHECK_RESULT(_exports_I32Array_ctor.Get("prototype"), protoSub);
  NAPI_CHECK_RESULT(SWIG_NAPI_ObjectWrap_ctor.Get("prototype"), protoBase);
  NAPI_CHECK_MAYBE(setProto.Call({
    _exports_I32Array_ctor, SWIG_NAPI_ObjectWrap_ctor
  }));
  NAPI_CHECK_MAYBE(setProto.Call({
    protoSub, protoBase
  }));
} while (0);


// Inheritance for _exports_U32Array (U32Array) <- SWIG_NAPI_ObjectWrap
// jsnapi_setup_inheritance
do {
  Napi::Value protoBase, protoSub;
  NAPI_CHECK_RESULT(_exports_U32Array_ctor.Get("prototype"), protoSub);
  NAPI_CHECK_RESULT(SWIG_NAPI_ObjectWrap_ctor.Get("prototype"), protoBase);
  NAPI_CHECK_MAYBE(setProto.Call({
    _exports_U32Array_ctor, SWIG_NAPI_ObjectWrap_ctor
  }));
  NAPI_CHECK_MAYBE(setProto.Call({
    protoSub, protoBase
  }));
} while (0);


// Inheritance for _exports_ByteArray (ByteArray) <- SWIG_NAPI_ObjectWrap
// jsnapi_setup_inheritance
do {
  Napi::Value protoBase, protoSub;
  NAPI_CHECK_RESULT(_exports_ByteArray_ctor.Get("prototype"), protoSub);
  NAPI_CHECK_RESULT(SWIG_NAPI_ObjectWrap_ctor.Get("prototype"), protoBase);
  NAPI_CHECK_MAYBE(setProto.Call({
    _exports_ByteArray_ctor, SWIG_NAPI_ObjectWrap_ctor
  }));
  NAPI_CHECK_MAYBE(setProto.Call({
    protoSub, protoBase
  }));
} while (0);


// Inheritance for _exports_LIN_EX_MSG (LIN_EX_MSG) <- SWIG_NAPI_ObjectWrap
// jsnapi_setup_inheritance
do {
  Napi::Value protoBase, protoSub;
  NAPI_CHECK_RESULT(_exports_LIN_EX_MSG_ctor.Get("prototype"), protoSub);
  NAPI_CHECK_RESULT(SWIG_NAPI_ObjectWrap_ctor.Get("prototype"), protoBase);
  NAPI_CHECK_MAYBE(setProto.Call({
    _exports_LIN_EX_MSG_ctor, SWIG_NAPI_ObjectWrap_ctor
  }));
  NAPI_CHECK_MAYBE(setProto.Call({
    protoSub, protoBase
  }));
} while (0);


// Inheritance for _exports_DEVICE_INFO (DEVICE_INFO) <- SWIG_NAPI_ObjectWrap
// jsnapi_setup_inheritance
do {
  Napi::Value protoBase, protoSub;
  NAPI_CHECK_RESULT(_exports_DEVICE_INFO_ctor.Get("prototype"), protoSub);
  NAPI_CHECK_RESULT(SWIG_NAPI_ObjectWrap_ctor.Get("prototype"), protoBase);
  NAPI_CHECK_MAYBE(setProto.Call({
    _exports_DEVICE_INFO_ctor, SWIG_NAPI_ObjectWrap_ctor
  }));
  NAPI_CHECK_MAYBE(setProto.Call({
    protoSub, protoBase
  }));
} while (0);


// Inheritance for _exports_HARDWARE_INFO (HARDWARE_INFO) <- SWIG_NAPI_ObjectWrap
// jsnapi_setup_inheritance
do {
  Napi::Value protoBase, protoSub;
  NAPI_CHECK_RESULT(_exports_HARDWARE_INFO_ctor.Get("prototype"), protoSub);
  NAPI_CHECK_RESULT(SWIG_NAPI_ObjectWrap_ctor.Get("prototype"), protoBase);
  NAPI_CHECK_MAYBE(setProto.Call({
    _exports_HARDWARE_INFO_ctor, SWIG_NAPI_ObjectWrap_ctor
  }));
  NAPI_CHECK_MAYBE(setProto.Call({
    protoSub, protoBase
  }));
} while (0);



  /* create and register namespace objects */
  // jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_EX_SUCCESS_get, JS_veto_set_variable>("LIN_EX_SUCCESS");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_EX_ERR_NOT_SUPPORT_get, JS_veto_set_variable>("LIN_EX_ERR_NOT_SUPPORT");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_EX_ERR_USB_WRITE_FAIL_get, JS_veto_set_variable>("LIN_EX_ERR_USB_WRITE_FAIL");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_EX_ERR_USB_READ_FAIL_get, JS_veto_set_variable>("LIN_EX_ERR_USB_READ_FAIL");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_EX_ERR_CMD_FAIL_get, JS_veto_set_variable>("LIN_EX_ERR_CMD_FAIL");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_EX_ERR_CH_NO_INIT_get, JS_veto_set_variable>("LIN_EX_ERR_CH_NO_INIT");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_EX_ERR_READ_DATA_get, JS_veto_set_variable>("LIN_EX_ERR_READ_DATA");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_EX_ERR_PARAMETER_get, JS_veto_set_variable>("LIN_EX_ERR_PARAMETER");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_EX_CHECK_STD_get, JS_veto_set_variable>("LIN_EX_CHECK_STD");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_EX_CHECK_EXT_get, JS_veto_set_variable>("LIN_EX_CHECK_EXT");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_EX_CHECK_USER_get, JS_veto_set_variable>("LIN_EX_CHECK_USER");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_EX_CHECK_NONE_get, JS_veto_set_variable>("LIN_EX_CHECK_NONE");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_EX_CHECK_ERROR_get, JS_veto_set_variable>("LIN_EX_CHECK_ERROR");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_EX_MASTER_get, JS_veto_set_variable>("LIN_EX_MASTER");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_EX_SLAVE_get, JS_veto_set_variable>("LIN_EX_SLAVE");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_EX_VBAT_0V_get, JS_veto_set_variable>("LIN_EX_VBAT_0V");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_EX_VBAT_12V_get, JS_veto_set_variable>("LIN_EX_VBAT_12V");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_EX_VBAT_5V_get, JS_veto_set_variable>("LIN_EX_VBAT_5V");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_EX_MSG_TYPE_UN_get, JS_veto_set_variable>("LIN_EX_MSG_TYPE_UN");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_EX_MSG_TYPE_MW_get, JS_veto_set_variable>("LIN_EX_MSG_TYPE_MW");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_EX_MSG_TYPE_MR_get, JS_veto_set_variable>("LIN_EX_MSG_TYPE_MR");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_EX_MSG_TYPE_SW_get, JS_veto_set_variable>("LIN_EX_MSG_TYPE_SW");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_EX_MSG_TYPE_SR_get, JS_veto_set_variable>("LIN_EX_MSG_TYPE_SR");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_EX_MSG_TYPE_BK_get, JS_veto_set_variable>("LIN_EX_MSG_TYPE_BK");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_EX_MSG_TYPE_SY_get, JS_veto_set_variable>("LIN_EX_MSG_TYPE_SY");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_EX_MSG_TYPE_ID_get, JS_veto_set_variable>("LIN_EX_MSG_TYPE_ID");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_EX_MSG_TYPE_DT_get, JS_veto_set_variable>("LIN_EX_MSG_TYPE_DT");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_EX_MSG_TYPE_CK_get, JS_veto_set_variable>("LIN_EX_MSG_TYPE_CK");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_EX_Init", _wrap_LIN_EX_Init);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_EX_Init2", _wrap_LIN_EX_Init2);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_EX_MasterSync", _wrap_LIN_EX_MasterSync);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_EX_MasterBreak", _wrap_LIN_EX_MasterBreak);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_EX_MasterWrite", _wrap_LIN_EX_MasterWrite);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_EX_MasterRead", _wrap_LIN_EX_MasterRead);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_EX_SlaveSetIDMode", _wrap_LIN_EX_SlaveSetIDMode);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_EX_SlaveGetIDMode", _wrap_LIN_EX_SlaveGetIDMode);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_EX_SlaveGetData", _wrap_LIN_EX_SlaveGetData);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_EX_CtrlPowerOut", _wrap_LIN_EX_CtrlPowerOut);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_EX_GetVbatValue", _wrap_LIN_EX_GetVbatValue);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_EX_MasterStartSch", _wrap_LIN_EX_MasterStartSch);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_EX_MasterStopSch", _wrap_LIN_EX_MasterStopSch);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_EX_MasterGetSchState", _wrap_LIN_EX_MasterGetSchState);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_EX_MasterGetSch", _wrap_LIN_EX_MasterGetSch);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_EX_MasterSetSchRunTimes", _wrap_LIN_EX_MasterSetSchRunTimes);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_EX_DecodeListFile", _wrap_LIN_EX_DecodeListFile);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_EX_GetListFileMsg", _wrap_LIN_EX_GetListFileMsg);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_EX_GetStartTime", _wrap_LIN_EX_GetStartTime);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_EX_ResetStartTime", _wrap_LIN_EX_ResetStartTime);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_EX_Stop", _wrap_LIN_EX_Stop);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_POWER_LEVEL_1V8_get, JS_veto_set_variable>("POWER_LEVEL_1V8");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_POWER_LEVEL_2V5_get, JS_veto_set_variable>("POWER_LEVEL_2V5");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_POWER_LEVEL_3V3_get, JS_veto_set_variable>("POWER_LEVEL_3V3");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("USB_ScanDevice", _wrap_USB_ScanDevice);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("USB_OpenDevice", _wrap_USB_OpenDevice);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("USB_CloseDevice", _wrap_USB_CloseDevice);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("USB_ResetDevice", _wrap_USB_ResetDevice);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("USB_RetryConnect", _wrap_USB_RetryConnect);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("USB_WaitResume", _wrap_USB_WaitResume);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("DEV_GetDeviceInfo", _wrap_DEV_GetDeviceInfo);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("DEV_GetHardwareInfo", _wrap_DEV_GetHardwareInfo);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("DEV_EraseUserData", _wrap_DEV_EraseUserData);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("DEV_EraseUserDataSector", _wrap_DEV_EraseUserDataSector);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("DEV_WriteUserData", _wrap_DEV_WriteUserData);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("DEV_ReadUserData", _wrap_DEV_ReadUserData);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("DEV_SetPowerLevel", _wrap_DEV_SetPowerLevel);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("DEV_GetTimestamp", _wrap_DEV_GetTimestamp);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("DEV_ResetTimestamp", _wrap_DEV_ResetTimestamp);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("DEV_GetDllBuildTime", _wrap_DEV_GetDllBuildTime);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LoadDll", _wrap_LoadDll);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);


  return exports;
  goto fail;
fail:
  return Napi::Object();
}

NODE_API_MODULE(xmlpp, Init)
