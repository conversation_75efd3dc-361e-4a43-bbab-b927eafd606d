/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (https://www.swig.org).
 * Version 4.2.1
 *
 * Do not make changes to this file unless you know what you are doing - modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */


#define SWIG_VERSION 0x040201
#define SWIGJAVASCRIPT
/* -----------------------------------------------------------------------------
 *  This section contains generic SWIG labels for method/variable
 *  declarations/attributes, and other compiler dependent labels.
 * ----------------------------------------------------------------------------- */

/* template workaround for compilers that cannot correctly implement the C++ standard */
#ifndef SWIGTEMPLATEDISAMBIGUATOR
# if defined(__SUNPRO_CC) && (__SUNPRO_CC <= 0x560)
#  define SWIGTEMPLATEDISAMBIGUATOR template
# elif defined(__HP_aCC)
/* Needed even with `aCC -AA' when `aCC -V' reports HP ANSI C++ B3910B A.03.55 */
/* If we find a maximum version that requires this, the test would be __HP_aCC <= 35500 for A.03.55 */
#  define SWIGTEMPLATEDISAMBIGUATOR template
# else
#  define SWIGTEMPLATEDISAMBIGUATOR
# endif
#endif

/* inline attribute */
#ifndef SWIGINLINE
# if defined(__cplusplus) || (defined(__GNUC__) && !defined(__STRICT_ANSI__))
#   define SWIGINLINE inline
# else
#   define SWIGINLINE
# endif
#endif

/* attribute recognised by some compilers to avoid 'unused' warnings */
#ifndef SWIGUNUSED
# if defined(__GNUC__)
#   if !(defined(__cplusplus)) || (__GNUC__ > 3 || (__GNUC__ == 3 && __GNUC_MINOR__ >= 4))
#     define SWIGUNUSED __attribute__ ((__unused__))
#   else
#     define SWIGUNUSED
#   endif
# elif defined(__ICC)
#   define SWIGUNUSED __attribute__ ((__unused__))
# else
#   define SWIGUNUSED
# endif
#endif

#ifndef SWIG_MSC_UNSUPPRESS_4505
# if defined(_MSC_VER)
#   pragma warning(disable : 4505) /* unreferenced local function has been removed */
# endif
#endif

#ifndef SWIGUNUSEDPARM
# ifdef __cplusplus
#   define SWIGUNUSEDPARM(p)
# else
#   define SWIGUNUSEDPARM(p) p SWIGUNUSED
# endif
#endif

/* internal SWIG method */
#ifndef SWIGINTERN
# define SWIGINTERN static SWIGUNUSED
#endif

/* internal inline SWIG method */
#ifndef SWIGINTERNINLINE
# define SWIGINTERNINLINE SWIGINTERN SWIGINLINE
#endif

/* exporting methods */
#if defined(__GNUC__)
#  if (__GNUC__ >= 4) || (__GNUC__ == 3 && __GNUC_MINOR__ >= 4)
#    ifndef GCC_HASCLASSVISIBILITY
#      define GCC_HASCLASSVISIBILITY
#    endif
#  endif
#endif

#ifndef SWIGEXPORT
# if defined(_WIN32) || defined(__WIN32__) || defined(__CYGWIN__)
#   if defined(STATIC_LINKED)
#     define SWIGEXPORT
#   else
#     define SWIGEXPORT __declspec(dllexport)
#   endif
# else
#   if defined(__GNUC__) && defined(GCC_HASCLASSVISIBILITY)
#     define SWIGEXPORT __attribute__ ((visibility("default")))
#   else
#     define SWIGEXPORT
#   endif
# endif
#endif

/* calling conventions for Windows */
#ifndef SWIGSTDCALL
# if defined(_WIN32) || defined(__WIN32__) || defined(__CYGWIN__)
#   define SWIGSTDCALL __stdcall
# else
#   define SWIGSTDCALL
# endif
#endif

/* Deal with Microsoft's attempt at deprecating C standard runtime functions */
#if !defined(SWIG_NO_CRT_SECURE_NO_DEPRECATE) && defined(_MSC_VER) && !defined(_CRT_SECURE_NO_DEPRECATE)
# define _CRT_SECURE_NO_DEPRECATE
#endif

/* Deal with Microsoft's attempt at deprecating methods in the standard C++ library */
#if !defined(SWIG_NO_SCL_SECURE_NO_DEPRECATE) && defined(_MSC_VER) && !defined(_SCL_SECURE_NO_DEPRECATE)
# define _SCL_SECURE_NO_DEPRECATE
#endif

/* Deal with Apple's deprecated 'AssertMacros.h' from Carbon-framework */
#if defined(__APPLE__) && !defined(__ASSERT_MACROS_DEFINE_VERSIONS_WITHOUT_UNDERSCORES)
# define __ASSERT_MACROS_DEFINE_VERSIONS_WITHOUT_UNDERSCORES 0
#endif

/* Intel's compiler complains if a variable which was never initialised is
 * cast to void, which is a common idiom which we use to indicate that we
 * are aware a variable isn't used.  So we just silence that warning.
 * See: https://github.com/swig/swig/issues/192 for more discussion.
 */
#ifdef __INTEL_COMPILER
# pragma warning disable 592
#endif

#if defined(__cplusplus) && __cplusplus >=201103L
# define SWIG_NULLPTR nullptr
#else
# define SWIG_NULLPTR NULL
#endif 

/* -----------------------------------------------------------------------------
 * swigcompat.swg
 *
 * Macros to provide support compatibility with older C and C++ standards.
 * ----------------------------------------------------------------------------- */

/* C99 and C++11 should provide snprintf, but define SWIG_NO_SNPRINTF
 * if you're missing it.
 */
#if ((defined __STDC_VERSION__ && __STDC_VERSION__ >= 199901L) || \
     (defined __cplusplus && __cplusplus >= 201103L) || \
     defined SWIG_HAVE_SNPRINTF) && \
    !defined SWIG_NO_SNPRINTF
# define SWIG_snprintf(O,S,F,A) snprintf(O,S,F,A)
# define SWIG_snprintf2(O,S,F,A,B) snprintf(O,S,F,A,B)
#else
/* Fallback versions ignore the buffer size, but most of our uses either have a
 * fixed maximum possible size or dynamically allocate a buffer that's large
 * enough.
 */
# define SWIG_snprintf(O,S,F,A) sprintf(O,F,A)
# define SWIG_snprintf2(O,S,F,A,B) sprintf(O,F,A,B)
#endif


#define SWIG_FromCharPtrAndSize(cptr, size) SWIG_Env_FromCharPtrAndSize(env, cptr, size)
#define SWIG_FromCharPtr(cptr)              SWIG_Env_FromCharPtrAndSize(env, cptr, strlen(cptr))


#define SWIG_NAPI_FROM_DECL_ARGS(arg1)              (Napi::Env env, arg1)
#define SWIG_NAPI_FROM_CALL_ARGS(arg1)              (env, arg1)



#define SWIG_exception_fail(code, msg) do { SWIG_Error(code, msg); SWIG_fail; } while(0) 

#define SWIG_contract_assert(expr, msg) do { if (!(expr)) { SWIG_Error(SWIG_RuntimeError, msg); SWIG_fail; } } while (0) 



#if defined(_CPPUNWIND) || defined(__EXCEPTIONS)
#define NAPI_CPP_EXCEPTIONS
#else
#define NAPI_DISABLE_CPP_EXCEPTIONS
#define NODE_ADDON_API_ENABLE_MAYBE
#endif

// This gives us
// Branch Node.js v10.x - from v10.20.0
// Branch Node.js v12.x - from v12.17.0
// Everything from Node.js v14.0.0 on
// Our limiting feature is napi_set_instance_data
#ifndef NAPI_VERSION
#define NAPI_VERSION 6
#elif NAPI_VERSION < 6
#error NAPI_VERSION 6 is the minimum supported target (Node.js >=14, >=12.17, >=10.20)
#endif
#include <napi.h>

#include <errno.h>
#include <limits.h>
#include <stdlib.h>
#include <assert.h>
#include <map>

/* -----------------------------------------------------------------------------
 * swigrun.swg
 *
 * This file contains generic C API SWIG runtime support for pointer
 * type checking.
 * ----------------------------------------------------------------------------- */

/* This should only be incremented when either the layout of swig_type_info changes,
   or for whatever reason, the runtime changes incompatibly */
#define SWIG_RUNTIME_VERSION "4"

/* define SWIG_TYPE_TABLE_NAME as "SWIG_TYPE_TABLE" */
#ifdef SWIG_TYPE_TABLE
# define SWIG_QUOTE_STRING(x) #x
# define SWIG_EXPAND_AND_QUOTE_STRING(x) SWIG_QUOTE_STRING(x)
# define SWIG_TYPE_TABLE_NAME SWIG_EXPAND_AND_QUOTE_STRING(SWIG_TYPE_TABLE)
#else
# define SWIG_TYPE_TABLE_NAME
#endif

/*
  You can use the SWIGRUNTIME and SWIGRUNTIMEINLINE macros for
  creating a static or dynamic library from the SWIG runtime code.
  In 99.9% of the cases, SWIG just needs to declare them as 'static'.

  But only do this if strictly necessary, ie, if you have problems
  with your compiler or suchlike.
*/

#ifndef SWIGRUNTIME
# define SWIGRUNTIME SWIGINTERN
#endif

#ifndef SWIGRUNTIMEINLINE
# define SWIGRUNTIMEINLINE SWIGRUNTIME SWIGINLINE
#endif

/*  Generic buffer size */
#ifndef SWIG_BUFFER_SIZE
# define SWIG_BUFFER_SIZE 1024
#endif

/* Flags for pointer conversions */
#define SWIG_POINTER_DISOWN        0x1
#define SWIG_CAST_NEW_MEMORY       0x2
#define SWIG_POINTER_NO_NULL       0x4
#define SWIG_POINTER_CLEAR         0x8
#define SWIG_POINTER_RELEASE       (SWIG_POINTER_CLEAR | SWIG_POINTER_DISOWN)

/* Flags for new pointer objects */
#define SWIG_POINTER_OWN           0x1


/*
   Flags/methods for returning states.

   The SWIG conversion methods, as ConvertPtr, return an integer
   that tells if the conversion was successful or not. And if not,
   an error code can be returned (see swigerrors.swg for the codes).

   Use the following macros/flags to set or process the returning
   states.

   In old versions of SWIG, code such as the following was usually written:

     if (SWIG_ConvertPtr(obj,vptr,ty.flags) != -1) {
       // success code
     } else {
       //fail code
     }

   Now you can be more explicit:

    int res = SWIG_ConvertPtr(obj,vptr,ty.flags);
    if (SWIG_IsOK(res)) {
      // success code
    } else {
      // fail code
    }

   which is the same really, but now you can also do

    Type *ptr;
    int res = SWIG_ConvertPtr(obj,(void **)(&ptr),ty.flags);
    if (SWIG_IsOK(res)) {
      // success code
      if (SWIG_IsNewObj(res) {
        ...
	delete *ptr;
      } else {
        ...
      }
    } else {
      // fail code
    }

   I.e., now SWIG_ConvertPtr can return new objects and you can
   identify the case and take care of the deallocation. Of course that
   also requires SWIG_ConvertPtr to return new result values, such as

      int SWIG_ConvertPtr(obj, ptr,...) {
        if (<obj is ok>) {
          if (<need new object>) {
            *ptr = <ptr to new allocated object>;
            return SWIG_NEWOBJ;
          } else {
            *ptr = <ptr to old object>;
            return SWIG_OLDOBJ;
          }
        } else {
          return SWIG_BADOBJ;
        }
      }

   Of course, returning the plain '0(success)/-1(fail)' still works, but you can be
   more explicit by returning SWIG_BADOBJ, SWIG_ERROR or any of the
   SWIG errors code.

   Finally, if the SWIG_CASTRANK_MODE is enabled, the result code
   allows returning the 'cast rank', for example, if you have this

       int food(double)
       int fooi(int);

   and you call

      food(1)   // cast rank '1'  (1 -> 1.0)
      fooi(1)   // cast rank '0'

   just use the SWIG_AddCast()/SWIG_CheckState()
*/

#define SWIG_OK                    (0)
/* Runtime errors are < 0 */
#define SWIG_ERROR                 (-1)
/* Errors in range -1 to -99 are in swigerrors.swg (errors for all languages including those not using the runtime) */
/* Errors in range -100 to -199 are language specific errors defined in *errors.swg */
/* Errors < -200 are generic runtime specific errors */
#define SWIG_ERROR_RELEASE_NOT_OWNED (-200)

#define SWIG_IsOK(r)               (r >= 0)
#define SWIG_ArgError(r)           ((r != SWIG_ERROR) ? r : SWIG_TypeError)

/* The CastRankLimit says how many bits are used for the cast rank */
#define SWIG_CASTRANKLIMIT         (1 << 8)
/* The NewMask denotes the object was created (using new/malloc) */
#define SWIG_NEWOBJMASK            (SWIG_CASTRANKLIMIT  << 1)
/* The TmpMask is for in/out typemaps that use temporary objects */
#define SWIG_TMPOBJMASK            (SWIG_NEWOBJMASK << 1)
/* Simple returning values */
#define SWIG_BADOBJ                (SWIG_ERROR)
#define SWIG_OLDOBJ                (SWIG_OK)
#define SWIG_NEWOBJ                (SWIG_OK | SWIG_NEWOBJMASK)
#define SWIG_TMPOBJ                (SWIG_OK | SWIG_TMPOBJMASK)
/* Check, add and del object mask methods */
#define SWIG_AddNewMask(r)         (SWIG_IsOK(r) ? (r | SWIG_NEWOBJMASK) : r)
#define SWIG_DelNewMask(r)         (SWIG_IsOK(r) ? (r & ~SWIG_NEWOBJMASK) : r)
#define SWIG_IsNewObj(r)           (SWIG_IsOK(r) && (r & SWIG_NEWOBJMASK))
#define SWIG_AddTmpMask(r)         (SWIG_IsOK(r) ? (r | SWIG_TMPOBJMASK) : r)
#define SWIG_DelTmpMask(r)         (SWIG_IsOK(r) ? (r & ~SWIG_TMPOBJMASK) : r)
#define SWIG_IsTmpObj(r)           (SWIG_IsOK(r) && (r & SWIG_TMPOBJMASK))

/* Cast-Rank Mode */
#if defined(SWIG_CASTRANK_MODE)
#  ifndef SWIG_TypeRank
#    define SWIG_TypeRank             unsigned long
#  endif
#  ifndef SWIG_MAXCASTRANK            /* Default cast allowed */
#    define SWIG_MAXCASTRANK          (2)
#  endif
#  define SWIG_CASTRANKMASK          ((SWIG_CASTRANKLIMIT) -1)
#  define SWIG_CastRank(r)           (r & SWIG_CASTRANKMASK)
SWIGINTERNINLINE int SWIG_AddCast(int r) {
  return SWIG_IsOK(r) ? ((SWIG_CastRank(r) < SWIG_MAXCASTRANK) ? (r + 1) : SWIG_ERROR) : r;
}
SWIGINTERNINLINE int SWIG_CheckState(int r) {
  return SWIG_IsOK(r) ? SWIG_CastRank(r) + 1 : 0;
}
#else /* no cast-rank mode */
#  define SWIG_AddCast(r) (r)
#  define SWIG_CheckState(r) (SWIG_IsOK(r) ? 1 : 0)
#endif


#include <string.h>

#ifdef __cplusplus
extern "C" {
#endif

typedef void *(*swig_converter_func)(void *, int *);
typedef struct swig_type_info *(*swig_dycast_func)(void **);

/* Structure to store information on one type */
typedef struct swig_type_info {
  const char             *name;			/* mangled name of this type */
  const char             *str;			/* human readable name of this type */
  swig_dycast_func        dcast;		/* dynamic cast function down a hierarchy */
  struct swig_cast_info  *cast;			/* linked list of types that can cast into this type */
  void                   *clientdata;		/* language specific type data */
  int                    owndata;		/* flag if the structure owns the clientdata */
} swig_type_info;

/* Structure to store a type and conversion function used for casting */
typedef struct swig_cast_info {
  swig_type_info         *type;			/* pointer to type that is equivalent to this type */
  swig_converter_func     converter;		/* function to cast the void pointers */
  struct swig_cast_info  *next;			/* pointer to next cast in linked list */
  struct swig_cast_info  *prev;			/* pointer to the previous cast */
} swig_cast_info;

/* Structure used to store module information
 * Each module generates one structure like this, and the runtime collects
 * all of these structures and stores them in a circularly linked list.*/
typedef struct swig_module_info {
  swig_type_info         **types;		/* Array of pointers to swig_type_info structures that are in this module */
  size_t                 size;		        /* Number of types in this module */
  struct swig_module_info *next;		/* Pointer to next element in circularly linked list */
  swig_type_info         **type_initial;	/* Array of initially generated type structures */
  swig_cast_info         **cast_initial;	/* Array of initially generated casting structures */
  void                    *clientdata;		/* Language specific module data */
} swig_module_info;

/*
  Compare two type names skipping the space characters, therefore
  "char*" == "char *" and "Class<int>" == "Class<int >", etc.

  Return 0 when the two name types are equivalent, as in
  strncmp, but skipping ' '.
*/
SWIGRUNTIME int
SWIG_TypeNameComp(const char *f1, const char *l1,
		  const char *f2, const char *l2) {
  for (;(f1 != l1) && (f2 != l2); ++f1, ++f2) {
    while ((*f1 == ' ') && (f1 != l1)) ++f1;
    while ((*f2 == ' ') && (f2 != l2)) ++f2;
    if (*f1 != *f2) return (*f1 > *f2) ? 1 : -1;
  }
  return (int)((l1 - f1) - (l2 - f2));
}

/*
  Check type equivalence in a name list like <name1>|<name2>|...
  Return 0 if equal, -1 if nb < tb, 1 if nb > tb
*/
SWIGRUNTIME int
SWIG_TypeCmp(const char *nb, const char *tb) {
  int equiv = 1;
  const char* te = tb + strlen(tb);
  const char* ne = nb;
  while (equiv != 0 && *ne) {
    for (nb = ne; *ne; ++ne) {
      if (*ne == '|') break;
    }
    equiv = SWIG_TypeNameComp(nb, ne, tb, te);
    if (*ne) ++ne;
  }
  return equiv;
}

/*
  Check type equivalence in a name list like <name1>|<name2>|...
  Return 0 if not equal, 1 if equal
*/
SWIGRUNTIME int
SWIG_TypeEquiv(const char *nb, const char *tb) {
  return SWIG_TypeCmp(nb, tb) == 0 ? 1 : 0;
}

/*
  Check the typename
*/
SWIGRUNTIME swig_cast_info *
SWIG_TypeCheck(const char *c, swig_type_info *ty) {
  if (ty) {
    swig_cast_info *iter = ty->cast;
    while (iter) {
      if (strcmp(iter->type->name, c) == 0) {
        if (iter == ty->cast)
          return iter;
        /* Move iter to the top of the linked list */
        iter->prev->next = iter->next;
        if (iter->next)
          iter->next->prev = iter->prev;
        iter->next = ty->cast;
        iter->prev = 0;
        if (ty->cast) ty->cast->prev = iter;
        ty->cast = iter;
        return iter;
      }
      iter = iter->next;
    }
  }
  return 0;
}

/*
  Identical to SWIG_TypeCheck, except strcmp is replaced with a pointer comparison
*/
SWIGRUNTIME swig_cast_info *
SWIG_TypeCheckStruct(const swig_type_info *from, swig_type_info *ty) {
  if (ty) {
    swig_cast_info *iter = ty->cast;
    while (iter) {
      if (iter->type == from) {
        if (iter == ty->cast)
          return iter;
        /* Move iter to the top of the linked list */
        iter->prev->next = iter->next;
        if (iter->next)
          iter->next->prev = iter->prev;
        iter->next = ty->cast;
        iter->prev = 0;
        if (ty->cast) ty->cast->prev = iter;
        ty->cast = iter;
        return iter;
      }
      iter = iter->next;
    }
  }
  return 0;
}

/*
  Cast a pointer up an inheritance hierarchy
*/
SWIGRUNTIMEINLINE void *
SWIG_TypeCast(swig_cast_info *ty, void *ptr, int *newmemory) {
  return ((!ty) || (!ty->converter)) ? ptr : (*ty->converter)(ptr, newmemory);
}

/*
   Dynamic pointer casting. Down an inheritance hierarchy
*/
SWIGRUNTIME swig_type_info *
SWIG_TypeDynamicCast(swig_type_info *ty, void **ptr) {
  swig_type_info *lastty = ty;
  if (!ty || !ty->dcast) return ty;
  while (ty && (ty->dcast)) {
    ty = (*ty->dcast)(ptr);
    if (ty) lastty = ty;
  }
  return lastty;
}

/*
  Return the name associated with this type
*/
SWIGRUNTIMEINLINE const char *
SWIG_TypeName(const swig_type_info *ty) {
  return ty->name;
}

/*
  Return the pretty name associated with this type,
  that is an unmangled type name in a form presentable to the user.
*/
SWIGRUNTIME const char *
SWIG_TypePrettyName(const swig_type_info *type) {
  /* The "str" field contains the equivalent pretty names of the
     type, separated by vertical-bar characters.  Choose the last
     name. It should be the most specific; a fully resolved name
     but not necessarily with default template parameters expanded. */
  if (!type) return NULL;
  if (type->str != NULL) {
    const char *last_name = type->str;
    const char *s;
    for (s = type->str; *s; s++)
      if (*s == '|') last_name = s+1;
    return last_name;
  }
  else
    return type->name;
}

/*
   Set the clientdata field for a type
*/
SWIGRUNTIME void
SWIG_TypeClientData(swig_type_info *ti, void *clientdata) {
  swig_cast_info *cast = ti->cast;
  /* if (ti->clientdata == clientdata) return; */
  ti->clientdata = clientdata;

  while (cast) {
    if (!cast->converter) {
      swig_type_info *tc = cast->type;
      if (!tc->clientdata) {
	SWIG_TypeClientData(tc, clientdata);
      }
    }
    cast = cast->next;
  }
}
SWIGRUNTIME void
SWIG_TypeNewClientData(swig_type_info *ti, void *clientdata) {
  SWIG_TypeClientData(ti, clientdata);
  ti->owndata = 1;
}

/*
  Search for a swig_type_info structure only by mangled name
  Search is a O(log #types)

  We start searching at module start, and finish searching when start == end.
  Note: if start == end at the beginning of the function, we go all the way around
  the circular list.
*/
SWIGRUNTIME swig_type_info *
SWIG_MangledTypeQueryModule(swig_module_info *start,
                            swig_module_info *end,
		            const char *name) {
  swig_module_info *iter = start;
  do {
    if (iter->size) {
      size_t l = 0;
      size_t r = iter->size - 1;
      do {
	/* since l+r >= 0, we can (>> 1) instead (/ 2) */
	size_t i = (l + r) >> 1;
	const char *iname = iter->types[i]->name;
	if (iname) {
	  int compare = strcmp(name, iname);
	  if (compare == 0) {
	    return iter->types[i];
	  } else if (compare < 0) {
	    if (i) {
	      r = i - 1;
	    } else {
	      break;
	    }
	  } else if (compare > 0) {
	    l = i + 1;
	  }
	} else {
	  break; /* should never happen */
	}
      } while (l <= r);
    }
    iter = iter->next;
  } while (iter != end);
  return 0;
}

/*
  Search for a swig_type_info structure for either a mangled name or a human readable name.
  It first searches the mangled names of the types, which is a O(log #types)
  If a type is not found it then searches the human readable names, which is O(#types).

  We start searching at module start, and finish searching when start == end.
  Note: if start == end at the beginning of the function, we go all the way around
  the circular list.
*/
SWIGRUNTIME swig_type_info *
SWIG_TypeQueryModule(swig_module_info *start,
                     swig_module_info *end,
		     const char *name) {
  /* STEP 1: Search the name field using binary search */
  swig_type_info *ret = SWIG_MangledTypeQueryModule(start, end, name);
  if (ret) {
    return ret;
  } else {
    /* STEP 2: If the type hasn't been found, do a complete search
       of the str field (the human readable name) */
    swig_module_info *iter = start;
    do {
      size_t i = 0;
      for (; i < iter->size; ++i) {
	if (iter->types[i]->str && (SWIG_TypeEquiv(iter->types[i]->str, name)))
	  return iter->types[i];
      }
      iter = iter->next;
    } while (iter != end);
  }

  /* neither found a match */
  return 0;
}

/*
   Pack binary data into a string
*/
SWIGRUNTIME char *
SWIG_PackData(char *c, void *ptr, size_t sz) {
  static const char hex[17] = "0123456789abcdef";
  const unsigned char *u = (unsigned char *) ptr;
  const unsigned char *eu =  u + sz;
  for (; u != eu; ++u) {
    unsigned char uu = *u;
    *(c++) = hex[(uu & 0xf0) >> 4];
    *(c++) = hex[uu & 0xf];
  }
  return c;
}

/*
   Unpack binary data from a string
*/
SWIGRUNTIME const char *
SWIG_UnpackData(const char *c, void *ptr, size_t sz) {
  unsigned char *u = (unsigned char *) ptr;
  const unsigned char *eu = u + sz;
  for (; u != eu; ++u) {
    char d = *(c++);
    unsigned char uu;
    if ((d >= '0') && (d <= '9'))
      uu = (unsigned char)((d - '0') << 4);
    else if ((d >= 'a') && (d <= 'f'))
      uu = (unsigned char)((d - ('a'-10)) << 4);
    else
      return (char *) 0;
    d = *(c++);
    if ((d >= '0') && (d <= '9'))
      uu |= (unsigned char)(d - '0');
    else if ((d >= 'a') && (d <= 'f'))
      uu |= (unsigned char)(d - ('a'-10));
    else
      return (char *) 0;
    *u = uu;
  }
  return c;
}

/*
   Pack 'void *' into a string buffer.
*/
SWIGRUNTIME char *
SWIG_PackVoidPtr(char *buff, void *ptr, const char *name, size_t bsz) {
  char *r = buff;
  if ((2*sizeof(void *) + 2) > bsz) return 0;
  *(r++) = '_';
  r = SWIG_PackData(r,&ptr,sizeof(void *));
  if (strlen(name) + 1 > (bsz - (r - buff))) return 0;
  strcpy(r,name);
  return buff;
}

SWIGRUNTIME const char *
SWIG_UnpackVoidPtr(const char *c, void **ptr, const char *name) {
  if (*c != '_') {
    if (strcmp(c,"NULL") == 0) {
      *ptr = (void *) 0;
      return name;
    } else {
      return 0;
    }
  }
  return SWIG_UnpackData(++c,ptr,sizeof(void *));
}

SWIGRUNTIME char *
SWIG_PackDataName(char *buff, void *ptr, size_t sz, const char *name, size_t bsz) {
  char *r = buff;
  size_t lname = (name ? strlen(name) : 0);
  if ((2*sz + 2 + lname) > bsz) return 0;
  *(r++) = '_';
  r = SWIG_PackData(r,ptr,sz);
  if (lname) {
    strncpy(r,name,lname+1);
  } else {
    *r = 0;
  }
  return buff;
}

SWIGRUNTIME const char *
SWIG_UnpackDataName(const char *c, void *ptr, size_t sz, const char *name) {
  if (*c != '_') {
    if (strcmp(c,"NULL") == 0) {
      memset(ptr,0,sz);
      return name;
    } else {
      return 0;
    }
  }
  return SWIG_UnpackData(++c,ptr,sz);
}

#ifdef __cplusplus
}
#endif

/* SWIG Errors applicable to all language modules, values are reserved from -1 to -99 */
#define  SWIG_UnknownError    	   -1
#define  SWIG_IOError        	   -2
#define  SWIG_RuntimeError   	   -3
#define  SWIG_IndexError     	   -4
#define  SWIG_TypeError      	   -5
#define  SWIG_DivisionByZero 	   -6
#define  SWIG_OverflowError  	   -7
#define  SWIG_SyntaxError    	   -8
#define  SWIG_ValueError     	   -9
#define  SWIG_SystemError    	   -10
#define  SWIG_AttributeError 	   -11
#define  SWIG_MemoryError    	   -12
#define  SWIG_NullReferenceError   -13


/* ---------------------------------------------------------------------------
 * Error handling
 *
 * ---------------------------------------------------------------------------*/

/*
 * We support several forms:
 *
 * SWIG_Raise("Error message")
 * which creates an Error object with the error message
 *
 * SWIG_Raise(SWIG_TypeError, "Type error")
 * which creates the specified error type with the message
 *
 * SWIG_Raise(obj)
 * which throws the object itself
 *
 * SWIG_Raise(obj, "Exception const &", SWIGType_p_Exception)
 * which also throws the object itself and discards the unneeded extra type info
 *
 * These must be functions instead of macros to use the C++ overloading to
 * resolve the arguments
 */
#define SWIG_exception(code, msg)               SWIG_Error(code, msg)
#define SWIG_fail                               goto fail

#ifdef NAPI_CPP_EXCEPTIONS

#define SWIG_Error(code, msg)                   SWIG_NAPI_Raise(env, code, msg)
#define NAPI_CHECK_MAYBE(maybe)                 (maybe)
#define NAPI_CHECK_RESULT(maybe, result)        (result = maybe)

SWIGINTERN void SWIG_NAPI_Raise(Napi::Env env, const char *msg) {
  throw Napi::Error::New(env, msg);
}

SWIGINTERN void SWIG_NAPI_Raise(Napi::Env env, int type, const char *msg) {
  switch(type) {
    default:
    case SWIG_IOError:
    case SWIG_MemoryError:
    case SWIG_SystemError:
    case SWIG_RuntimeError:
    case SWIG_DivisionByZero:
    case SWIG_SyntaxError:
      throw Napi::Error::New(env, msg);
    case SWIG_OverflowError:
    case SWIG_IndexError:
      throw Napi::RangeError::New(env, msg);
    case SWIG_ValueError:
    case SWIG_TypeError:
      throw Napi::TypeError::New(env, msg);
  }
}

SWIGINTERN void SWIG_NAPI_Raise(Napi::Env env, Napi::Value obj,
        const char *msg = nullptr, swig_type_info *info = nullptr) {
  throw Napi::Error(env, obj);
}

#else

#define SWIG_Error(code, msg)     do { SWIG_NAPI_Raise(env, code, msg); SWIG_fail; } while (0)
#define NAPI_CHECK_MAYBE(maybe)   do { if (maybe.IsNothing()) SWIG_fail; } while (0)
#define NAPI_CHECK_RESULT(maybe, result)          \
        do {                                      \
                auto r = maybe;                   \
                if (r.IsNothing()) SWIG_fail;     \
                result = r.Unwrap();              \
        } while (0)

SWIGINTERN void SWIG_NAPI_Raise(Napi::Env env, const char *msg) {
  Napi::Error::New(env, msg).ThrowAsJavaScriptException();
}

SWIGINTERN void SWIG_NAPI_Raise(Napi::Env env, int type, const char *msg) {
  switch(type) {
    default:
    case SWIG_IOError:
    case SWIG_MemoryError:
    case SWIG_SystemError:
    case SWIG_RuntimeError:
    case SWIG_DivisionByZero:
    case SWIG_SyntaxError:
      Napi::Error::New(env, msg).ThrowAsJavaScriptException();
      return;
    case SWIG_OverflowError:
    case SWIG_IndexError:
      Napi::RangeError::New(env, msg).ThrowAsJavaScriptException();
      return;
    case SWIG_ValueError:
    case SWIG_TypeError:
      Napi::TypeError::New(env, msg).ThrowAsJavaScriptException();
      return;
  }
}

SWIGINTERN void SWIG_NAPI_Raise(Napi::Env env, Napi::Value obj,
        const char *msg = nullptr, swig_type_info *info = nullptr) {
  Napi::Error(env, obj).ThrowAsJavaScriptException();
}

#endif

void JS_veto_set_variable(const Napi::CallbackInfo &info) {
  SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}

struct EnvInstanceData {
  Napi::Env env;
  // Base class per-environment constructor, used to check
  // if a JS object is a SWIG wrapper
  Napi::FunctionReference *SWIG_NAPI_ObjectWrapCtor;
  // Per-environment wrapper constructors, indexed by the number in
  // swig_type->clientdata
  Napi::FunctionReference **ctor;
  swig_module_info *swig_module;
  EnvInstanceData(Napi::Env, swig_module_info *);
  ~EnvInstanceData();
};

typedef size_t SWIG_NAPI_ClientData;

// Base class for all wrapped objects,
// used mostly when unwrapping unknown objects
template <typename SWIG_OBJ_WRAP>
class SWIG_NAPI_ObjectWrap_templ : public Napi::ObjectWrap<SWIG_OBJ_WRAP> {
  public:
    void *self;
    bool owned;
    size_t size;
    swig_type_info *info;
    SWIG_NAPI_ObjectWrap_templ(const Napi::CallbackInfo &info);
    SWIG_NAPI_ObjectWrap_templ(bool, const Napi::CallbackInfo &info) :
        Napi::ObjectWrap<SWIG_OBJ_WRAP>(info),
        self(nullptr),
        owned(true),
        size(0),
        info(nullptr)
        {}
    virtual ~SWIG_NAPI_ObjectWrap_templ() {};

    Napi::Value ToString(const Napi::CallbackInfo &info);
};

template <typename SWIG_OBJ_WRAP>
SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>::SWIG_NAPI_ObjectWrap_templ(const Napi::CallbackInfo &info) :
        Napi::ObjectWrap<SWIG_OBJ_WRAP>(info), size(0), info(nullptr) { 
  Napi::Env env = info.Env();
  if (info.Length() == 1 && info[0].IsExternal()) {
    // This constructor has been called internally from C++/SWIG
    // to wrap an already existing C++ object of unknown type in JS
    this->self = info[0].As<Napi::External<void>>().Data();
    this->owned = false;
  } else {
    SWIG_Error(SWIG_ERROR, "This constructor is not accessible from JS");
  }
  return;
  goto fail;
fail:
  return;
}

template <typename SWIG_OBJ_WRAP>
Napi::Value SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>::ToString(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  static char repr[128];
  const char *name = SWIG_TypePrettyName(this->info);
  snprintf(repr, sizeof(repr), "{SwigObject %s (%s) at %p %s}",
    this->info ? this->info->name : "unknown",
    name ? name : "unknown",
    this->self,
    this->owned ? "[owned]" : "[copy]");
  return Napi::String::New(env, repr);
}

class SWIG_NAPI_ObjectWrap_inst : public SWIG_NAPI_ObjectWrap_templ<SWIG_NAPI_ObjectWrap_inst> {
public:
  using SWIG_NAPI_ObjectWrap_templ::SWIG_NAPI_ObjectWrap_templ;
  static Napi::Function GetClass(Napi::Env);
  static void GetMembers(
    Napi::Env,
    std::map<std::string, SWIG_NAPI_ObjectWrap_templ::PropertyDescriptor> &,
    std::map<std::string, SWIG_NAPI_ObjectWrap_templ::PropertyDescriptor> &
  );
};

void SWIG_NAPI_ObjectWrap_inst::GetMembers(
        Napi::Env env,
        std::map<std::string, SWIG_NAPI_ObjectWrap_templ::PropertyDescriptor> &members,
        std::map<std::string, SWIG_NAPI_ObjectWrap_templ::PropertyDescriptor> &
) {
  members.erase("toString");
  members.insert({"toString", SWIG_NAPI_ObjectWrap_templ::InstanceMethod("toString", &SWIG_NAPI_ObjectWrap_templ::ToString)});
}

Napi::Function SWIG_NAPI_ObjectWrap_inst::GetClass(Napi::Env env) {
  return Napi::ObjectWrap<SWIG_NAPI_ObjectWrap_inst>::DefineClass(env, "SwigObject", {});
}

SWIGRUNTIME int SWIG_NAPI_ConvertInstancePtr(Napi::Object objRef, void **ptr, swig_type_info *info, int flags) {
  SWIG_NAPI_ObjectWrap_inst *ow;
  Napi::Env env = objRef.Env();
  if(!objRef.IsObject()) return SWIG_ERROR;

  // Check if this is a SWIG wrapper
  Napi::FunctionReference *ctor = env.GetInstanceData<EnvInstanceData>()->SWIG_NAPI_ObjectWrapCtor;
  bool instanceOf;
  NAPI_CHECK_RESULT(objRef.InstanceOf(ctor->Value()), instanceOf);
  if (!instanceOf) {
    return SWIG_TypeError;
  }

  ow = Napi::ObjectWrap<SWIG_NAPI_ObjectWrap_inst>::Unwrap(objRef);

  // Now check if the SWIG type is compatible unless the types match exactly or the type is unknown
  if(info && ow->info != info && ow->info != nullptr) {
    swig_cast_info *tc = SWIG_TypeCheckStruct(ow->info, info);
    if (!tc && ow->info->name) {
      tc = SWIG_TypeCheck(ow->info->name, info);
    }
    bool type_valid = tc != 0;
    if(!type_valid) {
      return SWIG_TypeError;
    }
    int newmemory = 0;
    *ptr = SWIG_TypeCast(tc, ow->self, &newmemory);
    assert(!newmemory); /* newmemory handling not yet implemented */
  } else {
    *ptr = ow->self;
  }

  if (((flags & SWIG_POINTER_RELEASE) == SWIG_POINTER_RELEASE) && !ow->owned) {
    return SWIG_ERROR_RELEASE_NOT_OWNED;
  } else {
    if (flags & SWIG_POINTER_DISOWN) {
      ow->owned = false;
    }
    if (flags & SWIG_POINTER_CLEAR) {
      ow->self = nullptr;
    }
  }
  return SWIG_OK;
  goto fail;
fail:
  return SWIG_ERROR;
}


SWIGRUNTIME int SWIG_NAPI_GetInstancePtr(Napi::Value valRef, void **ptr) {
  SWIG_NAPI_ObjectWrap_inst *ow;
  if(!valRef.IsObject()) {
    return SWIG_TypeError;
  }
  Napi::Object objRef;
  NAPI_CHECK_RESULT(valRef.ToObject(), objRef);
  ow = Napi::ObjectWrap<SWIG_NAPI_ObjectWrap_inst>::Unwrap(objRef);

  if(ow->self == nullptr) {
    return SWIG_ERROR;
  }

  *ptr = ow->self;
  return SWIG_OK;
  goto fail;
fail:
  return SWIG_ERROR;
}


SWIGRUNTIME int SWIG_NAPI_ConvertPtr(Napi::Value valRef, void **ptr, swig_type_info *info, int flags) {
  // special case: JavaScript null => C NULL pointer
  if (valRef.IsNull()) {
    *ptr=0;
    return (flags & SWIG_POINTER_NO_NULL) ? SWIG_NullReferenceError : SWIG_OK;
  }

  if (!valRef.IsObject()) {
    return SWIG_TypeError;
  }

  Napi::Object objRef;
  NAPI_CHECK_RESULT(valRef.ToObject(), objRef);
  return SWIG_NAPI_ConvertInstancePtr(objRef, ptr, info, flags);
  goto fail;
fail:
  return SWIG_ERROR;
}

SWIGRUNTIME Napi::Value SWIG_NAPI_NewPointerObj(Napi::Env env, void *ptr, swig_type_info *info, int flags) {
  Napi::External<void> native;
  Napi::FunctionReference *ctor;

  if (ptr == nullptr) {
    return env.Null();
  }
  native = Napi::External<void>::New(env, ptr);

  size_t *idx = info != nullptr ?
        reinterpret_cast<SWIG_NAPI_ClientData *>(info->clientdata) :
        nullptr;
  if (idx == nullptr) {
    // This type does not have a dedicated wrapper
    ctor = env.GetInstanceData<EnvInstanceData>()->SWIG_NAPI_ObjectWrapCtor;
  } else {
    ctor = env.GetInstanceData<EnvInstanceData>()->ctor[*idx];
  }

  Napi::Value wrapped;
  NAPI_CHECK_RESULT(ctor->New({native}), wrapped);

  // Preserve the type even if using the generic wrapper
  if (idx == nullptr && info != nullptr) {
    Napi::Object obj;
    NAPI_CHECK_RESULT(wrapped.ToObject(), obj);
    Napi::ObjectWrap<SWIG_NAPI_ObjectWrap_inst>::Unwrap(obj)->info = info;
  }

  if ((flags & SWIG_POINTER_OWN) == SWIG_POINTER_OWN) {
    Napi::Object obj;
    NAPI_CHECK_RESULT(wrapped.ToObject(), obj);
    Napi::ObjectWrap<SWIG_NAPI_ObjectWrap_inst>::Unwrap(obj)->owned = true;
  }

  return wrapped;
  goto fail;
fail:
  return Napi::Value();
}

#define SWIG_ConvertPtr(obj, ptr, info, flags)          SWIG_NAPI_ConvertPtr(obj, ptr, info, flags)
#define SWIG_NewPointerObj(ptr, info, flags)            SWIG_NAPI_NewPointerObj(env, ptr, info, flags)

#define SWIG_ConvertInstance(obj, pptr, type, flags)    SWIG_NAPI_ConvertInstancePtr(obj, pptr, type, flags)
#define SWIG_NewInstanceObj(thisvalue, type, flags)     SWIG_NAPI_NewPointerObj(env, thisvalue, type, flags)

#define SWIG_ConvertFunctionPtr(obj, pptr, type)        SWIG_NAPI_ConvertPtr(obj, pptr, type, 0)
#define SWIG_NewFunctionPtrObj(ptr, type)               SWIG_NAPI_NewPointerObj(env, ptr, type, 0)

#define SWIG_GetInstancePtr(obj, ptr)                   SWIG_NAPI_GetInstancePtr(obj, ptr)

SWIGRUNTIME Napi::Value _SWIG_NAPI_wrap_equals(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  void *arg1 = (void *) 0 ;
  void *arg2 = (void *) 0 ;
  bool result;
  int res1;
  int res2;

  if(info.Length() != 1) SWIG_Error(SWIG_ERROR, "Illegal number of arguments for equals.");

  res1 = SWIG_GetInstancePtr(info.This(), &arg1);
  if (!SWIG_IsOK(res1)) {
    SWIG_Error(SWIG_ERROR, "Could not get pointer from 'this' object for equals.");
  }
  res2 = SWIG_GetInstancePtr(info[0], &arg2);
  if (!SWIG_IsOK(res2)) {
    SWIG_Error(SWIG_ArgError(res2), " in method '" "equals" "', argument " "1"" of type '" "void *""'");
  }

  result = (bool)(arg1 == arg2);
  jsresult = Napi::Boolean::New(env, result);

  return jsresult;
  goto fail;
fail:
  return Napi::Value();
}

SWIGRUNTIME Napi::Value _wrap_getCPtr(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  void *arg1 = (void *) 0 ;
  long result;
  int res1;

  res1 = SWIG_GetInstancePtr(info.This(), &arg1);
  if (!SWIG_IsOK(res1)) {
    SWIG_Error(SWIG_ArgError(res1), " in method '" "getCPtr" "', argument " "1"" of type '" "void *""'");
  }

  result = (long)arg1;
  jsresult = Napi::Number::New(env, result);

  return jsresult;
  goto fail;
fail:
  return Napi::Value();
}


/* ---------------------------------------------------------------------------
 * PackedData object
 * (objects visible to JS that do not have a dedicated wrapper but must preserve type)
 * ---------------------------------------------------------------------------*/

SWIGRUNTIME
Napi::Value SWIG_NAPI_NewPackedObj(Napi::Env env, void *data, size_t size, swig_type_info *type) {
  void *data_copy = new uint8_t[size];
  memcpy(data_copy, data, size);
  Napi::Value val = SWIG_NAPI_NewPointerObj(env, data_copy, type, SWIG_POINTER_OWN);
  Napi::Object obj;
  if (val.IsEmpty()) goto fail;

  NAPI_CHECK_RESULT(val.ToObject(), obj);
  Napi::ObjectWrap<SWIG_NAPI_ObjectWrap_inst>::Unwrap(obj)->size = size;

fail:
  return val;
}

SWIGRUNTIME
int SWIG_NAPI_ConvertPacked(Napi::Value valRef, void *ptr, size_t size, swig_type_info *type) {
  void *tmp;
  if (!SWIG_IsOK(SWIG_NAPI_ConvertPtr(valRef, &tmp, type, 0))) {
    return SWIG_ERROR;
  }
  memcpy(ptr, tmp, size);
  return SWIG_OK;
}

#define SWIG_ConvertMember(obj, ptr, sz, ty)            SWIG_NAPI_ConvertPacked(obj, ptr, sz, ty)
#define SWIG_NewMemberObj(ptr, sz, type)                SWIG_NAPI_NewPackedObj(env, ptr, sz, type)


/* ---------------------------------------------------------------------------
 * Support for IN/OUTPUT typemaps (see Lib/typemaps/inoutlist.swg)
 *
 * ---------------------------------------------------------------------------*/

SWIGRUNTIME

Napi::Value SWIG_NAPI_AppendOutput(Napi::Env env, Napi::Value result, Napi::Value obj) {
  if (result.IsUndefined()) {
    result = Napi::Array::New(env);
  } else if (!result.IsArray()) {
    Napi::Array tmparr = Napi::Array::New(env);
    tmparr.Set(static_cast<uint32_t>(0), result);
    result = tmparr;
  }

  Napi::Array arr = result.As<Napi::Array>();
  arr.Set(arr.Length(), obj);
  return arr;
}


/* -------- TYPES TABLE (BEGIN) -------- */

#define SWIGTYPE_p_CharArray swig_types[0]
#define SWIGTYPE_p_CharPointer swig_types[1]
#define SWIGTYPE_p_IntPointer swig_types[2]
#define SWIGTYPE_p_LinMessageInfo swig_types[3]
#define SWIGTYPE_p_LinStatus swig_types[4]
#define SWIGTYPE_p_LongArray swig_types[5]
#define SWIGTYPE_p___int64 swig_types[6]
#define SWIGTYPE_p_char swig_types[7]
#define SWIGTYPE_p_float swig_types[8]
#define SWIGTYPE_p_int swig_types[9]
#define SWIGTYPE_p_long swig_types[10]
#define SWIGTYPE_p_long_long swig_types[11]
#define SWIGTYPE_p_p_LongArray swig_types[12]
#define SWIGTYPE_p_p_char swig_types[13]
#define SWIGTYPE_p_p_unsigned_long swig_types[14]
#define SWIGTYPE_p_short swig_types[15]
#define SWIGTYPE_p_signed___int64 swig_types[16]
#define SWIGTYPE_p_signed_char swig_types[17]
#define SWIGTYPE_p_unsigned___int64 swig_types[18]
#define SWIGTYPE_p_unsigned_char swig_types[19]
#define SWIGTYPE_p_unsigned_int swig_types[20]
#define SWIGTYPE_p_unsigned_long swig_types[21]
#define SWIGTYPE_p_unsigned_long_long swig_types[22]
#define SWIGTYPE_p_unsigned_short swig_types[23]
static swig_type_info *swig_types[25];
static swig_module_info swig_module = {swig_types, 24, 0, 0, 0, 0};
#define SWIG_TypeQuery(name) SWIG_TypeQueryModule(&swig_module, &swig_module, name)
#define SWIG_MangledTypeQuery(name) SWIG_MangledTypeQueryModule(&swig_module, &swig_module, name)

/* -------- TYPES TABLE (END) -------- */



#ifdef __cplusplus
#include <utility>
/* SwigValueWrapper is described in swig.swg */
template<typename T> class SwigValueWrapper {
  struct SwigSmartPointer {
    T *ptr;
    SwigSmartPointer(T *p) : ptr(p) { }
    ~SwigSmartPointer() { delete ptr; }
    SwigSmartPointer& operator=(SwigSmartPointer& rhs) { T* oldptr = ptr; ptr = 0; delete oldptr; ptr = rhs.ptr; rhs.ptr = 0; return *this; }
    void reset(T *p) { T* oldptr = ptr; ptr = 0; delete oldptr; ptr = p; }
  } pointer;
  SwigValueWrapper& operator=(const SwigValueWrapper<T>& rhs);
  SwigValueWrapper(const SwigValueWrapper<T>& rhs);
public:
  SwigValueWrapper() : pointer(0) { }
  SwigValueWrapper& operator=(const T& t) { SwigSmartPointer tmp(new T(t)); pointer = tmp; return *this; }
#if __cplusplus >=201103L
  SwigValueWrapper& operator=(T&& t) { SwigSmartPointer tmp(new T(std::move(t))); pointer = tmp; return *this; }
  operator T&&() const { return std::move(*pointer.ptr); }
#else
  operator T&() const { return *pointer.ptr; }
#endif
  T *operator&() const { return pointer.ptr; }
  static void reset(SwigValueWrapper& t, T *p) { t.pointer.reset(p); }
};

/*
 * SwigValueInit() is a generic initialisation solution as the following approach:
 * 
 *       T c_result = T();
 * 
 * doesn't compile for all types for example:
 * 
 *       unsigned int c_result = unsigned int();
 */
template <typename T> T SwigValueInit() {
  return T();
}

#if __cplusplus >=201103L
# define SWIG_STD_MOVE(OBJ) std::move(OBJ)
#else
# define SWIG_STD_MOVE(OBJ) OBJ
#endif

#endif


#define SWIG_as_voidptr(a) const_cast< void * >(static_cast< const void * >(a)) 
#define SWIG_as_voidptrptr(a) ((void)SWIG_as_voidptr(*a),reinterpret_cast< void** >(a)) 


#include <stdexcept>


#include <assert.h>


#include <windows.h>
#include <stdlib.h>
#include "linlib.h"


#include <stdint.h>		// Use the C99 official header


typedef unsigned char CharPointer;

SWIGINTERN CharPointer *new_CharPointer(){
  return new unsigned char();
}

#include <limits.h>
#if !defined(SWIG_NO_LLONG_MAX)
# if !defined(LLONG_MAX) && defined(__GNUC__) && defined (__LONG_LONG_MAX__)
#   define LLONG_MAX __LONG_LONG_MAX__
#   define LLONG_MIN (-LLONG_MAX - 1LL)
#   define ULLONG_MAX (LLONG_MAX * 2ULL + 1ULL)
# endif
#endif


SWIGINTERN
int SWIG_AsVal_double (Napi::Value obj, double *val)
{
  if(!obj.IsNumber()) {
    return SWIG_TypeError;
  }

  if(val) {
    Napi::Number num;
    NAPI_CHECK_RESULT(obj.ToNumber(), num);
    *val = static_cast<double>(num.DoubleValue());
  }

  return SWIG_OK;
  goto fail;
fail:
  return SWIG_ERROR;
}


#include <float.h>


#include <math.h>


SWIGINTERNINLINE int
SWIG_CanCastAsInteger(double *d, double min, double max) {
  double x = *d;
  if ((min <= x && x <= max)) {
   double fx, cx, rd;
   errno = 0;
   fx = floor(x);
   cx = ceil(x);
   rd =  ((x - fx) < 0.5) ? fx : cx; /* simple rint */
   if ((errno == EDOM) || (errno == ERANGE)) {
     errno = 0;
   } else {
     double summ, reps, diff;
     if (rd < x) {
       diff = x - rd;
     } else if (rd > x) {
       diff = rd - x;
     } else {
       return 1;
     }
     summ = rd + x;
     reps = diff/summ;
     if (reps < 8*DBL_EPSILON) {
       *d = rd;
       return 1;
     }
   }
  }
  return 0;
}


SWIGINTERN
int SWIG_AsVal_unsigned_SS_long (Napi::Value obj, unsigned long *val)
{
  if(!obj.IsNumber()) {
    return SWIG_TypeError;
  }
  if (val) {
    Napi::Number num;
    NAPI_CHECK_RESULT(obj.ToNumber(), num);
    if (num.Int64Value() < 0) {
      return SWIG_TypeError;
    }
    *val = static_cast<unsigned long>(num.Int64Value());
  }
  return SWIG_OK;
  goto fail;
fail:
  return SWIG_ERROR;
}


SWIGINTERN int
SWIG_AsVal_unsigned_SS_char (Napi::Value obj, unsigned char *val)
{
  unsigned long v;
  int res = SWIG_AsVal_unsigned_SS_long (obj, &v);
  if (SWIG_IsOK(res)) {
    if ((v > UCHAR_MAX)) {
      return SWIG_OverflowError;
    } else {
      if (val) *val = static_cast< unsigned char >(v);
    }
  }  
  return res;
}

SWIGINTERN void CharPointer_assign(CharPointer *self,unsigned char value){
  *self = value;
}
SWIGINTERN unsigned char CharPointer_value(CharPointer *self){
  return *self;
}

SWIGINTERNINLINE Napi::Value
SWIG_From_unsigned_SS_char(Napi::Env env, unsigned char c)
{
  return Napi::Number::New(env, static_cast<double>(c));
}

SWIGINTERN unsigned char *CharPointer_cast(CharPointer *self){
  return self;
}
SWIGINTERN CharPointer *CharPointer_frompointer(unsigned char *t){
  return (CharPointer *) t;
}

typedef int IntPointer;

SWIGINTERN IntPointer *new_IntPointer(){
  return new int();
}

SWIGINTERN
int SWIG_AsVal_int (Napi::Value valRef, int* val)
{
  if (!valRef.IsNumber()) {
    return SWIG_TypeError;
  }
  if (val) {
    Napi::Number num;
    NAPI_CHECK_RESULT(valRef.ToNumber(), num);
    *val = static_cast<int>(num.Int32Value());
  }

  return SWIG_OK;
  goto fail;
fail:
  return SWIG_ERROR;
}

SWIGINTERN void IntPointer_assign(IntPointer *self,int value){
  *self = value;
}
SWIGINTERN int IntPointer_value(IntPointer *self){
  return *self;
}

SWIGINTERN
Napi::Value SWIG_From_int(Napi::Env env, int val)
{
  return Napi::Number::New(env, val);
}

SWIGINTERN int *IntPointer_cast(IntPointer *self){
  return self;
}
SWIGINTERN IntPointer *IntPointer_frompointer(int *t){
  return (IntPointer *) t;
}

typedef unsigned long LongArray;


#if defined(LLONG_MAX) && !defined(SWIG_LONG_LONG_AVAILABLE)
#  define SWIG_LONG_LONG_AVAILABLE
#endif


#ifdef SWIG_LONG_LONG_AVAILABLE
SWIGINTERN
int SWIG_AsVal_unsigned_SS_long_SS_long (Napi::Value obj, unsigned long long *val)
{
  if(!obj.IsNumber()) {
    return SWIG_TypeError;
  }
  if (obj.ToNumber().Int64Value() < 0) {
    return SWIG_TypeError;
  }
  if (val) {
    Napi::Number num;
    NAPI_CHECK_RESULT(obj.ToNumber(), num);
    *val = static_cast<unsigned long long>(num.Int64Value());
  }
  return SWIG_OK;
  goto fail;
fail:
  return SWIG_ERROR;
}
#endif


SWIGINTERNINLINE int
SWIG_AsVal_size_t (Napi::Value obj, size_t *val)
{
  int res = SWIG_TypeError;
#ifdef SWIG_LONG_LONG_AVAILABLE
  if (sizeof(size_t) <= sizeof(unsigned long)) {
#endif
    unsigned long v;
    res = SWIG_AsVal_unsigned_SS_long (obj, val ? &v : 0);
    if (SWIG_IsOK(res) && val) *val = static_cast< size_t >(v);
#ifdef SWIG_LONG_LONG_AVAILABLE
  } else if (sizeof(size_t) <= sizeof(unsigned long long)) {
    unsigned long long v;
    res = SWIG_AsVal_unsigned_SS_long_SS_long (obj, val ? &v : 0);
    if (SWIG_IsOK(res) && val) *val = static_cast< size_t >(v);
  }
#endif
  return res;
}

SWIGINTERN LongArray *new_LongArray(size_t nelements){
  return new unsigned long[nelements]();
}
SWIGINTERN unsigned long LongArray_getitem(LongArray *self,size_t index){
  return self[index];
}

SWIGINTERN
Napi::Value SWIG_From_unsigned_SS_long(Napi::Env env, unsigned long val)
{
  return Napi::Number::New(env, val);
}

SWIGINTERN void LongArray_setitem(LongArray *self,size_t index,unsigned long value){
  self[index] = value;
}
SWIGINTERN unsigned long *LongArray_cast(LongArray *self){
  return self;
}
SWIGINTERN LongArray *LongArray_frompointer(unsigned long *t){
  return (LongArray *) t;
}

typedef unsigned char CharArray;

SWIGINTERN CharArray *new_CharArray(size_t nelements){
  return new unsigned char[nelements]();
}
SWIGINTERN unsigned char CharArray_getitem(CharArray *self,size_t index){
  return self[index];
}
SWIGINTERN void CharArray_setitem(CharArray *self,size_t index,unsigned char value){
  self[index] = value;
}
SWIGINTERN unsigned char *CharArray_cast(CharArray *self){
  return self;
}
SWIGINTERN CharArray *CharArray_frompointer(unsigned char *t){
  return (CharArray *) t;
}

SWIGINTERN
int SWIG_AsVal_unsigned_SS_short (Napi::Value valRef, unsigned short* val)
{
  if (!valRef.IsNumber()) {
    return SWIG_TypeError;
  }
  if (val) {
    Napi::Number num;
    NAPI_CHECK_RESULT(valRef.ToNumber(), num);
    if (num.Int64Value() < 0) {
      return SWIG_TypeError;
    }
    *val = static_cast<unsigned short>(num.Uint32Value());
  }

  return SWIG_OK;
  goto fail;
fail:
  return SWIG_ERROR;
}


SWIGINTERN
Napi::Value SWIG_From_unsigned_SS_short(Napi::Env env, unsigned short val)
{
  return Napi::Number::New(env, val);
}


SWIGINTERN
int SWIG_AsVal_unsigned_SS_int (Napi::Value valRef, unsigned int* val)
{
  if (!valRef.IsNumber()) {
    return SWIG_TypeError;
  }
  if (val) {
    Napi::Number num;
    NAPI_CHECK_RESULT(valRef.ToNumber(), num);
    if (num.Int64Value() < 0) {
      return SWIG_TypeError;
    }
    *val = static_cast<unsigned int>(num.Uint32Value());
  }

  return SWIG_OK;
  goto fail;
fail:
  return SWIG_ERROR;
}


void LoadDll(const char* path) {
  SetDllDirectory(path);
}


SWIGINTERN swig_type_info*
SWIG_pchar_descriptor(void)
{
  static int init = 0;
  static swig_type_info* info = 0;
  if (!init) {
    info = SWIG_TypeQuery("_p_char");
    init = 1;
  }
  return info;
}


SWIGINTERN int
SWIG_AsCharPtrAndSize(Napi::Value valRef, char** cptr, size_t* psize, int *alloc)
{
  if(valRef.IsString()) {
    Napi::String js_str;
    NAPI_CHECK_RESULT(valRef.ToString(), js_str);

    std::string str = js_str.Utf8Value();
    size_t len = str.size() + 1;
    char* cstr = (char*) (new char[len]());
    memcpy(cstr, str.data(), len);
    
    if(alloc) *alloc = SWIG_NEWOBJ;
    if(psize) *psize = len;
    if(cptr) *cptr = cstr;
    
    return SWIG_OK;
  } else {
    if(valRef.IsObject()) {
      swig_type_info* pchar_descriptor = SWIG_pchar_descriptor();
      Napi::Object obj;
      NAPI_CHECK_RESULT(valRef.ToObject(), obj);
      // try if the object is a wrapped char[]
      if (pchar_descriptor) {
        void* vptr = 0;
        if (SWIG_ConvertPtr(obj, &vptr, pchar_descriptor, 0) == SWIG_OK) {
          if (cptr) *cptr = (char *) vptr;
          if (psize) *psize = vptr ? (strlen((char *)vptr) + 1) : 0;
          if (alloc) *alloc = SWIG_OLDOBJ;
          return SWIG_OK;
        }
      }
    }
  }
  goto fail;
fail:
  return SWIG_TypeError;
}





#define SWIG_NAPI_INIT xmlpp_initialize


// jsnapi_class_prologue_template
template <typename SWIG_OBJ_WRAP>
class _exports_CharPointer_templ : public SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP> {
public:
  _exports_CharPointer_templ(const Napi::CallbackInfo &);
_exports_CharPointer_templ(bool, const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_new_CharPointer(const Napi::CallbackInfo &);
virtual ~_exports_CharPointer_templ();
// jsnapi_class_method_declaration
Napi::Value _wrap_CharPointer_assign(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_CharPointer_value(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_CharPointer_cast(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
static Napi::Value _wrap_CharPointer_frompointer(const Napi::CallbackInfo &);
// jsnapi_class_epilogue_template
static void JS_veto_set_static_variable(const Napi::CallbackInfo &, const Napi::Value &);
void JS_veto_set_variable(const Napi::CallbackInfo &, const Napi::Value &);
};

template <typename SWIG_OBJ_WRAP>
void _exports_CharPointer_templ<SWIG_OBJ_WRAP>::JS_veto_set_static_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}

template <typename SWIG_OBJ_WRAP>
void _exports_CharPointer_templ<SWIG_OBJ_WRAP>::JS_veto_set_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}
// jsnapi_class_instance
class _exports_CharPointer_inst : public _exports_CharPointer_templ<_exports_CharPointer_inst> {
public:
  using _exports_CharPointer_templ::_exports_CharPointer_templ;
  virtual ~_exports_CharPointer_inst() {
    
  };
  static void GetMembers(
    Napi::Env,
    std::map<std::string, _exports_CharPointer_templ::PropertyDescriptor> &,
    std::map<std::string, _exports_CharPointer_templ::PropertyDescriptor> &
    );
  static Napi::Function GetClass(Napi::Env);
};
/* Class: CharPointer (_exports_CharPointer) */
// jsnapi_getclass
Napi::Function _exports_CharPointer_inst::GetClass(Napi::Env env) {
  std::map<std::string, _exports_CharPointer_templ::PropertyDescriptor> members, staticMembers;
  GetMembers(env, members, staticMembers);
  
  std::vector<_exports_CharPointer_inst::PropertyDescriptor> symbolTable;
  for (auto it = members.begin(); it != members.end(); it++)
  symbolTable.push_back(it->second);
  for (auto it = staticMembers.begin(); it != staticMembers.end(); it++)
  symbolTable.push_back(it->second);
  
  return Napi::ObjectWrap<_exports_CharPointer_inst>::DefineClass(env, "CharPointer", symbolTable);
}

void _exports_CharPointer_inst::GetMembers(
  Napi::Env env,
  std::map<std::string, _exports_CharPointer_templ::PropertyDescriptor> &members,
  std::map<std::string, _exports_CharPointer_templ::PropertyDescriptor> &staticMembers
  ) {
  std::map<std::string, SWIG_NAPI_ObjectWrap_templ<SWIG_NAPI_ObjectWrap_inst>::PropertyDescriptor> baseMembers, baseStaticMembers;
  SWIG_NAPI_ObjectWrap_inst::GetMembers(env, baseMembers, baseStaticMembers);
  members.insert(baseMembers.begin(), baseMembers.end());
  staticMembers.insert(staticMembers.begin(), staticMembers.end());
  
  /* register wrapper functions */
  // jsnapi_member_function_descriptor
  members.erase("assign");
  members.insert({
    "assign",
      _exports_CharPointer_templ::InstanceMethod("assign",
        &_exports_CharPointer_templ::_wrap_CharPointer_assign,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  // jsnapi_member_function_descriptor
  members.erase("value");
  members.insert({
    "value",
      _exports_CharPointer_templ::InstanceMethod("value",
        &_exports_CharPointer_templ::_wrap_CharPointer_value,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  // jsnapi_member_function_descriptor
  members.erase("cast");
  members.insert({
    "cast",
      _exports_CharPointer_templ::InstanceMethod("cast",
        &_exports_CharPointer_templ::_wrap_CharPointer_cast,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  
  /* add static class functions and variables */
  // jsnapi_register_static_function
  staticMembers.erase("frompointer");
  staticMembers.insert({
    "frompointer",
      StaticMethod("frompointer",
        &_exports_CharPointer_templ::_wrap_CharPointer_frompointer,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  
}
// jsnapi_class_prologue_template
template <typename SWIG_OBJ_WRAP>
class _exports_IntPointer_templ : public SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP> {
public:
  _exports_IntPointer_templ(const Napi::CallbackInfo &);
_exports_IntPointer_templ(bool, const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_new_IntPointer(const Napi::CallbackInfo &);
virtual ~_exports_IntPointer_templ();
// jsnapi_class_method_declaration
Napi::Value _wrap_IntPointer_assign(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_IntPointer_value(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_IntPointer_cast(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
static Napi::Value _wrap_IntPointer_frompointer(const Napi::CallbackInfo &);
// jsnapi_class_epilogue_template
static void JS_veto_set_static_variable(const Napi::CallbackInfo &, const Napi::Value &);
void JS_veto_set_variable(const Napi::CallbackInfo &, const Napi::Value &);
};

template <typename SWIG_OBJ_WRAP>
void _exports_IntPointer_templ<SWIG_OBJ_WRAP>::JS_veto_set_static_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}

template <typename SWIG_OBJ_WRAP>
void _exports_IntPointer_templ<SWIG_OBJ_WRAP>::JS_veto_set_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}
// jsnapi_class_instance
class _exports_IntPointer_inst : public _exports_IntPointer_templ<_exports_IntPointer_inst> {
public:
  using _exports_IntPointer_templ::_exports_IntPointer_templ;
  virtual ~_exports_IntPointer_inst() {
    
  };
  static void GetMembers(
    Napi::Env,
    std::map<std::string, _exports_IntPointer_templ::PropertyDescriptor> &,
    std::map<std::string, _exports_IntPointer_templ::PropertyDescriptor> &
    );
  static Napi::Function GetClass(Napi::Env);
};
/* Class: IntPointer (_exports_IntPointer) */
// jsnapi_getclass
Napi::Function _exports_IntPointer_inst::GetClass(Napi::Env env) {
  std::map<std::string, _exports_IntPointer_templ::PropertyDescriptor> members, staticMembers;
  GetMembers(env, members, staticMembers);
  
  std::vector<_exports_IntPointer_inst::PropertyDescriptor> symbolTable;
  for (auto it = members.begin(); it != members.end(); it++)
  symbolTable.push_back(it->second);
  for (auto it = staticMembers.begin(); it != staticMembers.end(); it++)
  symbolTable.push_back(it->second);
  
  return Napi::ObjectWrap<_exports_IntPointer_inst>::DefineClass(env, "IntPointer", symbolTable);
}

void _exports_IntPointer_inst::GetMembers(
  Napi::Env env,
  std::map<std::string, _exports_IntPointer_templ::PropertyDescriptor> &members,
  std::map<std::string, _exports_IntPointer_templ::PropertyDescriptor> &staticMembers
  ) {
  std::map<std::string, SWIG_NAPI_ObjectWrap_templ<SWIG_NAPI_ObjectWrap_inst>::PropertyDescriptor> baseMembers, baseStaticMembers;
  SWIG_NAPI_ObjectWrap_inst::GetMembers(env, baseMembers, baseStaticMembers);
  members.insert(baseMembers.begin(), baseMembers.end());
  staticMembers.insert(staticMembers.begin(), staticMembers.end());
  
  /* register wrapper functions */
  // jsnapi_member_function_descriptor
  members.erase("assign");
  members.insert({
    "assign",
      _exports_IntPointer_templ::InstanceMethod("assign",
        &_exports_IntPointer_templ::_wrap_IntPointer_assign,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  // jsnapi_member_function_descriptor
  members.erase("value");
  members.insert({
    "value",
      _exports_IntPointer_templ::InstanceMethod("value",
        &_exports_IntPointer_templ::_wrap_IntPointer_value,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  // jsnapi_member_function_descriptor
  members.erase("cast");
  members.insert({
    "cast",
      _exports_IntPointer_templ::InstanceMethod("cast",
        &_exports_IntPointer_templ::_wrap_IntPointer_cast,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  
  /* add static class functions and variables */
  // jsnapi_register_static_function
  staticMembers.erase("frompointer");
  staticMembers.insert({
    "frompointer",
      StaticMethod("frompointer",
        &_exports_IntPointer_templ::_wrap_IntPointer_frompointer,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  
}
// jsnapi_class_prologue_template
template <typename SWIG_OBJ_WRAP>
class _exports_LongArray_templ : public SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP> {
public:
  _exports_LongArray_templ(const Napi::CallbackInfo &);
_exports_LongArray_templ(bool, const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_new_LongArray(const Napi::CallbackInfo &);
virtual ~_exports_LongArray_templ();
// jsnapi_class_method_declaration
Napi::Value _wrap_LongArray_getitem(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_LongArray_setitem(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_LongArray_cast(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
static Napi::Value _wrap_LongArray_frompointer(const Napi::CallbackInfo &);
// jsnapi_class_epilogue_template
static void JS_veto_set_static_variable(const Napi::CallbackInfo &, const Napi::Value &);
void JS_veto_set_variable(const Napi::CallbackInfo &, const Napi::Value &);
};

template <typename SWIG_OBJ_WRAP>
void _exports_LongArray_templ<SWIG_OBJ_WRAP>::JS_veto_set_static_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}

template <typename SWIG_OBJ_WRAP>
void _exports_LongArray_templ<SWIG_OBJ_WRAP>::JS_veto_set_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}
// jsnapi_class_instance
class _exports_LongArray_inst : public _exports_LongArray_templ<_exports_LongArray_inst> {
public:
  using _exports_LongArray_templ::_exports_LongArray_templ;
  virtual ~_exports_LongArray_inst() {
    
  };
  static void GetMembers(
    Napi::Env,
    std::map<std::string, _exports_LongArray_templ::PropertyDescriptor> &,
    std::map<std::string, _exports_LongArray_templ::PropertyDescriptor> &
    );
  static Napi::Function GetClass(Napi::Env);
};
/* Class: LongArray (_exports_LongArray) */
// jsnapi_getclass
Napi::Function _exports_LongArray_inst::GetClass(Napi::Env env) {
  std::map<std::string, _exports_LongArray_templ::PropertyDescriptor> members, staticMembers;
  GetMembers(env, members, staticMembers);
  
  std::vector<_exports_LongArray_inst::PropertyDescriptor> symbolTable;
  for (auto it = members.begin(); it != members.end(); it++)
  symbolTable.push_back(it->second);
  for (auto it = staticMembers.begin(); it != staticMembers.end(); it++)
  symbolTable.push_back(it->second);
  
  return Napi::ObjectWrap<_exports_LongArray_inst>::DefineClass(env, "LongArray", symbolTable);
}

void _exports_LongArray_inst::GetMembers(
  Napi::Env env,
  std::map<std::string, _exports_LongArray_templ::PropertyDescriptor> &members,
  std::map<std::string, _exports_LongArray_templ::PropertyDescriptor> &staticMembers
  ) {
  std::map<std::string, SWIG_NAPI_ObjectWrap_templ<SWIG_NAPI_ObjectWrap_inst>::PropertyDescriptor> baseMembers, baseStaticMembers;
  SWIG_NAPI_ObjectWrap_inst::GetMembers(env, baseMembers, baseStaticMembers);
  members.insert(baseMembers.begin(), baseMembers.end());
  staticMembers.insert(staticMembers.begin(), staticMembers.end());
  
  /* register wrapper functions */
  // jsnapi_member_function_descriptor
  members.erase("getitem");
  members.insert({
    "getitem",
      _exports_LongArray_templ::InstanceMethod("getitem",
        &_exports_LongArray_templ::_wrap_LongArray_getitem,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  // jsnapi_member_function_descriptor
  members.erase("setitem");
  members.insert({
    "setitem",
      _exports_LongArray_templ::InstanceMethod("setitem",
        &_exports_LongArray_templ::_wrap_LongArray_setitem,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  // jsnapi_member_function_descriptor
  members.erase("cast");
  members.insert({
    "cast",
      _exports_LongArray_templ::InstanceMethod("cast",
        &_exports_LongArray_templ::_wrap_LongArray_cast,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  
  /* add static class functions and variables */
  // jsnapi_register_static_function
  staticMembers.erase("frompointer");
  staticMembers.insert({
    "frompointer",
      StaticMethod("frompointer",
        &_exports_LongArray_templ::_wrap_LongArray_frompointer,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  
}
// jsnapi_class_prologue_template
template <typename SWIG_OBJ_WRAP>
class _exports_CharArray_templ : public SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP> {
public:
  _exports_CharArray_templ(const Napi::CallbackInfo &);
_exports_CharArray_templ(bool, const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_new_CharArray(const Napi::CallbackInfo &);
virtual ~_exports_CharArray_templ();
// jsnapi_class_method_declaration
Napi::Value _wrap_CharArray_getitem(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_CharArray_setitem(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_CharArray_cast(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
static Napi::Value _wrap_CharArray_frompointer(const Napi::CallbackInfo &);
// jsnapi_class_epilogue_template
static void JS_veto_set_static_variable(const Napi::CallbackInfo &, const Napi::Value &);
void JS_veto_set_variable(const Napi::CallbackInfo &, const Napi::Value &);
};

template <typename SWIG_OBJ_WRAP>
void _exports_CharArray_templ<SWIG_OBJ_WRAP>::JS_veto_set_static_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}

template <typename SWIG_OBJ_WRAP>
void _exports_CharArray_templ<SWIG_OBJ_WRAP>::JS_veto_set_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}
// jsnapi_class_instance
class _exports_CharArray_inst : public _exports_CharArray_templ<_exports_CharArray_inst> {
public:
  using _exports_CharArray_templ::_exports_CharArray_templ;
  virtual ~_exports_CharArray_inst() {
    
  };
  static void GetMembers(
    Napi::Env,
    std::map<std::string, _exports_CharArray_templ::PropertyDescriptor> &,
    std::map<std::string, _exports_CharArray_templ::PropertyDescriptor> &
    );
  static Napi::Function GetClass(Napi::Env);
};
/* Class: CharArray (_exports_CharArray) */
// jsnapi_getclass
Napi::Function _exports_CharArray_inst::GetClass(Napi::Env env) {
  std::map<std::string, _exports_CharArray_templ::PropertyDescriptor> members, staticMembers;
  GetMembers(env, members, staticMembers);
  
  std::vector<_exports_CharArray_inst::PropertyDescriptor> symbolTable;
  for (auto it = members.begin(); it != members.end(); it++)
  symbolTable.push_back(it->second);
  for (auto it = staticMembers.begin(); it != staticMembers.end(); it++)
  symbolTable.push_back(it->second);
  
  return Napi::ObjectWrap<_exports_CharArray_inst>::DefineClass(env, "CharArray", symbolTable);
}

void _exports_CharArray_inst::GetMembers(
  Napi::Env env,
  std::map<std::string, _exports_CharArray_templ::PropertyDescriptor> &members,
  std::map<std::string, _exports_CharArray_templ::PropertyDescriptor> &staticMembers
  ) {
  std::map<std::string, SWIG_NAPI_ObjectWrap_templ<SWIG_NAPI_ObjectWrap_inst>::PropertyDescriptor> baseMembers, baseStaticMembers;
  SWIG_NAPI_ObjectWrap_inst::GetMembers(env, baseMembers, baseStaticMembers);
  members.insert(baseMembers.begin(), baseMembers.end());
  staticMembers.insert(staticMembers.begin(), staticMembers.end());
  
  /* register wrapper functions */
  // jsnapi_member_function_descriptor
  members.erase("getitem");
  members.insert({
    "getitem",
      _exports_CharArray_templ::InstanceMethod("getitem",
        &_exports_CharArray_templ::_wrap_CharArray_getitem,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  // jsnapi_member_function_descriptor
  members.erase("setitem");
  members.insert({
    "setitem",
      _exports_CharArray_templ::InstanceMethod("setitem",
        &_exports_CharArray_templ::_wrap_CharArray_setitem,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  // jsnapi_member_function_descriptor
  members.erase("cast");
  members.insert({
    "cast",
      _exports_CharArray_templ::InstanceMethod("cast",
        &_exports_CharArray_templ::_wrap_CharArray_cast,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  
  /* add static class functions and variables */
  // jsnapi_register_static_function
  staticMembers.erase("frompointer");
  staticMembers.insert({
    "frompointer",
      StaticMethod("frompointer",
        &_exports_CharArray_templ::_wrap_CharArray_frompointer,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  
}
// jsnapi_class_prologue_template
template <typename SWIG_OBJ_WRAP>
class _exports_LinMessageInfo_templ : public SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP> {
public:
  _exports_LinMessageInfo_templ(const Napi::CallbackInfo &);
_exports_LinMessageInfo_templ(bool, const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_LinMessageInfo_timestamp_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_LinMessageInfo_timestamp_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_LinMessageInfo_synchBreakLength_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_LinMessageInfo_synchBreakLength_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_LinMessageInfo_frameLength_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_LinMessageInfo_frameLength_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_LinMessageInfo_bitrate_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_LinMessageInfo_bitrate_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_LinMessageInfo_checkSum_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_LinMessageInfo_checkSum_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_LinMessageInfo_idPar_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_LinMessageInfo_idPar_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_LinMessageInfo_z_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_LinMessageInfo_z_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_LinMessageInfo_synchEdgeTime_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_LinMessageInfo_synchEdgeTime_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_LinMessageInfo_byteTime_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_LinMessageInfo_byteTime_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_new_LinMessageInfo(const Napi::CallbackInfo &);
virtual ~_exports_LinMessageInfo_templ();
// jsnapi_class_epilogue_template
static void JS_veto_set_static_variable(const Napi::CallbackInfo &, const Napi::Value &);
void JS_veto_set_variable(const Napi::CallbackInfo &, const Napi::Value &);
};

template <typename SWIG_OBJ_WRAP>
void _exports_LinMessageInfo_templ<SWIG_OBJ_WRAP>::JS_veto_set_static_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}

template <typename SWIG_OBJ_WRAP>
void _exports_LinMessageInfo_templ<SWIG_OBJ_WRAP>::JS_veto_set_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}
// jsnapi_class_instance
class _exports_LinMessageInfo_inst : public _exports_LinMessageInfo_templ<_exports_LinMessageInfo_inst> {
public:
  using _exports_LinMessageInfo_templ::_exports_LinMessageInfo_templ;
  virtual ~_exports_LinMessageInfo_inst() {
    
  };
  static void GetMembers(
    Napi::Env,
    std::map<std::string, _exports_LinMessageInfo_templ::PropertyDescriptor> &,
    std::map<std::string, _exports_LinMessageInfo_templ::PropertyDescriptor> &
    );
  static Napi::Function GetClass(Napi::Env);
};
/* Class: LinMessageInfo (_exports_LinMessageInfo) */
// jsnapi_getclass
Napi::Function _exports_LinMessageInfo_inst::GetClass(Napi::Env env) {
  std::map<std::string, _exports_LinMessageInfo_templ::PropertyDescriptor> members, staticMembers;
  GetMembers(env, members, staticMembers);
  
  std::vector<_exports_LinMessageInfo_inst::PropertyDescriptor> symbolTable;
  for (auto it = members.begin(); it != members.end(); it++)
  symbolTable.push_back(it->second);
  for (auto it = staticMembers.begin(); it != staticMembers.end(); it++)
  symbolTable.push_back(it->second);
  
  return Napi::ObjectWrap<_exports_LinMessageInfo_inst>::DefineClass(env, "LinMessageInfo", symbolTable);
}

void _exports_LinMessageInfo_inst::GetMembers(
  Napi::Env env,
  std::map<std::string, _exports_LinMessageInfo_templ::PropertyDescriptor> &members,
  std::map<std::string, _exports_LinMessageInfo_templ::PropertyDescriptor> &staticMembers
  ) {
  std::map<std::string, SWIG_NAPI_ObjectWrap_templ<SWIG_NAPI_ObjectWrap_inst>::PropertyDescriptor> baseMembers, baseStaticMembers;
  SWIG_NAPI_ObjectWrap_inst::GetMembers(env, baseMembers, baseStaticMembers);
  members.insert(baseMembers.begin(), baseMembers.end());
  staticMembers.insert(staticMembers.begin(), staticMembers.end());
  
  /* register wrapper functions */
  // jsnapi_register_member_variable
  members.erase("timestamp");
  members.insert({
    "timestamp",
      _exports_LinMessageInfo_templ::InstanceAccessor("timestamp",
        &_exports_LinMessageInfo_templ::_wrap_LinMessageInfo_timestamp_get,
        &_exports_LinMessageInfo_templ::_wrap_LinMessageInfo_timestamp_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("synchBreakLength");
  members.insert({
    "synchBreakLength",
      _exports_LinMessageInfo_templ::InstanceAccessor("synchBreakLength",
        &_exports_LinMessageInfo_templ::_wrap_LinMessageInfo_synchBreakLength_get,
        &_exports_LinMessageInfo_templ::_wrap_LinMessageInfo_synchBreakLength_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("frameLength");
  members.insert({
    "frameLength",
      _exports_LinMessageInfo_templ::InstanceAccessor("frameLength",
        &_exports_LinMessageInfo_templ::_wrap_LinMessageInfo_frameLength_get,
        &_exports_LinMessageInfo_templ::_wrap_LinMessageInfo_frameLength_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("bitrate");
  members.insert({
    "bitrate",
      _exports_LinMessageInfo_templ::InstanceAccessor("bitrate",
        &_exports_LinMessageInfo_templ::_wrap_LinMessageInfo_bitrate_get,
        &_exports_LinMessageInfo_templ::_wrap_LinMessageInfo_bitrate_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("checkSum");
  members.insert({
    "checkSum",
      _exports_LinMessageInfo_templ::InstanceAccessor("checkSum",
        &_exports_LinMessageInfo_templ::_wrap_LinMessageInfo_checkSum_get,
        &_exports_LinMessageInfo_templ::_wrap_LinMessageInfo_checkSum_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("idPar");
  members.insert({
    "idPar",
      _exports_LinMessageInfo_templ::InstanceAccessor("idPar",
        &_exports_LinMessageInfo_templ::_wrap_LinMessageInfo_idPar_get,
        &_exports_LinMessageInfo_templ::_wrap_LinMessageInfo_idPar_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("z");
  members.insert({
    "z",
      _exports_LinMessageInfo_templ::InstanceAccessor("z",
        &_exports_LinMessageInfo_templ::_wrap_LinMessageInfo_z_get,
        &_exports_LinMessageInfo_templ::_wrap_LinMessageInfo_z_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("synchEdgeTime");
  members.insert({
    "synchEdgeTime",
      _exports_LinMessageInfo_templ::InstanceAccessor("synchEdgeTime",
        &_exports_LinMessageInfo_templ::_wrap_LinMessageInfo_synchEdgeTime_get,
        &_exports_LinMessageInfo_templ::_wrap_LinMessageInfo_synchEdgeTime_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("byteTime");
  members.insert({
    "byteTime",
      _exports_LinMessageInfo_templ::InstanceAccessor("byteTime",
        &_exports_LinMessageInfo_templ::_wrap_LinMessageInfo_byteTime_get,
        &_exports_LinMessageInfo_templ::_wrap_LinMessageInfo_byteTime_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  
  /* add static class functions and variables */
  
}





template <typename SWIG_OBJ_WRAP>
// js_ctor
// This is the main constructor
_exports_CharPointer_templ<SWIG_OBJ_WRAP>::_exports_CharPointer_templ(const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  Napi::Env env = info.Env();
  
  this->info = SWIGTYPE_p_CharPointer;
  if (info.Length() == 1 && info[0].IsExternal()) {
    // This constructor has been called internally from C++/SWIG
    // to wrap an already existing C++ object in JS
    this->self = info[0].As<Napi::External<void>>().Data();
    this->owned = false;
    return;
  }
  this->owned = true;
  
  CharPointer *result;
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_new_CharPointer.");
  }
  result = (CharPointer *)new_CharPointer();
  
  
  this->self = result;
  return;
  goto fail;
fail:
  return;
}

// This is the bypass constructor to be used from child classes
template <typename SWIG_OBJ_WRAP>
_exports_CharPointer_templ<SWIG_OBJ_WRAP>::_exports_CharPointer_templ(bool, const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  
}

SWIGINTERN void delete_CharPointer(CharPointer *self){
  delete self;
}

// js_dtoroverride
template <typename SWIG_OBJ_WRAP>
_exports_CharPointer_templ<SWIG_OBJ_WRAP>::~_exports_CharPointer_templ() {
  auto arg1 = reinterpret_cast<CharPointer *>(this->self);
  if (this->owned && arg1) {
    delete_CharPointer(arg1);
    this->self = nullptr;
  }
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_CharPointer_templ<SWIG_OBJ_WRAP>::_wrap_CharPointer_assign(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  CharPointer *arg1 = (CharPointer *) 0 ;
  unsigned char arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_CharPointer_assign.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_CharPointer, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "CharPointer_assign" "', argument " "1"" of type '" "CharPointer *""'"); 
  }
  arg1 = reinterpret_cast< CharPointer * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(info[0], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "CharPointer_assign" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);CharPointer_assign(arg1,arg2);
  jsresult = env.Undefined();
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_CharPointer_templ<SWIG_OBJ_WRAP>::_wrap_CharPointer_value(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  CharPointer *arg1 = (CharPointer *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char result;
  
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_CharPointer_value.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_CharPointer, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "CharPointer_value" "', argument " "1"" of type '" "CharPointer *""'"); 
  }
  arg1 = reinterpret_cast< CharPointer * >(argp1);result = (unsigned char)CharPointer_value(arg1);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_CharPointer_templ<SWIG_OBJ_WRAP>::_wrap_CharPointer_cast(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  CharPointer *arg1 = (CharPointer *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char *result = 0 ;
  
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_CharPointer_cast.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_CharPointer, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "CharPointer_cast" "', argument " "1"" of type '" "CharPointer *""'"); 
  }
  arg1 = reinterpret_cast< CharPointer * >(argp1);result = (unsigned char *)CharPointer_cast(arg1);
  jsresult = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_unsigned_char, 0 |  0 );
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_CharPointer_templ<SWIG_OBJ_WRAP>::_wrap_CharPointer_frompointer(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  unsigned char *arg1 = (unsigned char *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  CharPointer *result = 0 ;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_CharPointer_frompointer.");
  }
  
  res1 = SWIG_ConvertPtr(info[0], &argp1,SWIGTYPE_p_unsigned_char, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "CharPointer_frompointer" "', argument " "1"" of type '" "unsigned char *""'"); 
  }
  arg1 = reinterpret_cast< unsigned char * >(argp1);result = (CharPointer *)CharPointer_frompointer(arg1);
  jsresult = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_CharPointer, 0 |  0 );
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


template <typename SWIG_OBJ_WRAP>
// js_ctor
// This is the main constructor
_exports_IntPointer_templ<SWIG_OBJ_WRAP>::_exports_IntPointer_templ(const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  Napi::Env env = info.Env();
  
  this->info = SWIGTYPE_p_IntPointer;
  if (info.Length() == 1 && info[0].IsExternal()) {
    // This constructor has been called internally from C++/SWIG
    // to wrap an already existing C++ object in JS
    this->self = info[0].As<Napi::External<void>>().Data();
    this->owned = false;
    return;
  }
  this->owned = true;
  
  IntPointer *result;
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_new_IntPointer.");
  }
  result = (IntPointer *)new_IntPointer();
  
  
  this->self = result;
  return;
  goto fail;
fail:
  return;
}

// This is the bypass constructor to be used from child classes
template <typename SWIG_OBJ_WRAP>
_exports_IntPointer_templ<SWIG_OBJ_WRAP>::_exports_IntPointer_templ(bool, const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  
}

SWIGINTERN void delete_IntPointer(IntPointer *self){
  delete self;
}

// js_dtoroverride
template <typename SWIG_OBJ_WRAP>
_exports_IntPointer_templ<SWIG_OBJ_WRAP>::~_exports_IntPointer_templ() {
  auto arg1 = reinterpret_cast<IntPointer *>(this->self);
  if (this->owned && arg1) {
    delete_IntPointer(arg1);
    this->self = nullptr;
  }
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_IntPointer_templ<SWIG_OBJ_WRAP>::_wrap_IntPointer_assign(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  IntPointer *arg1 = (IntPointer *) 0 ;
  int arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int val2 ;
  int ecode2 = 0 ;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_IntPointer_assign.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_IntPointer, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "IntPointer_assign" "', argument " "1"" of type '" "IntPointer *""'"); 
  }
  arg1 = reinterpret_cast< IntPointer * >(argp1);ecode2 = SWIG_AsVal_int(info[0], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "IntPointer_assign" "', argument " "2"" of type '" "int""'");
  } 
  arg2 = static_cast< int >(val2);IntPointer_assign(arg1,arg2);
  jsresult = env.Undefined();
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_IntPointer_templ<SWIG_OBJ_WRAP>::_wrap_IntPointer_value(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  IntPointer *arg1 = (IntPointer *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int result;
  
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_IntPointer_value.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_IntPointer, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "IntPointer_value" "', argument " "1"" of type '" "IntPointer *""'"); 
  }
  arg1 = reinterpret_cast< IntPointer * >(argp1);result = (int)IntPointer_value(arg1);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_IntPointer_templ<SWIG_OBJ_WRAP>::_wrap_IntPointer_cast(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  IntPointer *arg1 = (IntPointer *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int *result = 0 ;
  
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_IntPointer_cast.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_IntPointer, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "IntPointer_cast" "', argument " "1"" of type '" "IntPointer *""'"); 
  }
  arg1 = reinterpret_cast< IntPointer * >(argp1);result = (int *)IntPointer_cast(arg1);
  jsresult = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_int, 0 |  0 );
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_IntPointer_templ<SWIG_OBJ_WRAP>::_wrap_IntPointer_frompointer(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  int *arg1 = (int *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  IntPointer *result = 0 ;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_IntPointer_frompointer.");
  }
  
  res1 = SWIG_ConvertPtr(info[0], &argp1,SWIGTYPE_p_int, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "IntPointer_frompointer" "', argument " "1"" of type '" "int *""'"); 
  }
  arg1 = reinterpret_cast< int * >(argp1);result = (IntPointer *)IntPointer_frompointer(arg1);
  jsresult = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_IntPointer, 0 |  0 );
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


template <typename SWIG_OBJ_WRAP>
// js_ctor
// This is the main constructor
_exports_LongArray_templ<SWIG_OBJ_WRAP>::_exports_LongArray_templ(const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  Napi::Env env = info.Env();
  
  this->info = SWIGTYPE_p_LongArray;
  if (info.Length() == 1 && info[0].IsExternal()) {
    // This constructor has been called internally from C++/SWIG
    // to wrap an already existing C++ object in JS
    this->self = info[0].As<Napi::External<void>>().Data();
    this->owned = false;
    return;
  }
  this->owned = true;
  
  size_t arg1 ;
  size_t val1 ;
  int ecode1 = 0 ;
  LongArray *result;
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_new_LongArray.");
  }
  ecode1 = SWIG_AsVal_size_t(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "new_LongArray" "', argument " "1"" of type '" "size_t""'");
  } 
  arg1 = static_cast< size_t >(val1);result = (LongArray *)new_LongArray(SWIG_STD_MOVE(arg1));
  
  
  
  this->self = result;
  return;
  goto fail;
fail:
  return;
}

// This is the bypass constructor to be used from child classes
template <typename SWIG_OBJ_WRAP>
_exports_LongArray_templ<SWIG_OBJ_WRAP>::_exports_LongArray_templ(bool, const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  
}

SWIGINTERN void delete_LongArray(LongArray *self){
  delete [] self;
}

// js_dtoroverride
template <typename SWIG_OBJ_WRAP>
_exports_LongArray_templ<SWIG_OBJ_WRAP>::~_exports_LongArray_templ() {
  auto arg1 = reinterpret_cast<LongArray *>(this->self);
  if (this->owned && arg1) {
    delete_LongArray(arg1);
    this->self = nullptr;
  }
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_LongArray_templ<SWIG_OBJ_WRAP>::_wrap_LongArray_getitem(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  LongArray *arg1 = (LongArray *) 0 ;
  size_t arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  size_t val2 ;
  int ecode2 = 0 ;
  unsigned long result;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LongArray_getitem.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_LongArray, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LongArray_getitem" "', argument " "1"" of type '" "LongArray *""'"); 
  }
  arg1 = reinterpret_cast< LongArray * >(argp1);ecode2 = SWIG_AsVal_size_t(info[0], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LongArray_getitem" "', argument " "2"" of type '" "size_t""'");
  } 
  arg2 = static_cast< size_t >(val2);result = (unsigned long)LongArray_getitem(arg1,SWIG_STD_MOVE(arg2));
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_LongArray_templ<SWIG_OBJ_WRAP>::_wrap_LongArray_setitem(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  LongArray *arg1 = (LongArray *) 0 ;
  size_t arg2 ;
  unsigned long arg3 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  size_t val2 ;
  int ecode2 = 0 ;
  unsigned long val3 ;
  int ecode3 = 0 ;
  
  if(static_cast<int>(info.Length()) < 2 || static_cast<int>(info.Length()) > 2) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LongArray_setitem.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_LongArray, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LongArray_setitem" "', argument " "1"" of type '" "LongArray *""'"); 
  }
  arg1 = reinterpret_cast< LongArray * >(argp1);ecode2 = SWIG_AsVal_size_t(info[0], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LongArray_setitem" "', argument " "2"" of type '" "size_t""'");
  } 
  arg2 = static_cast< size_t >(val2);ecode3 = SWIG_AsVal_unsigned_SS_long(info[1], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "LongArray_setitem" "', argument " "3"" of type '" "unsigned long""'");
  } 
  arg3 = static_cast< unsigned long >(val3);LongArray_setitem(arg1,SWIG_STD_MOVE(arg2),arg3);
  jsresult = env.Undefined();
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_LongArray_templ<SWIG_OBJ_WRAP>::_wrap_LongArray_cast(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  LongArray *arg1 = (LongArray *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned long *result = 0 ;
  
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LongArray_cast.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_LongArray, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LongArray_cast" "', argument " "1"" of type '" "LongArray *""'"); 
  }
  arg1 = reinterpret_cast< LongArray * >(argp1);result = (unsigned long *)LongArray_cast(arg1);
  jsresult = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_unsigned_long, 0 |  0 );
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_LongArray_templ<SWIG_OBJ_WRAP>::_wrap_LongArray_frompointer(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  unsigned long *arg1 = (unsigned long *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  LongArray *result = 0 ;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LongArray_frompointer.");
  }
  
  res1 = SWIG_ConvertPtr(info[0], &argp1,SWIGTYPE_p_unsigned_long, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LongArray_frompointer" "', argument " "1"" of type '" "unsigned long *""'"); 
  }
  arg1 = reinterpret_cast< unsigned long * >(argp1);result = (LongArray *)LongArray_frompointer(arg1);
  jsresult = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_LongArray, 0 |  0 );
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


template <typename SWIG_OBJ_WRAP>
// js_ctor
// This is the main constructor
_exports_CharArray_templ<SWIG_OBJ_WRAP>::_exports_CharArray_templ(const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  Napi::Env env = info.Env();
  
  this->info = SWIGTYPE_p_CharArray;
  if (info.Length() == 1 && info[0].IsExternal()) {
    // This constructor has been called internally from C++/SWIG
    // to wrap an already existing C++ object in JS
    this->self = info[0].As<Napi::External<void>>().Data();
    this->owned = false;
    return;
  }
  this->owned = true;
  
  size_t arg1 ;
  size_t val1 ;
  int ecode1 = 0 ;
  CharArray *result;
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_new_CharArray.");
  }
  ecode1 = SWIG_AsVal_size_t(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "new_CharArray" "', argument " "1"" of type '" "size_t""'");
  } 
  arg1 = static_cast< size_t >(val1);result = (CharArray *)new_CharArray(SWIG_STD_MOVE(arg1));
  
  
  
  this->self = result;
  return;
  goto fail;
fail:
  return;
}

// This is the bypass constructor to be used from child classes
template <typename SWIG_OBJ_WRAP>
_exports_CharArray_templ<SWIG_OBJ_WRAP>::_exports_CharArray_templ(bool, const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  
}

SWIGINTERN void delete_CharArray(CharArray *self){
  delete [] self;
}

// js_dtoroverride
template <typename SWIG_OBJ_WRAP>
_exports_CharArray_templ<SWIG_OBJ_WRAP>::~_exports_CharArray_templ() {
  auto arg1 = reinterpret_cast<CharArray *>(this->self);
  if (this->owned && arg1) {
    delete_CharArray(arg1);
    this->self = nullptr;
  }
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_CharArray_templ<SWIG_OBJ_WRAP>::_wrap_CharArray_getitem(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  CharArray *arg1 = (CharArray *) 0 ;
  size_t arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  size_t val2 ;
  int ecode2 = 0 ;
  unsigned char result;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_CharArray_getitem.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_CharArray, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "CharArray_getitem" "', argument " "1"" of type '" "CharArray *""'"); 
  }
  arg1 = reinterpret_cast< CharArray * >(argp1);ecode2 = SWIG_AsVal_size_t(info[0], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "CharArray_getitem" "', argument " "2"" of type '" "size_t""'");
  } 
  arg2 = static_cast< size_t >(val2);result = (unsigned char)CharArray_getitem(arg1,SWIG_STD_MOVE(arg2));
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_CharArray_templ<SWIG_OBJ_WRAP>::_wrap_CharArray_setitem(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  CharArray *arg1 = (CharArray *) 0 ;
  size_t arg2 ;
  unsigned char arg3 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  size_t val2 ;
  int ecode2 = 0 ;
  unsigned char val3 ;
  int ecode3 = 0 ;
  
  if(static_cast<int>(info.Length()) < 2 || static_cast<int>(info.Length()) > 2) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_CharArray_setitem.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_CharArray, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "CharArray_setitem" "', argument " "1"" of type '" "CharArray *""'"); 
  }
  arg1 = reinterpret_cast< CharArray * >(argp1);ecode2 = SWIG_AsVal_size_t(info[0], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "CharArray_setitem" "', argument " "2"" of type '" "size_t""'");
  } 
  arg2 = static_cast< size_t >(val2);ecode3 = SWIG_AsVal_unsigned_SS_char(info[1], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "CharArray_setitem" "', argument " "3"" of type '" "unsigned char""'");
  } 
  arg3 = static_cast< unsigned char >(val3);CharArray_setitem(arg1,SWIG_STD_MOVE(arg2),arg3);
  jsresult = env.Undefined();
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_CharArray_templ<SWIG_OBJ_WRAP>::_wrap_CharArray_cast(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  CharArray *arg1 = (CharArray *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char *result = 0 ;
  
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_CharArray_cast.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_CharArray, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "CharArray_cast" "', argument " "1"" of type '" "CharArray *""'"); 
  }
  arg1 = reinterpret_cast< CharArray * >(argp1);result = (unsigned char *)CharArray_cast(arg1);
  jsresult = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_unsigned_char, 0 |  0 );
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_CharArray_templ<SWIG_OBJ_WRAP>::_wrap_CharArray_frompointer(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  unsigned char *arg1 = (unsigned char *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  CharArray *result = 0 ;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_CharArray_frompointer.");
  }
  
  res1 = SWIG_ConvertPtr(info[0], &argp1,SWIGTYPE_p_unsigned_char, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "CharArray_frompointer" "', argument " "1"" of type '" "unsigned char *""'"); 
  }
  arg1 = reinterpret_cast< unsigned char * >(argp1);result = (CharArray *)CharArray_frompointer(arg1);
  jsresult = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_CharArray, 0 |  0 );
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_linOK_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(linOK));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_linERR_NOMSG_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(linERR_NOMSG));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_linERR_NOTRUNNING_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(linERR_NOTRUNNING));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_linERR_RUNNING_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(linERR_RUNNING));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_linERR_MASTERONLY_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(linERR_MASTERONLY));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_linERR_SLAVEONLY_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(linERR_SLAVEONLY));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_linERR_PARAM_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(linERR_PARAM));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_linERR_NOTFOUND_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(linERR_NOTFOUND));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_linERR_NOMEM_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(linERR_NOMEM));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_linERR_NOCHANNELS_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(linERR_NOCHANNELS));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_linERR_TIMEOUT_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(linERR_TIMEOUT));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_linERR_NOTINITIALIZED_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(linERR_NOTINITIALIZED));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_linERR_NOHANDLES_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(linERR_NOHANDLES));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_linERR_INVHANDLE_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(linERR_INVHANDLE));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_linERR_CANERROR_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(linERR_CANERROR));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_linERR_ERRRESP_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(linERR_ERRRESP));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_linERR_WRONGRESP_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(linERR_WRONGRESP));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_linERR_DRIVER_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(linERR_DRIVER));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_linERR_DRIVERFAILED_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(linERR_DRIVERFAILED));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_linERR_NOCARD_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(linERR_NOCARD));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_linERR_LICENSE_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(linERR_LICENSE));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_linERR_INTERNAL_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(linERR_INTERNAL));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_linERR_NO_ACCESS_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(linERR_NO_ACCESS));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_linERR_VERSION_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(linERR_VERSION));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_linERR_NO_REF_POWER_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(linERR_NO_REF_POWER));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_linERR_NOT_IMPLEMENTED_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(linERR_NOT_IMPLEMENTED));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_LinMessageInfo_templ<SWIG_OBJ_WRAP>::_wrap_LinMessageInfo_timestamp_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  LinMessageInfo *arg1 = (LinMessageInfo *) 0 ;
  unsigned long arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned long val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_LinMessageInfo, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LinMessageInfo_timestamp_set" "', argument " "1"" of type '" "LinMessageInfo *""'"); 
  }
  arg1 = reinterpret_cast< LinMessageInfo * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_long(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LinMessageInfo_timestamp_set" "', argument " "2"" of type '" "unsigned long""'");
  } 
  arg2 = static_cast< unsigned long >(val2);if (arg1) (arg1)->timestamp = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_LinMessageInfo_templ<SWIG_OBJ_WRAP>::_wrap_LinMessageInfo_timestamp_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  LinMessageInfo *arg1 = (LinMessageInfo *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned long result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_LinMessageInfo, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LinMessageInfo_timestamp_get" "', argument " "1"" of type '" "LinMessageInfo *""'"); 
  }
  arg1 = reinterpret_cast< LinMessageInfo * >(argp1);result = (unsigned long) ((arg1)->timestamp);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_LinMessageInfo_templ<SWIG_OBJ_WRAP>::_wrap_LinMessageInfo_synchBreakLength_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  LinMessageInfo *arg1 = (LinMessageInfo *) 0 ;
  unsigned long arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned long val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_LinMessageInfo, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LinMessageInfo_synchBreakLength_set" "', argument " "1"" of type '" "LinMessageInfo *""'"); 
  }
  arg1 = reinterpret_cast< LinMessageInfo * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_long(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LinMessageInfo_synchBreakLength_set" "', argument " "2"" of type '" "unsigned long""'");
  } 
  arg2 = static_cast< unsigned long >(val2);if (arg1) (arg1)->synchBreakLength = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_LinMessageInfo_templ<SWIG_OBJ_WRAP>::_wrap_LinMessageInfo_synchBreakLength_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  LinMessageInfo *arg1 = (LinMessageInfo *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned long result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_LinMessageInfo, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LinMessageInfo_synchBreakLength_get" "', argument " "1"" of type '" "LinMessageInfo *""'"); 
  }
  arg1 = reinterpret_cast< LinMessageInfo * >(argp1);result = (unsigned long) ((arg1)->synchBreakLength);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_LinMessageInfo_templ<SWIG_OBJ_WRAP>::_wrap_LinMessageInfo_frameLength_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  LinMessageInfo *arg1 = (LinMessageInfo *) 0 ;
  unsigned long arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned long val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_LinMessageInfo, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LinMessageInfo_frameLength_set" "', argument " "1"" of type '" "LinMessageInfo *""'"); 
  }
  arg1 = reinterpret_cast< LinMessageInfo * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_long(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LinMessageInfo_frameLength_set" "', argument " "2"" of type '" "unsigned long""'");
  } 
  arg2 = static_cast< unsigned long >(val2);if (arg1) (arg1)->frameLength = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_LinMessageInfo_templ<SWIG_OBJ_WRAP>::_wrap_LinMessageInfo_frameLength_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  LinMessageInfo *arg1 = (LinMessageInfo *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned long result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_LinMessageInfo, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LinMessageInfo_frameLength_get" "', argument " "1"" of type '" "LinMessageInfo *""'"); 
  }
  arg1 = reinterpret_cast< LinMessageInfo * >(argp1);result = (unsigned long) ((arg1)->frameLength);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_LinMessageInfo_templ<SWIG_OBJ_WRAP>::_wrap_LinMessageInfo_bitrate_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  LinMessageInfo *arg1 = (LinMessageInfo *) 0 ;
  unsigned long arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned long val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_LinMessageInfo, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LinMessageInfo_bitrate_set" "', argument " "1"" of type '" "LinMessageInfo *""'"); 
  }
  arg1 = reinterpret_cast< LinMessageInfo * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_long(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LinMessageInfo_bitrate_set" "', argument " "2"" of type '" "unsigned long""'");
  } 
  arg2 = static_cast< unsigned long >(val2);if (arg1) (arg1)->bitrate = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_LinMessageInfo_templ<SWIG_OBJ_WRAP>::_wrap_LinMessageInfo_bitrate_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  LinMessageInfo *arg1 = (LinMessageInfo *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned long result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_LinMessageInfo, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LinMessageInfo_bitrate_get" "', argument " "1"" of type '" "LinMessageInfo *""'"); 
  }
  arg1 = reinterpret_cast< LinMessageInfo * >(argp1);result = (unsigned long) ((arg1)->bitrate);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_LinMessageInfo_templ<SWIG_OBJ_WRAP>::_wrap_LinMessageInfo_checkSum_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  LinMessageInfo *arg1 = (LinMessageInfo *) 0 ;
  unsigned char arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_LinMessageInfo, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LinMessageInfo_checkSum_set" "', argument " "1"" of type '" "LinMessageInfo *""'"); 
  }
  arg1 = reinterpret_cast< LinMessageInfo * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LinMessageInfo_checkSum_set" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);if (arg1) (arg1)->checkSum = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_LinMessageInfo_templ<SWIG_OBJ_WRAP>::_wrap_LinMessageInfo_checkSum_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  LinMessageInfo *arg1 = (LinMessageInfo *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_LinMessageInfo, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LinMessageInfo_checkSum_get" "', argument " "1"" of type '" "LinMessageInfo *""'"); 
  }
  arg1 = reinterpret_cast< LinMessageInfo * >(argp1);result = (unsigned char) ((arg1)->checkSum);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_LinMessageInfo_templ<SWIG_OBJ_WRAP>::_wrap_LinMessageInfo_idPar_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  LinMessageInfo *arg1 = (LinMessageInfo *) 0 ;
  unsigned char arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_LinMessageInfo, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LinMessageInfo_idPar_set" "', argument " "1"" of type '" "LinMessageInfo *""'"); 
  }
  arg1 = reinterpret_cast< LinMessageInfo * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LinMessageInfo_idPar_set" "', argument " "2"" of type '" "unsigned char""'");
  } 
  arg2 = static_cast< unsigned char >(val2);if (arg1) (arg1)->idPar = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_LinMessageInfo_templ<SWIG_OBJ_WRAP>::_wrap_LinMessageInfo_idPar_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  LinMessageInfo *arg1 = (LinMessageInfo *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_LinMessageInfo, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LinMessageInfo_idPar_get" "', argument " "1"" of type '" "LinMessageInfo *""'"); 
  }
  arg1 = reinterpret_cast< LinMessageInfo * >(argp1);result = (unsigned char) ((arg1)->idPar);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_LinMessageInfo_templ<SWIG_OBJ_WRAP>::_wrap_LinMessageInfo_z_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  LinMessageInfo *arg1 = (LinMessageInfo *) 0 ;
  unsigned short arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned short val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_LinMessageInfo, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LinMessageInfo_z_set" "', argument " "1"" of type '" "LinMessageInfo *""'"); 
  }
  arg1 = reinterpret_cast< LinMessageInfo * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_short(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LinMessageInfo_z_set" "', argument " "2"" of type '" "unsigned short""'");
  } 
  arg2 = static_cast< unsigned short >(val2);if (arg1) (arg1)->z = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_LinMessageInfo_templ<SWIG_OBJ_WRAP>::_wrap_LinMessageInfo_z_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  LinMessageInfo *arg1 = (LinMessageInfo *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned short result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_LinMessageInfo, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LinMessageInfo_z_get" "', argument " "1"" of type '" "LinMessageInfo *""'"); 
  }
  arg1 = reinterpret_cast< LinMessageInfo * >(argp1);result = (unsigned short) ((arg1)->z);
  jsresult = SWIG_From_unsigned_SS_short  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned short >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_LinMessageInfo_templ<SWIG_OBJ_WRAP>::_wrap_LinMessageInfo_synchEdgeTime_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  LinMessageInfo *arg1 = (LinMessageInfo *) 0 ;
  unsigned long *arg2 = (unsigned long *) (unsigned long *)0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  void *argp2 = 0 ;
  int res2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_LinMessageInfo, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LinMessageInfo_synchEdgeTime_set" "', argument " "1"" of type '" "LinMessageInfo *""'"); 
  }
  arg1 = reinterpret_cast< LinMessageInfo * >(argp1);res2 = SWIG_ConvertPtr(value, &argp2,SWIGTYPE_p_unsigned_long, 0 |  0 );
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "LinMessageInfo_synchEdgeTime_set" "', argument " "2"" of type '" "unsigned long [4]""'"); 
  } 
  arg2 = reinterpret_cast< unsigned long * >(argp2);{
    if (arg2) {
      size_t ii = 0;
      for (; ii < (size_t)4; ++ii) *(unsigned long *)&arg1->synchEdgeTime[ii] = *((unsigned long *)arg2 + ii);
    } else {
      SWIG_exception_fail(SWIG_ValueError, "invalid null reference " "in variable '""synchEdgeTime""' of type '""unsigned long [4]""'");
    }
  }
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_LinMessageInfo_templ<SWIG_OBJ_WRAP>::_wrap_LinMessageInfo_synchEdgeTime_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  LinMessageInfo *arg1 = (LinMessageInfo *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned long *result = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_LinMessageInfo, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LinMessageInfo_synchEdgeTime_get" "', argument " "1"" of type '" "LinMessageInfo *""'"); 
  }
  arg1 = reinterpret_cast< LinMessageInfo * >(argp1);result = (unsigned long *)(unsigned long *) ((arg1)->synchEdgeTime);
  jsresult = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_unsigned_long, 0 |  0 );
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_LinMessageInfo_templ<SWIG_OBJ_WRAP>::_wrap_LinMessageInfo_byteTime_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  LinMessageInfo *arg1 = (LinMessageInfo *) 0 ;
  unsigned long *arg2 = (unsigned long *) (unsigned long *)0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  void *argp2 = 0 ;
  int res2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_LinMessageInfo, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LinMessageInfo_byteTime_set" "', argument " "1"" of type '" "LinMessageInfo *""'"); 
  }
  arg1 = reinterpret_cast< LinMessageInfo * >(argp1);res2 = SWIG_ConvertPtr(value, &argp2,SWIGTYPE_p_unsigned_long, 0 |  0 );
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "LinMessageInfo_byteTime_set" "', argument " "2"" of type '" "unsigned long [8]""'"); 
  } 
  arg2 = reinterpret_cast< unsigned long * >(argp2);{
    if (arg2) {
      size_t ii = 0;
      for (; ii < (size_t)8; ++ii) *(unsigned long *)&arg1->byteTime[ii] = *((unsigned long *)arg2 + ii);
    } else {
      SWIG_exception_fail(SWIG_ValueError, "invalid null reference " "in variable '""byteTime""' of type '""unsigned long [8]""'");
    }
  }
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_LinMessageInfo_templ<SWIG_OBJ_WRAP>::_wrap_LinMessageInfo_byteTime_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  LinMessageInfo *arg1 = (LinMessageInfo *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned long *result = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_LinMessageInfo, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LinMessageInfo_byteTime_get" "', argument " "1"" of type '" "LinMessageInfo *""'"); 
  }
  arg1 = reinterpret_cast< LinMessageInfo * >(argp1);result = (unsigned long *)(unsigned long *) ((arg1)->byteTime);
  jsresult = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_unsigned_long, 0 |  0 );
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


template <typename SWIG_OBJ_WRAP>
// js_ctor
// This is the main constructor
_exports_LinMessageInfo_templ<SWIG_OBJ_WRAP>::_exports_LinMessageInfo_templ(const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  Napi::Env env = info.Env();
  
  this->info = SWIGTYPE_p_LinMessageInfo;
  if (info.Length() == 1 && info[0].IsExternal()) {
    // This constructor has been called internally from C++/SWIG
    // to wrap an already existing C++ object in JS
    this->self = info[0].As<Napi::External<void>>().Data();
    this->owned = false;
    return;
  }
  this->owned = true;
  
  LinMessageInfo *result;
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_new_LinMessageInfo.");
  }
  result = (LinMessageInfo *)new LinMessageInfo();
  
  
  this->self = result;
  return;
  goto fail;
fail:
  return;
}

// This is the bypass constructor to be used from child classes
template <typename SWIG_OBJ_WRAP>
_exports_LinMessageInfo_templ<SWIG_OBJ_WRAP>::_exports_LinMessageInfo_templ(bool, const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  
}


// js_dtoroverride
template <typename SWIG_OBJ_WRAP>
_exports_LinMessageInfo_templ<SWIG_OBJ_WRAP>::~_exports_LinMessageInfo_templ() {
  auto arg1 = reinterpret_cast<LinMessageInfo *>(this->self);
  if (this->owned && arg1) {
    delete arg1;
    this->self = nullptr;
  }
}


// js_global_getter
Napi::Value exports_LIN_TX_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(1));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_RX_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(2));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_WAKEUP_FRAME_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(4));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_NODATA_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(8));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_CSUM_ERROR_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(16));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_PARITY_ERROR_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(32));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_SYNCH_ERROR_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(64));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_BIT_ERROR_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(128));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_linCHANNELDATA_CARD_FIRMWARE_REV_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(9));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_linInitializeLibrary(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_linInitializeLibrary.");
  }
  
  linInitializeLibrary();
  jsresult = env.Undefined();
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_linUnloadLibrary(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_linUnloadLibrary.");
  }
  
  linUnloadLibrary();
  jsresult = env.Undefined();
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_linGetTransceiverData(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  int arg1 ;
  unsigned char *arg2 = (unsigned char *) (unsigned char *)0 ;
  unsigned char *arg3 = (unsigned char *) (unsigned char *)0 ;
  int *arg4 = (int *) 0 ;
  int val1 ;
  int ecode1 = 0 ;
  void *argp2 = 0 ;
  int res2 = 0 ;
  void *argp3 = 0 ;
  int res3 = 0 ;
  void *argp4 = 0 ;
  int res4 = 0 ;
  LinStatus result;
  
  if(static_cast<int>(info.Length()) < 4 || static_cast<int>(info.Length()) > 4) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_linGetTransceiverData.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "linGetTransceiverData" "', argument " "1"" of type '" "int""'");
  } 
  arg1 = static_cast< int >(val1);res2 = SWIG_ConvertPtr(info[1], &argp2,SWIGTYPE_p_unsigned_char, 0 |  0 );
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "linGetTransceiverData" "', argument " "2"" of type '" "unsigned char [8]""'"); 
  } 
  arg2 = reinterpret_cast< unsigned char * >(argp2);res3 = SWIG_ConvertPtr(info[2], &argp3,SWIGTYPE_p_unsigned_char, 0 |  0 );
  if (!SWIG_IsOK(res3)) {
    SWIG_exception_fail(SWIG_ArgError(res3), "in method '" "linGetTransceiverData" "', argument " "3"" of type '" "unsigned char [8]""'"); 
  } 
  arg3 = reinterpret_cast< unsigned char * >(argp3);res4 = SWIG_ConvertPtr(info[3], &argp4,SWIGTYPE_p_int, 0 |  0 );
  if (!SWIG_IsOK(res4)) {
    SWIG_exception_fail(SWIG_ArgError(res4), "in method '" "linGetTransceiverData" "', argument " "4"" of type '" "int *""'"); 
  }
  arg4 = reinterpret_cast< int * >(argp4);result = (LinStatus)linGetTransceiverData(arg1,arg2,arg3,arg4);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_linOpenChannel(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  int arg1 ;
  int arg2 ;
  int val1 ;
  int ecode1 = 0 ;
  int val2 ;
  int ecode2 = 0 ;
  LinHandle result;
  
  if(static_cast<int>(info.Length()) < 2 || static_cast<int>(info.Length()) > 2) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_linOpenChannel.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "linOpenChannel" "', argument " "1"" of type '" "int""'");
  } 
  arg1 = static_cast< int >(val1);ecode2 = SWIG_AsVal_int(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "linOpenChannel" "', argument " "2"" of type '" "int""'");
  } 
  arg2 = static_cast< int >(val2);result = (LinHandle)linOpenChannel(arg1,arg2);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_MASTER_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(1));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_SLAVE_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(2));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_linClose(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  LinHandle arg1 ;
  int val1 ;
  int ecode1 = 0 ;
  LinStatus result;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_linClose.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "linClose" "', argument " "1"" of type '" "LinHandle""'");
  } 
  arg1 = static_cast< LinHandle >(val1);result = (LinStatus)linClose(arg1);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_linGetVersion(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  int *arg1 = (int *) 0 ;
  int *arg2 = (int *) 0 ;
  int *arg3 = (int *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  void *argp2 = 0 ;
  int res2 = 0 ;
  void *argp3 = 0 ;
  int res3 = 0 ;
  LinStatus result;
  
  if(static_cast<int>(info.Length()) < 3 || static_cast<int>(info.Length()) > 3) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_linGetVersion.");
  }
  
  res1 = SWIG_ConvertPtr(info[0], &argp1,SWIGTYPE_p_int, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "linGetVersion" "', argument " "1"" of type '" "int *""'"); 
  }
  arg1 = reinterpret_cast< int * >(argp1);res2 = SWIG_ConvertPtr(info[1], &argp2,SWIGTYPE_p_int, 0 |  0 );
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "linGetVersion" "', argument " "2"" of type '" "int *""'"); 
  }
  arg2 = reinterpret_cast< int * >(argp2);res3 = SWIG_ConvertPtr(info[2], &argp3,SWIGTYPE_p_int, 0 |  0 );
  if (!SWIG_IsOK(res3)) {
    SWIG_exception_fail(SWIG_ArgError(res3), "in method '" "linGetVersion" "', argument " "3"" of type '" "int *""'"); 
  }
  arg3 = reinterpret_cast< int * >(argp3);result = (LinStatus)linGetVersion(arg1,arg2,arg3);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_linGetFirmwareVersion(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  LinHandle arg1 ;
  unsigned char *arg2 = (unsigned char *) 0 ;
  unsigned char *arg3 = (unsigned char *) 0 ;
  unsigned char *arg4 = (unsigned char *) 0 ;
  unsigned char *arg5 = (unsigned char *) 0 ;
  unsigned char *arg6 = (unsigned char *) 0 ;
  unsigned char *arg7 = (unsigned char *) 0 ;
  int val1 ;
  int ecode1 = 0 ;
  void *argp2 = 0 ;
  int res2 = 0 ;
  void *argp3 = 0 ;
  int res3 = 0 ;
  void *argp4 = 0 ;
  int res4 = 0 ;
  void *argp5 = 0 ;
  int res5 = 0 ;
  void *argp6 = 0 ;
  int res6 = 0 ;
  void *argp7 = 0 ;
  int res7 = 0 ;
  LinStatus result;
  
  if(static_cast<int>(info.Length()) < 7 || static_cast<int>(info.Length()) > 7) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_linGetFirmwareVersion.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "linGetFirmwareVersion" "', argument " "1"" of type '" "LinHandle""'");
  } 
  arg1 = static_cast< LinHandle >(val1);res2 = SWIG_ConvertPtr(info[1], &argp2,SWIGTYPE_p_unsigned_char, 0 |  0 );
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "linGetFirmwareVersion" "', argument " "2"" of type '" "unsigned char *""'"); 
  }
  arg2 = reinterpret_cast< unsigned char * >(argp2);res3 = SWIG_ConvertPtr(info[2], &argp3,SWIGTYPE_p_unsigned_char, 0 |  0 );
  if (!SWIG_IsOK(res3)) {
    SWIG_exception_fail(SWIG_ArgError(res3), "in method '" "linGetFirmwareVersion" "', argument " "3"" of type '" "unsigned char *""'"); 
  }
  arg3 = reinterpret_cast< unsigned char * >(argp3);res4 = SWIG_ConvertPtr(info[3], &argp4,SWIGTYPE_p_unsigned_char, 0 |  0 );
  if (!SWIG_IsOK(res4)) {
    SWIG_exception_fail(SWIG_ArgError(res4), "in method '" "linGetFirmwareVersion" "', argument " "4"" of type '" "unsigned char *""'"); 
  }
  arg4 = reinterpret_cast< unsigned char * >(argp4);res5 = SWIG_ConvertPtr(info[4], &argp5,SWIGTYPE_p_unsigned_char, 0 |  0 );
  if (!SWIG_IsOK(res5)) {
    SWIG_exception_fail(SWIG_ArgError(res5), "in method '" "linGetFirmwareVersion" "', argument " "5"" of type '" "unsigned char *""'"); 
  }
  arg5 = reinterpret_cast< unsigned char * >(argp5);res6 = SWIG_ConvertPtr(info[5], &argp6,SWIGTYPE_p_unsigned_char, 0 |  0 );
  if (!SWIG_IsOK(res6)) {
    SWIG_exception_fail(SWIG_ArgError(res6), "in method '" "linGetFirmwareVersion" "', argument " "6"" of type '" "unsigned char *""'"); 
  }
  arg6 = reinterpret_cast< unsigned char * >(argp6);res7 = SWIG_ConvertPtr(info[6], &argp7,SWIGTYPE_p_unsigned_char, 0 |  0 );
  if (!SWIG_IsOK(res7)) {
    SWIG_exception_fail(SWIG_ArgError(res7), "in method '" "linGetFirmwareVersion" "', argument " "7"" of type '" "unsigned char *""'"); 
  }
  arg7 = reinterpret_cast< unsigned char * >(argp7);result = (LinStatus)linGetFirmwareVersion(arg1,arg2,arg3,arg4,arg5,arg6,arg7);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  
  
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_linGetChannelData(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  int arg1 ;
  int arg2 ;
  void *arg3 = (void *) 0 ;
  size_t arg4 ;
  int val1 ;
  int ecode1 = 0 ;
  int val2 ;
  int ecode2 = 0 ;
  LinStatus result;
  
  if(static_cast<int>(info.Length()) < 3 || static_cast<int>(info.Length()) > 3) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_linGetChannelData.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "linGetChannelData" "', argument " "1"" of type '" "int""'");
  } 
  arg1 = static_cast< int >(val1);ecode2 = SWIG_AsVal_int(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "linGetChannelData" "', argument " "2"" of type '" "int""'");
  } 
  arg2 = static_cast< int >(val2);{
    {
      if (info[2].IsBuffer()) {
        Napi::Buffer<char> buf = info[2].As<Napi::Buffer<char>>();
        arg3 = reinterpret_cast<char *>(buf.Data());
        arg4 = buf.ByteLength();
        
      } else {
        SWIG_exception_fail(SWIG_TypeError, "in method 'linGetChannelData', argument is not a Buffer");
      }
    }
  }
  result = (LinStatus)linGetChannelData(arg1,arg2,arg3,SWIG_STD_MOVE(arg4));
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_linSetBitrate(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  LinHandle arg1 ;
  unsigned int arg2 ;
  int val1 ;
  int ecode1 = 0 ;
  unsigned int val2 ;
  int ecode2 = 0 ;
  LinStatus result;
  
  if(static_cast<int>(info.Length()) < 2 || static_cast<int>(info.Length()) > 2) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_linSetBitrate.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "linSetBitrate" "', argument " "1"" of type '" "LinHandle""'");
  } 
  arg1 = static_cast< LinHandle >(val1);ecode2 = SWIG_AsVal_unsigned_SS_int(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "linSetBitrate" "', argument " "2"" of type '" "unsigned int""'");
  } 
  arg2 = static_cast< unsigned int >(val2);result = (LinStatus)linSetBitrate(arg1,arg2);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_linBusOn(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  LinHandle arg1 ;
  int val1 ;
  int ecode1 = 0 ;
  LinStatus result;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_linBusOn.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "linBusOn" "', argument " "1"" of type '" "LinHandle""'");
  } 
  arg1 = static_cast< LinHandle >(val1);result = (LinStatus)linBusOn(arg1);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_linBusOff(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  LinHandle arg1 ;
  int val1 ;
  int ecode1 = 0 ;
  LinStatus result;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_linBusOff.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "linBusOff" "', argument " "1"" of type '" "LinHandle""'");
  } 
  arg1 = static_cast< LinHandle >(val1);result = (LinStatus)linBusOff(arg1);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_linReadTimer(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  LinHandle arg1 ;
  int val1 ;
  int ecode1 = 0 ;
  unsigned long result;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_linReadTimer.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "linReadTimer" "', argument " "1"" of type '" "LinHandle""'");
  } 
  arg1 = static_cast< LinHandle >(val1);result = (unsigned long)linReadTimer(arg1);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_linWriteMessage(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  LinHandle arg1 ;
  unsigned int arg2 ;
  void *arg3 = (void *) 0 ;
  unsigned int arg4 ;
  int val1 ;
  int ecode1 = 0 ;
  unsigned int val2 ;
  int ecode2 = 0 ;
  LinStatus result;
  
  if(static_cast<int>(info.Length()) < 3 || static_cast<int>(info.Length()) > 3) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_linWriteMessage.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "linWriteMessage" "', argument " "1"" of type '" "LinHandle""'");
  } 
  arg1 = static_cast< LinHandle >(val1);ecode2 = SWIG_AsVal_unsigned_SS_int(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "linWriteMessage" "', argument " "2"" of type '" "unsigned int""'");
  } 
  arg2 = static_cast< unsigned int >(val2);{
    {
      if (info[2].IsBuffer()) {
        Napi::Buffer<char> buf = info[2].As<Napi::Buffer<char>>();
        arg3 = reinterpret_cast<char *>(buf.Data());
        arg4 = buf.ByteLength();
        
      } else {
        SWIG_exception_fail(SWIG_TypeError, "in method 'linWriteMessage', argument is not a Buffer");
      }
    }
  }
  result = (LinStatus)linWriteMessage(arg1,arg2,(void const *)arg3,arg4);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_linRequestMessage(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  LinHandle arg1 ;
  unsigned int arg2 ;
  int val1 ;
  int ecode1 = 0 ;
  unsigned int val2 ;
  int ecode2 = 0 ;
  LinStatus result;
  
  if(static_cast<int>(info.Length()) < 2 || static_cast<int>(info.Length()) > 2) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_linRequestMessage.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "linRequestMessage" "', argument " "1"" of type '" "LinHandle""'");
  } 
  arg1 = static_cast< LinHandle >(val1);ecode2 = SWIG_AsVal_unsigned_SS_int(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "linRequestMessage" "', argument " "2"" of type '" "unsigned int""'");
  } 
  arg2 = static_cast< unsigned int >(val2);result = (LinStatus)linRequestMessage(arg1,arg2);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_linReadMessage(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  LinHandle arg1 ;
  unsigned int *arg2 = (unsigned int *) 0 ;
  void *arg3 = (void *) 0 ;
  unsigned int *arg4 = (unsigned int *) 0 ;
  unsigned int *arg5 = (unsigned int *) 0 ;
  LinMessageInfo *arg6 = (LinMessageInfo *) 0 ;
  int val1 ;
  int ecode1 = 0 ;
  void *argp2 = 0 ;
  int res2 = 0 ;
  int res3 ;
  void *argp4 = 0 ;
  int res4 = 0 ;
  void *argp5 = 0 ;
  int res5 = 0 ;
  void *argp6 = 0 ;
  int res6 = 0 ;
  LinStatus result;
  
  if(static_cast<int>(info.Length()) < 6 || static_cast<int>(info.Length()) > 6) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_linReadMessage.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "linReadMessage" "', argument " "1"" of type '" "LinHandle""'");
  } 
  arg1 = static_cast< LinHandle >(val1);res2 = SWIG_ConvertPtr(info[1], &argp2,SWIGTYPE_p_unsigned_int, 0 |  0 );
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "linReadMessage" "', argument " "2"" of type '" "unsigned int *""'"); 
  }
  arg2 = reinterpret_cast< unsigned int * >(argp2);res3 = SWIG_ConvertPtr(info[2],SWIG_as_voidptrptr(&arg3), 0, 0);
  if (!SWIG_IsOK(res3)) {
    SWIG_exception_fail(SWIG_ArgError(res3), "in method '" "linReadMessage" "', argument " "3"" of type '" "void *""'"); 
  }res4 = SWIG_ConvertPtr(info[3], &argp4,SWIGTYPE_p_unsigned_int, 0 |  0 );
  if (!SWIG_IsOK(res4)) {
    SWIG_exception_fail(SWIG_ArgError(res4), "in method '" "linReadMessage" "', argument " "4"" of type '" "unsigned int *""'"); 
  }
  arg4 = reinterpret_cast< unsigned int * >(argp4);res5 = SWIG_ConvertPtr(info[4], &argp5,SWIGTYPE_p_unsigned_int, 0 |  0 );
  if (!SWIG_IsOK(res5)) {
    SWIG_exception_fail(SWIG_ArgError(res5), "in method '" "linReadMessage" "', argument " "5"" of type '" "unsigned int *""'"); 
  }
  arg5 = reinterpret_cast< unsigned int * >(argp5);res6 = SWIG_ConvertPtr(info[5], &argp6,SWIGTYPE_p_LinMessageInfo, 0 |  0 );
  if (!SWIG_IsOK(res6)) {
    SWIG_exception_fail(SWIG_ArgError(res6), "in method '" "linReadMessage" "', argument " "6"" of type '" "LinMessageInfo *""'"); 
  }
  arg6 = reinterpret_cast< LinMessageInfo * >(argp6);result = (LinStatus)linReadMessage(arg1,arg2,arg3,arg4,arg5,arg6);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_linReadMessageWait(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  LinHandle arg1 ;
  unsigned int *arg2 = (unsigned int *) 0 ;
  void *arg3 = (void *) 0 ;
  unsigned int *arg4 = (unsigned int *) 0 ;
  unsigned int *arg5 = (unsigned int *) 0 ;
  LinMessageInfo *arg6 = (LinMessageInfo *) 0 ;
  unsigned long arg7 ;
  int val1 ;
  int ecode1 = 0 ;
  void *argp2 = 0 ;
  int res2 = 0 ;
  int res3 ;
  void *argp4 = 0 ;
  int res4 = 0 ;
  void *argp5 = 0 ;
  int res5 = 0 ;
  void *argp6 = 0 ;
  int res6 = 0 ;
  unsigned long val7 ;
  int ecode7 = 0 ;
  LinStatus result;
  
  if(static_cast<int>(info.Length()) < 7 || static_cast<int>(info.Length()) > 7) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_linReadMessageWait.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "linReadMessageWait" "', argument " "1"" of type '" "LinHandle""'");
  } 
  arg1 = static_cast< LinHandle >(val1);res2 = SWIG_ConvertPtr(info[1], &argp2,SWIGTYPE_p_unsigned_int, 0 |  0 );
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "linReadMessageWait" "', argument " "2"" of type '" "unsigned int *""'"); 
  }
  arg2 = reinterpret_cast< unsigned int * >(argp2);res3 = SWIG_ConvertPtr(info[2],SWIG_as_voidptrptr(&arg3), 0, 0);
  if (!SWIG_IsOK(res3)) {
    SWIG_exception_fail(SWIG_ArgError(res3), "in method '" "linReadMessageWait" "', argument " "3"" of type '" "void *""'"); 
  }res4 = SWIG_ConvertPtr(info[3], &argp4,SWIGTYPE_p_unsigned_int, 0 |  0 );
  if (!SWIG_IsOK(res4)) {
    SWIG_exception_fail(SWIG_ArgError(res4), "in method '" "linReadMessageWait" "', argument " "4"" of type '" "unsigned int *""'"); 
  }
  arg4 = reinterpret_cast< unsigned int * >(argp4);res5 = SWIG_ConvertPtr(info[4], &argp5,SWIGTYPE_p_unsigned_int, 0 |  0 );
  if (!SWIG_IsOK(res5)) {
    SWIG_exception_fail(SWIG_ArgError(res5), "in method '" "linReadMessageWait" "', argument " "5"" of type '" "unsigned int *""'"); 
  }
  arg5 = reinterpret_cast< unsigned int * >(argp5);res6 = SWIG_ConvertPtr(info[5], &argp6,SWIGTYPE_p_LinMessageInfo, 0 |  0 );
  if (!SWIG_IsOK(res6)) {
    SWIG_exception_fail(SWIG_ArgError(res6), "in method '" "linReadMessageWait" "', argument " "6"" of type '" "LinMessageInfo *""'"); 
  }
  arg6 = reinterpret_cast< LinMessageInfo * >(argp6);ecode7 = SWIG_AsVal_unsigned_SS_long(info[6], &val7);
  if (!SWIG_IsOK(ecode7)) {
    SWIG_exception_fail(SWIG_ArgError(ecode7), "in method '" "linReadMessageWait" "', argument " "7"" of type '" "unsigned long""'");
  } 
  arg7 = static_cast< unsigned long >(val7);result = (LinStatus)linReadMessageWait(arg1,arg2,arg3,arg4,arg5,arg6,arg7);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  
  
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_linUpdateMessage(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  LinHandle arg1 ;
  unsigned int arg2 ;
  void *arg3 = (void *) 0 ;
  unsigned int arg4 ;
  int val1 ;
  int ecode1 = 0 ;
  unsigned int val2 ;
  int ecode2 = 0 ;
  LinStatus result;
  
  if(static_cast<int>(info.Length()) < 3 || static_cast<int>(info.Length()) > 3) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_linUpdateMessage.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "linUpdateMessage" "', argument " "1"" of type '" "LinHandle""'");
  } 
  arg1 = static_cast< LinHandle >(val1);ecode2 = SWIG_AsVal_unsigned_SS_int(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "linUpdateMessage" "', argument " "2"" of type '" "unsigned int""'");
  } 
  arg2 = static_cast< unsigned int >(val2);{
    {
      if (info[2].IsBuffer()) {
        Napi::Buffer<char> buf = info[2].As<Napi::Buffer<char>>();
        arg3 = reinterpret_cast<char *>(buf.Data());
        arg4 = buf.ByteLength();
        
      } else {
        SWIG_exception_fail(SWIG_TypeError, "in method 'linUpdateMessage', argument is not a Buffer");
      }
    }
  }
  result = (LinStatus)linUpdateMessage(arg1,arg2,(void const *)arg3,arg4);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_linSetupIllegalMessage(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  LinHandle arg1 ;
  unsigned int arg2 ;
  unsigned int arg3 ;
  unsigned int arg4 ;
  int val1 ;
  int ecode1 = 0 ;
  unsigned int val2 ;
  int ecode2 = 0 ;
  unsigned int val3 ;
  int ecode3 = 0 ;
  unsigned int val4 ;
  int ecode4 = 0 ;
  LinStatus result;
  
  if(static_cast<int>(info.Length()) < 4 || static_cast<int>(info.Length()) > 4) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_linSetupIllegalMessage.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "linSetupIllegalMessage" "', argument " "1"" of type '" "LinHandle""'");
  } 
  arg1 = static_cast< LinHandle >(val1);ecode2 = SWIG_AsVal_unsigned_SS_int(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "linSetupIllegalMessage" "', argument " "2"" of type '" "unsigned int""'");
  } 
  arg2 = static_cast< unsigned int >(val2);ecode3 = SWIG_AsVal_unsigned_SS_int(info[2], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "linSetupIllegalMessage" "', argument " "3"" of type '" "unsigned int""'");
  } 
  arg3 = static_cast< unsigned int >(val3);ecode4 = SWIG_AsVal_unsigned_SS_int(info[3], &val4);
  if (!SWIG_IsOK(ecode4)) {
    SWIG_exception_fail(SWIG_ArgError(ecode4), "in method '" "linSetupIllegalMessage" "', argument " "4"" of type '" "unsigned int""'");
  } 
  arg4 = static_cast< unsigned int >(val4);result = (LinStatus)linSetupIllegalMessage(arg1,arg2,arg3,arg4);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_MSG_DISTURB_CSUM_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(1));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_MSG_DISTURB_PARITY_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(2));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_MSG_USE_STANDARD_PARITY_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(0x04));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_MSG_USE_ENHANCED_PARITY_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(0x08));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_linSetupLIN(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  LinHandle arg1 ;
  unsigned int arg2 ;
  unsigned int arg3 ;
  int val1 ;
  int ecode1 = 0 ;
  unsigned int val2 ;
  int ecode2 = 0 ;
  unsigned int val3 ;
  int ecode3 = 0 ;
  LinStatus result;
  
  if(static_cast<int>(info.Length()) < 3 || static_cast<int>(info.Length()) > 3) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_linSetupLIN.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "linSetupLIN" "', argument " "1"" of type '" "LinHandle""'");
  } 
  arg1 = static_cast< LinHandle >(val1);ecode2 = SWIG_AsVal_unsigned_SS_int(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "linSetupLIN" "', argument " "2"" of type '" "unsigned int""'");
  } 
  arg2 = static_cast< unsigned int >(val2);ecode3 = SWIG_AsVal_unsigned_SS_int(info[2], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "linSetupLIN" "', argument " "3"" of type '" "unsigned int""'");
  } 
  arg3 = static_cast< unsigned int >(val3);result = (LinStatus)linSetupLIN(arg1,arg2,arg3);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_ENHANCED_CHECKSUM_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(1));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_VARIABLE_DLC_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(2));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_linWriteWakeup(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  LinHandle arg1 ;
  unsigned int arg2 ;
  unsigned int arg3 ;
  int val1 ;
  int ecode1 = 0 ;
  unsigned int val2 ;
  int ecode2 = 0 ;
  unsigned int val3 ;
  int ecode3 = 0 ;
  LinStatus result;
  
  if(static_cast<int>(info.Length()) < 3 || static_cast<int>(info.Length()) > 3) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_linWriteWakeup.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "linWriteWakeup" "', argument " "1"" of type '" "LinHandle""'");
  } 
  arg1 = static_cast< LinHandle >(val1);ecode2 = SWIG_AsVal_unsigned_SS_int(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "linWriteWakeup" "', argument " "2"" of type '" "unsigned int""'");
  } 
  arg2 = static_cast< unsigned int >(val2);ecode3 = SWIG_AsVal_unsigned_SS_int(info[2], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "linWriteWakeup" "', argument " "3"" of type '" "unsigned int""'");
  } 
  arg3 = static_cast< unsigned int >(val3);result = (LinStatus)linWriteWakeup(arg1,arg2,arg3);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_linClearMessage(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  LinHandle arg1 ;
  unsigned int arg2 ;
  int val1 ;
  int ecode1 = 0 ;
  unsigned int val2 ;
  int ecode2 = 0 ;
  LinStatus result;
  
  if(static_cast<int>(info.Length()) < 2 || static_cast<int>(info.Length()) > 2) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_linClearMessage.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "linClearMessage" "', argument " "1"" of type '" "LinHandle""'");
  } 
  arg1 = static_cast< LinHandle >(val1);ecode2 = SWIG_AsVal_unsigned_SS_int(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "linClearMessage" "', argument " "2"" of type '" "unsigned int""'");
  } 
  arg2 = static_cast< unsigned int >(val2);result = (LinStatus)linClearMessage(arg1,arg2);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_linWriteSync(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  LinHandle arg1 ;
  unsigned long arg2 ;
  int val1 ;
  int ecode1 = 0 ;
  unsigned long val2 ;
  int ecode2 = 0 ;
  LinStatus result;
  
  if(static_cast<int>(info.Length()) < 2 || static_cast<int>(info.Length()) > 2) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_linWriteSync.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "linWriteSync" "', argument " "1"" of type '" "LinHandle""'");
  } 
  arg1 = static_cast< LinHandle >(val1);ecode2 = SWIG_AsVal_unsigned_SS_long(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "linWriteSync" "', argument " "2"" of type '" "unsigned long""'");
  } 
  arg2 = static_cast< unsigned long >(val2);result = (LinStatus)linWriteSync(arg1,arg2);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_linGetCanHandle(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  LinHandle arg1 ;
  unsigned int *arg2 = (unsigned int *) 0 ;
  int val1 ;
  int ecode1 = 0 ;
  void *argp2 = 0 ;
  int res2 = 0 ;
  LinStatus result;
  
  if(static_cast<int>(info.Length()) < 2 || static_cast<int>(info.Length()) > 2) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_linGetCanHandle.");
  }
  
  ecode1 = SWIG_AsVal_int(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "linGetCanHandle" "', argument " "1"" of type '" "LinHandle""'");
  } 
  arg1 = static_cast< LinHandle >(val1);res2 = SWIG_ConvertPtr(info[1], &argp2,SWIGTYPE_p_unsigned_int, 0 |  0 );
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "linGetCanHandle" "', argument " "2"" of type '" "unsigned int *""'"); 
  }
  arg2 = reinterpret_cast< unsigned int * >(argp2);result = (LinStatus)linGetCanHandle(arg1,arg2);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LoadDll(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  char *arg1 = (char *) 0 ;
  int res1 ;
  char *buf1 = 0 ;
  int alloc1 = 0 ;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LoadDll.");
  }
  
  res1 = SWIG_AsCharPtrAndSize(info[0], &buf1, NULL, &alloc1);
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LoadDll" "', argument " "1"" of type '" "char const *""'");
  }
  arg1 = reinterpret_cast< char * >(buf1);LoadDll((char const *)arg1);
  jsresult = env.Undefined();
  if (alloc1 == SWIG_NEWOBJ) delete[] buf1;
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


/* -------- TYPE CONVERSION AND EQUIVALENCE RULES (BEGIN) -------- */

static void *_p_LongArrayTo_p_unsigned_long(void *x, int *SWIGUNUSEDPARM(newmemory)) {
    return (void *)((unsigned long *)  ((LongArray *) x));
}
static void *_p_CharArrayTo_p_unsigned_char(void *x, int *SWIGUNUSEDPARM(newmemory)) {
    return (void *)((unsigned char *)  ((CharArray *) x));
}
static void *_p_CharPointerTo_p_unsigned_char(void *x, int *SWIGUNUSEDPARM(newmemory)) {
    return (void *)((unsigned char *)  ((CharPointer *) x));
}
static void *_p_IntPointerTo_p_int(void *x, int *SWIGUNUSEDPARM(newmemory)) {
    return (void *)((int *)  ((IntPointer *) x));
}
static void *_p_p_LongArrayTo_p_p_unsigned_long(void *x, int *SWIGUNUSEDPARM(newmemory)) {
    return (void *)((unsigned long **)  ((LongArray **) x));
}
static swig_type_info _swigt__p_CharArray = {"_p_CharArray", "CharArray *|p_CharArray", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_CharPointer = {"_p_CharPointer", "CharPointer *|p_CharPointer", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_IntPointer = {"_p_IntPointer", "p_IntPointer|IntPointer *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_LinMessageInfo = {"_p_LinMessageInfo", "LinMessageInfo *|p_LinMessageInfo", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_LinStatus = {"_p_LinStatus", "LinStatus *|enum LinStatus *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_LongArray = {"_p_LongArray", "p_LongArray|LongArray *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p___int64 = {"_p___int64", "LONG64 *|LONGLONG *|__int64 *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_char = {"_p_char", "CCHAR *|CHAR *|TCHAR *|char *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_float = {"_p_float", "FLOAT *|float *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_int = {"_p_int", "BOOL *|INT *|INT32 *|INT_PTR *|LONG32 *|LinHandle *|int32_t *|int_fast16_t *|int_fast32_t *|int_least32_t *|intptr_t *|int *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_long = {"_p_long", "HRESULT *|LONG *|LONG_PTR *|SHANDLE_PTR *|SSIZE_T *|long *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_long_long = {"_p_long_long", "int64_t *|int_fast64_t *|int_least64_t *|intmax_t *|long long *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_p_char = {"_p_p_char", "LPCTSTR *|LPCUTSTR *|LPTCH *|LPTSTR *|LPUTSTR *|PCTSTR *|PCUTSTR *|PTCH *|PTSTR *|PUTSTR *|char **", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_p_unsigned_long = {"_p_p_unsigned_long", "PLCID *|unsigned long **", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_p_LongArray = {"_p_p_LongArray", 0, 0, 0, 0, 0};
static swig_type_info _swigt__p_short = {"_p_short", "HALF_PTR *|INT16 *|SHORT *|int16_t *|int_least16_t *|short *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_signed___int64 = {"_p_signed___int64", "INT64 *|signed __int64 *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_signed_char = {"_p_signed_char", "INT8 *|int8_t *|int_fast8_t *|int_least8_t *|signed char *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_unsigned___int64 = {"_p_unsigned___int64", "DWORD64 *|DWORDLONG *|UINT64 *|ULONG64 *|ULONGLONG *|unsigned __int64 *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_unsigned_char = {"_p_unsigned_char", "BOOLEAN *|BYTE *|FCHAR *|TBYTE *|UCHAR *|UINT8 *|uint8_t *|uint_fast8_t *|uint_least8_t *|unsigned char *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_unsigned_int = {"_p_unsigned_int", "DWORD32 *|UINT *|UINT32 *|UINT_PTR *|ULONG32 *|uint32_t *|uint_fast16_t *|uint_fast32_t *|uint_least32_t *|uintptr_t *|unsigned int *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_unsigned_long = {"_p_unsigned_long", "DWORD *|DWORD_PTR *|FLONG *|HANDLE_PTR *|LCID *|SIZE_T *|ULONG *|ULONG_PTR *|unsigned long *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_unsigned_long_long = {"_p_unsigned_long_long", "uint64_t *|uint_fast64_t *|uint_least64_t *|uintmax_t *|unsigned long long *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_unsigned_short = {"_p_unsigned_short", "FSHORT *|LANGID *|UHALF_PTR *|UINT16 *|USHORT *|WORD *|uint16_t *|uint_least16_t *|unsigned short *", 0, 0, (void*)0, 0};

static swig_type_info *swig_type_initial[] = {
  &_swigt__p_CharArray,
  &_swigt__p_CharPointer,
  &_swigt__p_IntPointer,
  &_swigt__p_LinMessageInfo,
  &_swigt__p_LinStatus,
  &_swigt__p_LongArray,
  &_swigt__p___int64,
  &_swigt__p_char,
  &_swigt__p_float,
  &_swigt__p_int,
  &_swigt__p_long,
  &_swigt__p_long_long,
  &_swigt__p_p_LongArray,
  &_swigt__p_p_char,
  &_swigt__p_p_unsigned_long,
  &_swigt__p_short,
  &_swigt__p_signed___int64,
  &_swigt__p_signed_char,
  &_swigt__p_unsigned___int64,
  &_swigt__p_unsigned_char,
  &_swigt__p_unsigned_int,
  &_swigt__p_unsigned_long,
  &_swigt__p_unsigned_long_long,
  &_swigt__p_unsigned_short,
};

static swig_cast_info _swigc__p_CharArray[] = {  {&_swigt__p_CharArray, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_CharPointer[] = {  {&_swigt__p_CharPointer, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_IntPointer[] = {  {&_swigt__p_IntPointer, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_LinMessageInfo[] = {  {&_swigt__p_LinMessageInfo, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_LinStatus[] = {  {&_swigt__p_LinStatus, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_LongArray[] = {  {&_swigt__p_LongArray, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p___int64[] = {  {&_swigt__p___int64, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_char[] = {  {&_swigt__p_char, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_float[] = {  {&_swigt__p_float, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_int[] = {  {&_swigt__p_int, 0, 0, 0},  {&_swigt__p_IntPointer, _p_IntPointerTo_p_int, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_long[] = {  {&_swigt__p_long, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_long_long[] = {  {&_swigt__p_long_long, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_p_char[] = {  {&_swigt__p_p_char, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_p_LongArray[] = {{&_swigt__p_p_LongArray, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_p_unsigned_long[] = {  {&_swigt__p_p_unsigned_long, 0, 0, 0},  {&_swigt__p_p_LongArray, _p_p_LongArrayTo_p_p_unsigned_long, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_short[] = {  {&_swigt__p_short, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_signed___int64[] = {  {&_swigt__p_signed___int64, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_signed_char[] = {  {&_swigt__p_signed_char, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_unsigned___int64[] = {  {&_swigt__p_unsigned___int64, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_unsigned_char[] = {  {&_swigt__p_unsigned_char, 0, 0, 0},  {&_swigt__p_CharArray, _p_CharArrayTo_p_unsigned_char, 0, 0},  {&_swigt__p_CharPointer, _p_CharPointerTo_p_unsigned_char, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_unsigned_int[] = {  {&_swigt__p_unsigned_int, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_unsigned_long[] = {  {&_swigt__p_unsigned_long, 0, 0, 0},  {&_swigt__p_LongArray, _p_LongArrayTo_p_unsigned_long, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_unsigned_long_long[] = {  {&_swigt__p_unsigned_long_long, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_unsigned_short[] = {  {&_swigt__p_unsigned_short, 0, 0, 0},{0, 0, 0, 0}};

static swig_cast_info *swig_cast_initial[] = {
  _swigc__p_CharArray,
  _swigc__p_CharPointer,
  _swigc__p_IntPointer,
  _swigc__p_LinMessageInfo,
  _swigc__p_LinStatus,
  _swigc__p_LongArray,
  _swigc__p___int64,
  _swigc__p_char,
  _swigc__p_float,
  _swigc__p_int,
  _swigc__p_long,
  _swigc__p_long_long,
  _swigc__p_p_LongArray,
  _swigc__p_p_char,
  _swigc__p_p_unsigned_long,
  _swigc__p_short,
  _swigc__p_signed___int64,
  _swigc__p_signed_char,
  _swigc__p_unsigned___int64,
  _swigc__p_unsigned_char,
  _swigc__p_unsigned_int,
  _swigc__p_unsigned_long,
  _swigc__p_unsigned_long_long,
  _swigc__p_unsigned_short,
};


/* -------- TYPE CONVERSION AND EQUIVALENCE RULES (END) -------- */




EnvInstanceData::EnvInstanceData(Napi::Env env, swig_module_info *swig_module) :
env(env), SWIG_NAPI_ObjectWrapCtor(nullptr), ctor(nullptr), swig_module(swig_module) {
  ctor = new Napi::FunctionReference*[swig_module->size + 1];
  for (size_t i = 0; i <= swig_module->size; i++) {
    ctor[i] = nullptr;
  }
}

EnvInstanceData::~EnvInstanceData() {
  for (size_t i = 0; i <= swig_module->size; i++) {
    if (ctor[i] != nullptr)
      delete ctor[i];
    ctor[i] = nullptr;
  }
  delete [] ctor;
  delete SWIG_NAPI_ObjectWrapCtor;
}

SWIGRUNTIME void
SWIG_NAPI_SetModule(Napi::Env env, swig_module_info *swig_module) {
  auto data = new EnvInstanceData(env, swig_module);
  env.SetInstanceData(data);
}

SWIGRUNTIME swig_module_info *
SWIG_NAPI_GetModule(Napi::Env env) {
  auto data = env.GetInstanceData<EnvInstanceData>();
  if (data == nullptr) return nullptr;
  return data->swig_module;
}

#define SWIG_GetModule(clientdata)                SWIG_NAPI_GetModule(clientdata)
#define SWIG_SetModule(clientdata, pointer)       SWIG_NAPI_SetModule(clientdata, pointer)
#define SWIG_INIT_CLIENT_DATA_TYPE                Napi::Env


/* -----------------------------------------------------------------------------
 * Type initialization:
 * This problem is tough by the requirement that no dynamic
 * memory is used. Also, since swig_type_info structures store pointers to
 * swig_cast_info structures and swig_cast_info structures store pointers back
 * to swig_type_info structures, we need some lookup code at initialization.
 * The idea is that swig generates all the structures that are needed.
 * The runtime then collects these partially filled structures.
 * The SWIG_InitializeModule function takes these initial arrays out of
 * swig_module, and does all the lookup, filling in the swig_module.types
 * array with the correct data and linking the correct swig_cast_info
 * structures together.
 *
 * The generated swig_type_info structures are assigned statically to an initial
 * array. We just loop through that array, and handle each type individually.
 * First we lookup if this type has been already loaded, and if so, use the
 * loaded structure instead of the generated one. Then we have to fill in the
 * cast linked list. The cast data is initially stored in something like a
 * two-dimensional array. Each row corresponds to a type (there are the same
 * number of rows as there are in the swig_type_initial array). Each entry in
 * a column is one of the swig_cast_info structures for that type.
 * The cast_initial array is actually an array of arrays, because each row has
 * a variable number of columns. So to actually build the cast linked list,
 * we find the array of casts associated with the type, and loop through it
 * adding the casts to the list. The one last trick we need to do is making
 * sure the type pointer in the swig_cast_info struct is correct.
 *
 * First off, we lookup the cast->type name to see if it is already loaded.
 * There are three cases to handle:
 *  1) If the cast->type has already been loaded AND the type we are adding
 *     casting info to has not been loaded (it is in this module), THEN we
 *     replace the cast->type pointer with the type pointer that has already
 *     been loaded.
 *  2) If BOTH types (the one we are adding casting info to, and the
 *     cast->type) are loaded, THEN the cast info has already been loaded by
 *     the previous module so we just ignore it.
 *  3) Finally, if cast->type has not already been loaded, then we add that
 *     swig_cast_info to the linked list (because the cast->type) pointer will
 *     be correct.
 * ----------------------------------------------------------------------------- */

#ifdef __cplusplus
extern "C" {
#if 0
} /* c-mode */
#endif
#endif

#if 0
#define SWIGRUNTIME_DEBUG
#endif

#ifndef SWIG_INIT_CLIENT_DATA_TYPE
#define SWIG_INIT_CLIENT_DATA_TYPE void *
#endif

SWIGRUNTIME void
SWIG_InitializeModule(SWIG_INIT_CLIENT_DATA_TYPE clientdata) {
  size_t i;
  swig_module_info *module_head, *iter;
  int init;

  /* check to see if the circular list has been setup, if not, set it up */
  if (swig_module.next==0) {
    /* Initialize the swig_module */
    swig_module.type_initial = swig_type_initial;
    swig_module.cast_initial = swig_cast_initial;
    swig_module.next = &swig_module;
    init = 1;
  } else {
    init = 0;
  }

  /* Try and load any already created modules */
  module_head = SWIG_GetModule(clientdata);
  if (!module_head) {
    /* This is the first module loaded for this interpreter */
    /* so set the swig module into the interpreter */
    SWIG_SetModule(clientdata, &swig_module);
  } else {
    /* the interpreter has loaded a SWIG module, but has it loaded this one? */
    iter=module_head;
    do {
      if (iter==&swig_module) {
        /* Our module is already in the list, so there's nothing more to do. */
        return;
      }
      iter=iter->next;
    } while (iter!= module_head);

    /* otherwise we must add our module into the list */
    swig_module.next = module_head->next;
    module_head->next = &swig_module;
  }

  /* When multiple interpreters are used, a module could have already been initialized in
     a different interpreter, but not yet have a pointer in this interpreter.
     In this case, we do not want to continue adding types... everything should be
     set up already */
  if (init == 0) return;

  /* Now work on filling in swig_module.types */
#ifdef SWIGRUNTIME_DEBUG
  printf("SWIG_InitializeModule: size %lu\n", (unsigned long)swig_module.size);
#endif
  for (i = 0; i < swig_module.size; ++i) {
    swig_type_info *type = 0;
    swig_type_info *ret;
    swig_cast_info *cast;

#ifdef SWIGRUNTIME_DEBUG
    printf("SWIG_InitializeModule: type %lu %s\n", (unsigned long)i, swig_module.type_initial[i]->name);
#endif

    /* if there is another module already loaded */
    if (swig_module.next != &swig_module) {
      type = SWIG_MangledTypeQueryModule(swig_module.next, &swig_module, swig_module.type_initial[i]->name);
    }
    if (type) {
      /* Overwrite clientdata field */
#ifdef SWIGRUNTIME_DEBUG
      printf("SWIG_InitializeModule: found type %s\n", type->name);
#endif
      if (swig_module.type_initial[i]->clientdata) {
	type->clientdata = swig_module.type_initial[i]->clientdata;
#ifdef SWIGRUNTIME_DEBUG
      printf("SWIG_InitializeModule: found and overwrite type %s \n", type->name);
#endif
      }
    } else {
      type = swig_module.type_initial[i];
    }

    /* Insert casting types */
    cast = swig_module.cast_initial[i];
    while (cast->type) {

      /* Don't need to add information already in the list */
      ret = 0;
#ifdef SWIGRUNTIME_DEBUG
      printf("SWIG_InitializeModule: look cast %s\n", cast->type->name);
#endif
      if (swig_module.next != &swig_module) {
        ret = SWIG_MangledTypeQueryModule(swig_module.next, &swig_module, cast->type->name);
#ifdef SWIGRUNTIME_DEBUG
	if (ret) printf("SWIG_InitializeModule: found cast %s\n", ret->name);
#endif
      }
      if (ret) {
	if (type == swig_module.type_initial[i]) {
#ifdef SWIGRUNTIME_DEBUG
	  printf("SWIG_InitializeModule: skip old type %s\n", ret->name);
#endif
	  cast->type = ret;
	  ret = 0;
	} else {
	  /* Check for casting already in the list */
	  swig_cast_info *ocast = SWIG_TypeCheck(ret->name, type);
#ifdef SWIGRUNTIME_DEBUG
	  if (ocast) printf("SWIG_InitializeModule: skip old cast %s\n", ret->name);
#endif
	  if (!ocast) ret = 0;
	}
      }

      if (!ret) {
#ifdef SWIGRUNTIME_DEBUG
	printf("SWIG_InitializeModule: adding cast %s\n", cast->type->name);
#endif
        if (type->cast) {
          type->cast->prev = cast;
          cast->next = type->cast;
        }
        type->cast = cast;
      }
      cast++;
    }
    /* Set entry in modules->types array equal to the type */
    swig_module.types[i] = type;
  }
  swig_module.types[i] = 0;

#ifdef SWIGRUNTIME_DEBUG
  printf("**** SWIG_InitializeModule: Cast List ******\n");
  for (i = 0; i < swig_module.size; ++i) {
    int j = 0;
    swig_cast_info *cast = swig_module.cast_initial[i];
    printf("SWIG_InitializeModule: type %lu %s\n", (unsigned long)i, swig_module.type_initial[i]->name);
    while (cast->type) {
      printf("SWIG_InitializeModule: cast type %s\n", cast->type->name);
      cast++;
      ++j;
    }
  printf("---- Total casts: %d\n",j);
  }
  printf("**** SWIG_InitializeModule: Cast List ******\n");
#endif
}

/* This function will propagate the clientdata field of type to
* any new swig_type_info structures that have been added into the list
* of equivalent types.  It is like calling
* SWIG_TypeClientData(type, clientdata) a second time.
*/
SWIGRUNTIME void
SWIG_PropagateClientData(void) {
  size_t i;
  swig_cast_info *equiv;
  static int init_run = 0;

  if (init_run) return;
  init_run = 1;

  for (i = 0; i < swig_module.size; i++) {
    if (swig_module.types[i]->clientdata) {
      equiv = swig_module.types[i]->cast;
      while (equiv) {
        if (!equiv->converter) {
          if (equiv->type && !equiv->type->clientdata)
            SWIG_TypeClientData(equiv->type, swig_module.types[i]->clientdata);
        }
        equiv = equiv->next;
      }
    }
  }
}

#ifdef __cplusplus
#if 0
{ /* c-mode */
#endif
}
#endif


Napi::Object Init(Napi::Env env, Napi::Object exports) {
  SWIG_InitializeModule(env);



extern void CreateTSFN(const Napi::CallbackInfo &info);
extern void FreeTSFN(const Napi::CallbackInfo &info);


do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("CreateTSFN", CreateTSFN);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);

do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("FreeTSFN", FreeTSFN);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
	pd
  }));
} while (0);


  Napi::Function SWIG_NAPI_ObjectWrap_ctor = SWIG_NAPI_ObjectWrap_inst::GetClass(env);
  Napi::FunctionReference *SWIG_NAPI_ObjectWrap_ctor_ref = new Napi::FunctionReference();
  *SWIG_NAPI_ObjectWrap_ctor_ref = Napi::Persistent(SWIG_NAPI_ObjectWrap_ctor);
  env.GetInstanceData<EnvInstanceData>()->SWIG_NAPI_ObjectWrapCtor = SWIG_NAPI_ObjectWrap_ctor_ref;

  /* create objects for namespaces */
  

  /* register classes */
  /* Class: CharPointer (_exports_CharPointer) */
// jsnapi_registerclass
Napi::Function _exports_CharPointer_ctor = _exports_CharPointer_inst::GetClass(env);
exports.Set("CharPointer", _exports_CharPointer_ctor);
if (SWIGTYPE_p_CharPointer->clientdata == nullptr) {
  SWIGTYPE_p_CharPointer->clientdata = new size_t(0);
}
Napi::FunctionReference *_exports_CharPointer_ctor_ref = new Napi::FunctionReference();
*_exports_CharPointer_ctor_ref = Napi::Persistent(_exports_CharPointer_ctor);
env.GetInstanceData<EnvInstanceData>()->ctor[0] = _exports_CharPointer_ctor_ref;
/* Class: IntPointer (_exports_IntPointer) */
// jsnapi_registerclass
Napi::Function _exports_IntPointer_ctor = _exports_IntPointer_inst::GetClass(env);
exports.Set("IntPointer", _exports_IntPointer_ctor);
if (SWIGTYPE_p_IntPointer->clientdata == nullptr) {
  SWIGTYPE_p_IntPointer->clientdata = new size_t(1);
}
Napi::FunctionReference *_exports_IntPointer_ctor_ref = new Napi::FunctionReference();
*_exports_IntPointer_ctor_ref = Napi::Persistent(_exports_IntPointer_ctor);
env.GetInstanceData<EnvInstanceData>()->ctor[1] = _exports_IntPointer_ctor_ref;
/* Class: LongArray (_exports_LongArray) */
// jsnapi_registerclass
Napi::Function _exports_LongArray_ctor = _exports_LongArray_inst::GetClass(env);
exports.Set("LongArray", _exports_LongArray_ctor);
if (SWIGTYPE_p_LongArray->clientdata == nullptr) {
  SWIGTYPE_p_LongArray->clientdata = new size_t(2);
}
Napi::FunctionReference *_exports_LongArray_ctor_ref = new Napi::FunctionReference();
*_exports_LongArray_ctor_ref = Napi::Persistent(_exports_LongArray_ctor);
env.GetInstanceData<EnvInstanceData>()->ctor[2] = _exports_LongArray_ctor_ref;
/* Class: CharArray (_exports_CharArray) */
// jsnapi_registerclass
Napi::Function _exports_CharArray_ctor = _exports_CharArray_inst::GetClass(env);
exports.Set("CharArray", _exports_CharArray_ctor);
if (SWIGTYPE_p_CharArray->clientdata == nullptr) {
  SWIGTYPE_p_CharArray->clientdata = new size_t(3);
}
Napi::FunctionReference *_exports_CharArray_ctor_ref = new Napi::FunctionReference();
*_exports_CharArray_ctor_ref = Napi::Persistent(_exports_CharArray_ctor);
env.GetInstanceData<EnvInstanceData>()->ctor[3] = _exports_CharArray_ctor_ref;
/* Class: LinMessageInfo (_exports_LinMessageInfo) */
// jsnapi_registerclass
Napi::Function _exports_LinMessageInfo_ctor = _exports_LinMessageInfo_inst::GetClass(env);
exports.Set("LinMessageInfo", _exports_LinMessageInfo_ctor);
if (SWIGTYPE_p_LinMessageInfo->clientdata == nullptr) {
  SWIGTYPE_p_LinMessageInfo->clientdata = new size_t(4);
}
Napi::FunctionReference *_exports_LinMessageInfo_ctor_ref = new Napi::FunctionReference();
*_exports_LinMessageInfo_ctor_ref = Napi::Persistent(_exports_LinMessageInfo_ctor);
env.GetInstanceData<EnvInstanceData>()->ctor[4] = _exports_LinMessageInfo_ctor_ref;


  /* enable inheritance */
  
Napi::Value jsObjectValue, jsSetProtoValue;
Napi::Object jsObject;
Napi::Function setProto;
NAPI_CHECK_RESULT(env.Global().Get("Object"), jsObjectValue);
NAPI_CHECK_RESULT(jsObjectValue.ToObject(), jsObject);
NAPI_CHECK_RESULT(jsObject.Get("setPrototypeOf"), jsSetProtoValue);
setProto = jsSetProtoValue.As<Napi::Function>();



  /* setup inheritances */
  
// Inheritance for _exports_CharPointer (CharPointer) <- SWIG_NAPI_ObjectWrap
// jsnapi_setup_inheritance
do {
  Napi::Value protoBase, protoSub;
  NAPI_CHECK_RESULT(_exports_CharPointer_ctor.Get("prototype"), protoSub);
  NAPI_CHECK_RESULT(SWIG_NAPI_ObjectWrap_ctor.Get("prototype"), protoBase);
  NAPI_CHECK_MAYBE(setProto.Call({
    _exports_CharPointer_ctor, SWIG_NAPI_ObjectWrap_ctor
  }));
  NAPI_CHECK_MAYBE(setProto.Call({
    protoSub, protoBase
  }));
} while (0);


// Inheritance for _exports_IntPointer (IntPointer) <- SWIG_NAPI_ObjectWrap
// jsnapi_setup_inheritance
do {
  Napi::Value protoBase, protoSub;
  NAPI_CHECK_RESULT(_exports_IntPointer_ctor.Get("prototype"), protoSub);
  NAPI_CHECK_RESULT(SWIG_NAPI_ObjectWrap_ctor.Get("prototype"), protoBase);
  NAPI_CHECK_MAYBE(setProto.Call({
    _exports_IntPointer_ctor, SWIG_NAPI_ObjectWrap_ctor
  }));
  NAPI_CHECK_MAYBE(setProto.Call({
    protoSub, protoBase
  }));
} while (0);


// Inheritance for _exports_LongArray (LongArray) <- SWIG_NAPI_ObjectWrap
// jsnapi_setup_inheritance
do {
  Napi::Value protoBase, protoSub;
  NAPI_CHECK_RESULT(_exports_LongArray_ctor.Get("prototype"), protoSub);
  NAPI_CHECK_RESULT(SWIG_NAPI_ObjectWrap_ctor.Get("prototype"), protoBase);
  NAPI_CHECK_MAYBE(setProto.Call({
    _exports_LongArray_ctor, SWIG_NAPI_ObjectWrap_ctor
  }));
  NAPI_CHECK_MAYBE(setProto.Call({
    protoSub, protoBase
  }));
} while (0);


// Inheritance for _exports_CharArray (CharArray) <- SWIG_NAPI_ObjectWrap
// jsnapi_setup_inheritance
do {
  Napi::Value protoBase, protoSub;
  NAPI_CHECK_RESULT(_exports_CharArray_ctor.Get("prototype"), protoSub);
  NAPI_CHECK_RESULT(SWIG_NAPI_ObjectWrap_ctor.Get("prototype"), protoBase);
  NAPI_CHECK_MAYBE(setProto.Call({
    _exports_CharArray_ctor, SWIG_NAPI_ObjectWrap_ctor
  }));
  NAPI_CHECK_MAYBE(setProto.Call({
    protoSub, protoBase
  }));
} while (0);


// Inheritance for _exports_LinMessageInfo (LinMessageInfo) <- SWIG_NAPI_ObjectWrap
// jsnapi_setup_inheritance
do {
  Napi::Value protoBase, protoSub;
  NAPI_CHECK_RESULT(_exports_LinMessageInfo_ctor.Get("prototype"), protoSub);
  NAPI_CHECK_RESULT(SWIG_NAPI_ObjectWrap_ctor.Get("prototype"), protoBase);
  NAPI_CHECK_MAYBE(setProto.Call({
    _exports_LinMessageInfo_ctor, SWIG_NAPI_ObjectWrap_ctor
  }));
  NAPI_CHECK_MAYBE(setProto.Call({
    protoSub, protoBase
  }));
} while (0);



  /* create and register namespace objects */
  // jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_linOK_get, JS_veto_set_variable>("linOK");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_linERR_NOMSG_get, JS_veto_set_variable>("linERR_NOMSG");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_linERR_NOTRUNNING_get, JS_veto_set_variable>("linERR_NOTRUNNING");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_linERR_RUNNING_get, JS_veto_set_variable>("linERR_RUNNING");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_linERR_MASTERONLY_get, JS_veto_set_variable>("linERR_MASTERONLY");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_linERR_SLAVEONLY_get, JS_veto_set_variable>("linERR_SLAVEONLY");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_linERR_PARAM_get, JS_veto_set_variable>("linERR_PARAM");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_linERR_NOTFOUND_get, JS_veto_set_variable>("linERR_NOTFOUND");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_linERR_NOMEM_get, JS_veto_set_variable>("linERR_NOMEM");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_linERR_NOCHANNELS_get, JS_veto_set_variable>("linERR_NOCHANNELS");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_linERR_TIMEOUT_get, JS_veto_set_variable>("linERR_TIMEOUT");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_linERR_NOTINITIALIZED_get, JS_veto_set_variable>("linERR_NOTINITIALIZED");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_linERR_NOHANDLES_get, JS_veto_set_variable>("linERR_NOHANDLES");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_linERR_INVHANDLE_get, JS_veto_set_variable>("linERR_INVHANDLE");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_linERR_CANERROR_get, JS_veto_set_variable>("linERR_CANERROR");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_linERR_ERRRESP_get, JS_veto_set_variable>("linERR_ERRRESP");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_linERR_WRONGRESP_get, JS_veto_set_variable>("linERR_WRONGRESP");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_linERR_DRIVER_get, JS_veto_set_variable>("linERR_DRIVER");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_linERR_DRIVERFAILED_get, JS_veto_set_variable>("linERR_DRIVERFAILED");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_linERR_NOCARD_get, JS_veto_set_variable>("linERR_NOCARD");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_linERR_LICENSE_get, JS_veto_set_variable>("linERR_LICENSE");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_linERR_INTERNAL_get, JS_veto_set_variable>("linERR_INTERNAL");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_linERR_NO_ACCESS_get, JS_veto_set_variable>("linERR_NO_ACCESS");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_linERR_VERSION_get, JS_veto_set_variable>("linERR_VERSION");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_linERR_NO_REF_POWER_get, JS_veto_set_variable>("linERR_NO_REF_POWER");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_linERR_NOT_IMPLEMENTED_get, JS_veto_set_variable>("linERR_NOT_IMPLEMENTED");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_TX_get, JS_veto_set_variable>("LIN_TX");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_RX_get, JS_veto_set_variable>("LIN_RX");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_WAKEUP_FRAME_get, JS_veto_set_variable>("LIN_WAKEUP_FRAME");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_NODATA_get, JS_veto_set_variable>("LIN_NODATA");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_CSUM_ERROR_get, JS_veto_set_variable>("LIN_CSUM_ERROR");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_PARITY_ERROR_get, JS_veto_set_variable>("LIN_PARITY_ERROR");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_SYNCH_ERROR_get, JS_veto_set_variable>("LIN_SYNCH_ERROR");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_BIT_ERROR_get, JS_veto_set_variable>("LIN_BIT_ERROR");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_linCHANNELDATA_CARD_FIRMWARE_REV_get, JS_veto_set_variable>("linCHANNELDATA_CARD_FIRMWARE_REV");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("linInitializeLibrary", _wrap_linInitializeLibrary);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("linUnloadLibrary", _wrap_linUnloadLibrary);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("linGetTransceiverData", _wrap_linGetTransceiverData);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("linOpenChannel", _wrap_linOpenChannel);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_MASTER_get, JS_veto_set_variable>("LIN_MASTER");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_SLAVE_get, JS_veto_set_variable>("LIN_SLAVE");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("linClose", _wrap_linClose);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("linGetVersion", _wrap_linGetVersion);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("linGetFirmwareVersion", _wrap_linGetFirmwareVersion);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("linGetChannelData", _wrap_linGetChannelData);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("linSetBitrate", _wrap_linSetBitrate);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("linBusOn", _wrap_linBusOn);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("linBusOff", _wrap_linBusOff);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("linReadTimer", _wrap_linReadTimer);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("linWriteMessage", _wrap_linWriteMessage);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("linRequestMessage", _wrap_linRequestMessage);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("linReadMessage", _wrap_linReadMessage);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("linReadMessageWait", _wrap_linReadMessageWait);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("linUpdateMessage", _wrap_linUpdateMessage);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("linSetupIllegalMessage", _wrap_linSetupIllegalMessage);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_MSG_DISTURB_CSUM_get, JS_veto_set_variable>("LIN_MSG_DISTURB_CSUM");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_MSG_DISTURB_PARITY_get, JS_veto_set_variable>("LIN_MSG_DISTURB_PARITY");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_MSG_USE_STANDARD_PARITY_get, JS_veto_set_variable>("LIN_MSG_USE_STANDARD_PARITY");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_MSG_USE_ENHANCED_PARITY_get, JS_veto_set_variable>("LIN_MSG_USE_ENHANCED_PARITY");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("linSetupLIN", _wrap_linSetupLIN);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_ENHANCED_CHECKSUM_get, JS_veto_set_variable>("LIN_ENHANCED_CHECKSUM");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_VARIABLE_DLC_get, JS_veto_set_variable>("LIN_VARIABLE_DLC");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("linWriteWakeup", _wrap_linWriteWakeup);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("linClearMessage", _wrap_linClearMessage);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("linWriteSync", _wrap_linWriteSync);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("linGetCanHandle", _wrap_linGetCanHandle);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LoadDll", _wrap_LoadDll);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);


  return exports;
  goto fail;
fail:
  return Napi::Object();
}

NODE_API_MODULE(xmlpp, Init)
