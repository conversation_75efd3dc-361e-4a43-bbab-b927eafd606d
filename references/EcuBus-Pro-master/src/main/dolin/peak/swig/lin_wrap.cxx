/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (https://www.swig.org).
 * Version 4.2.1
 *
 * Do not make changes to this file unless you know what you are doing - modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */


#define SWIG_VERSION 0x040201
#define SWIGJAVASCRIPT
/* -----------------------------------------------------------------------------
 *  This section contains generic SWIG labels for method/variable
 *  declarations/attributes, and other compiler dependent labels.
 * ----------------------------------------------------------------------------- */

/* template workaround for compilers that cannot correctly implement the C++ standard */
#ifndef SWIGTEMPLATEDISAMBIGUATOR
# if defined(__SUNPRO_CC) && (__SUNPRO_CC <= 0x560)
#  define SWIGTEMPLATEDISAMBIGUATOR template
# elif defined(__HP_aCC)
/* Needed even with `aCC -AA' when `aCC -V' reports HP ANSI C++ B3910B A.03.55 */
/* If we find a maximum version that requires this, the test would be __HP_aCC <= 35500 for A.03.55 */
#  define SWIGTEMPLATEDISAMBIGUATOR template
# else
#  define SWIGTEMPLATEDISAMBIGUATOR
# endif
#endif

/* inline attribute */
#ifndef SWIGINLINE
# if defined(__cplusplus) || (defined(__GNUC__) && !defined(__STRICT_ANSI__))
#   define SWIGINLINE inline
# else
#   define SWIGINLINE
# endif
#endif

/* attribute recognised by some compilers to avoid 'unused' warnings */
#ifndef SWIGUNUSED
# if defined(__GNUC__)
#   if !(defined(__cplusplus)) || (__GNUC__ > 3 || (__GNUC__ == 3 && __GNUC_MINOR__ >= 4))
#     define SWIGUNUSED __attribute__ ((__unused__))
#   else
#     define SWIGUNUSED
#   endif
# elif defined(__ICC)
#   define SWIGUNUSED __attribute__ ((__unused__))
# else
#   define SWIGUNUSED
# endif
#endif

#ifndef SWIG_MSC_UNSUPPRESS_4505
# if defined(_MSC_VER)
#   pragma warning(disable : 4505) /* unreferenced local function has been removed */
# endif
#endif

#ifndef SWIGUNUSEDPARM
# ifdef __cplusplus
#   define SWIGUNUSEDPARM(p)
# else
#   define SWIGUNUSEDPARM(p) p SWIGUNUSED
# endif
#endif

/* internal SWIG method */
#ifndef SWIGINTERN
# define SWIGINTERN static SWIGUNUSED
#endif

/* internal inline SWIG method */
#ifndef SWIGINTERNINLINE
# define SWIGINTERNINLINE SWIGINTERN SWIGINLINE
#endif

/* exporting methods */
#if defined(__GNUC__)
#  if (__GNUC__ >= 4) || (__GNUC__ == 3 && __GNUC_MINOR__ >= 4)
#    ifndef GCC_HASCLASSVISIBILITY
#      define GCC_HASCLASSVISIBILITY
#    endif
#  endif
#endif

#ifndef SWIGEXPORT
# if defined(_WIN32) || defined(__WIN32__) || defined(__CYGWIN__)
#   if defined(STATIC_LINKED)
#     define SWIGEXPORT
#   else
#     define SWIGEXPORT __declspec(dllexport)
#   endif
# else
#   if defined(__GNUC__) && defined(GCC_HASCLASSVISIBILITY)
#     define SWIGEXPORT __attribute__ ((visibility("default")))
#   else
#     define SWIGEXPORT
#   endif
# endif
#endif

/* calling conventions for Windows */
#ifndef SWIGSTDCALL
# if defined(_WIN32) || defined(__WIN32__) || defined(__CYGWIN__)
#   define SWIGSTDCALL __stdcall
# else
#   define SWIGSTDCALL
# endif
#endif

/* Deal with Microsoft's attempt at deprecating C standard runtime functions */
#if !defined(SWIG_NO_CRT_SECURE_NO_DEPRECATE) && defined(_MSC_VER) && !defined(_CRT_SECURE_NO_DEPRECATE)
# define _CRT_SECURE_NO_DEPRECATE
#endif

/* Deal with Microsoft's attempt at deprecating methods in the standard C++ library */
#if !defined(SWIG_NO_SCL_SECURE_NO_DEPRECATE) && defined(_MSC_VER) && !defined(_SCL_SECURE_NO_DEPRECATE)
# define _SCL_SECURE_NO_DEPRECATE
#endif

/* Deal with Apple's deprecated 'AssertMacros.h' from Carbon-framework */
#if defined(__APPLE__) && !defined(__ASSERT_MACROS_DEFINE_VERSIONS_WITHOUT_UNDERSCORES)
# define __ASSERT_MACROS_DEFINE_VERSIONS_WITHOUT_UNDERSCORES 0
#endif

/* Intel's compiler complains if a variable which was never initialised is
 * cast to void, which is a common idiom which we use to indicate that we
 * are aware a variable isn't used.  So we just silence that warning.
 * See: https://github.com/swig/swig/issues/192 for more discussion.
 */
#ifdef __INTEL_COMPILER
# pragma warning disable 592
#endif

#if defined(__cplusplus) && __cplusplus >=201103L
# define SWIG_NULLPTR nullptr
#else
# define SWIG_NULLPTR NULL
#endif 

/* -----------------------------------------------------------------------------
 * swigcompat.swg
 *
 * Macros to provide support compatibility with older C and C++ standards.
 * ----------------------------------------------------------------------------- */

/* C99 and C++11 should provide snprintf, but define SWIG_NO_SNPRINTF
 * if you're missing it.
 */
#if ((defined __STDC_VERSION__ && __STDC_VERSION__ >= 199901L) || \
     (defined __cplusplus && __cplusplus >= 201103L) || \
     defined SWIG_HAVE_SNPRINTF) && \
    !defined SWIG_NO_SNPRINTF
# define SWIG_snprintf(O,S,F,A) snprintf(O,S,F,A)
# define SWIG_snprintf2(O,S,F,A,B) snprintf(O,S,F,A,B)
#else
/* Fallback versions ignore the buffer size, but most of our uses either have a
 * fixed maximum possible size or dynamically allocate a buffer that's large
 * enough.
 */
# define SWIG_snprintf(O,S,F,A) sprintf(O,F,A)
# define SWIG_snprintf2(O,S,F,A,B) sprintf(O,F,A,B)
#endif


#define SWIG_FromCharPtrAndSize(cptr, size) SWIG_Env_FromCharPtrAndSize(env, cptr, size)
#define SWIG_FromCharPtr(cptr)              SWIG_Env_FromCharPtrAndSize(env, cptr, strlen(cptr))


#define SWIG_NAPI_FROM_DECL_ARGS(arg1)              (Napi::Env env, arg1)
#define SWIG_NAPI_FROM_CALL_ARGS(arg1)              (env, arg1)



#define SWIG_exception_fail(code, msg) do { SWIG_Error(code, msg); SWIG_fail; } while(0) 

#define SWIG_contract_assert(expr, msg) do { if (!(expr)) { SWIG_Error(SWIG_RuntimeError, msg); SWIG_fail; } } while (0) 



#if defined(_CPPUNWIND) || defined(__EXCEPTIONS)
#define NAPI_CPP_EXCEPTIONS
#else
#define NAPI_DISABLE_CPP_EXCEPTIONS
#define NODE_ADDON_API_ENABLE_MAYBE
#endif

// This gives us
// Branch Node.js v10.x - from v10.20.0
// Branch Node.js v12.x - from v12.17.0
// Everything from Node.js v14.0.0 on
// Our limiting feature is napi_set_instance_data
#ifndef NAPI_VERSION
#define NAPI_VERSION 6
#elif NAPI_VERSION < 6
#error NAPI_VERSION 6 is the minimum supported target (Node.js >=14, >=12.17, >=10.20)
#endif
#include <napi.h>

#include <errno.h>
#include <limits.h>
#include <stdlib.h>
#include <assert.h>
#include <map>

/* -----------------------------------------------------------------------------
 * swigrun.swg
 *
 * This file contains generic C API SWIG runtime support for pointer
 * type checking.
 * ----------------------------------------------------------------------------- */

/* This should only be incremented when either the layout of swig_type_info changes,
   or for whatever reason, the runtime changes incompatibly */
#define SWIG_RUNTIME_VERSION "4"

/* define SWIG_TYPE_TABLE_NAME as "SWIG_TYPE_TABLE" */
#ifdef SWIG_TYPE_TABLE
# define SWIG_QUOTE_STRING(x) #x
# define SWIG_EXPAND_AND_QUOTE_STRING(x) SWIG_QUOTE_STRING(x)
# define SWIG_TYPE_TABLE_NAME SWIG_EXPAND_AND_QUOTE_STRING(SWIG_TYPE_TABLE)
#else
# define SWIG_TYPE_TABLE_NAME
#endif

/*
  You can use the SWIGRUNTIME and SWIGRUNTIMEINLINE macros for
  creating a static or dynamic library from the SWIG runtime code.
  In 99.9% of the cases, SWIG just needs to declare them as 'static'.

  But only do this if strictly necessary, ie, if you have problems
  with your compiler or suchlike.
*/

#ifndef SWIGRUNTIME
# define SWIGRUNTIME SWIGINTERN
#endif

#ifndef SWIGRUNTIMEINLINE
# define SWIGRUNTIMEINLINE SWIGRUNTIME SWIGINLINE
#endif

/*  Generic buffer size */
#ifndef SWIG_BUFFER_SIZE
# define SWIG_BUFFER_SIZE 1024
#endif

/* Flags for pointer conversions */
#define SWIG_POINTER_DISOWN        0x1
#define SWIG_CAST_NEW_MEMORY       0x2
#define SWIG_POINTER_NO_NULL       0x4
#define SWIG_POINTER_CLEAR         0x8
#define SWIG_POINTER_RELEASE       (SWIG_POINTER_CLEAR | SWIG_POINTER_DISOWN)

/* Flags for new pointer objects */
#define SWIG_POINTER_OWN           0x1


/*
   Flags/methods for returning states.

   The SWIG conversion methods, as ConvertPtr, return an integer
   that tells if the conversion was successful or not. And if not,
   an error code can be returned (see swigerrors.swg for the codes).

   Use the following macros/flags to set or process the returning
   states.

   In old versions of SWIG, code such as the following was usually written:

     if (SWIG_ConvertPtr(obj,vptr,ty.flags) != -1) {
       // success code
     } else {
       //fail code
     }

   Now you can be more explicit:

    int res = SWIG_ConvertPtr(obj,vptr,ty.flags);
    if (SWIG_IsOK(res)) {
      // success code
    } else {
      // fail code
    }

   which is the same really, but now you can also do

    Type *ptr;
    int res = SWIG_ConvertPtr(obj,(void **)(&ptr),ty.flags);
    if (SWIG_IsOK(res)) {
      // success code
      if (SWIG_IsNewObj(res) {
        ...
	delete *ptr;
      } else {
        ...
      }
    } else {
      // fail code
    }

   I.e., now SWIG_ConvertPtr can return new objects and you can
   identify the case and take care of the deallocation. Of course that
   also requires SWIG_ConvertPtr to return new result values, such as

      int SWIG_ConvertPtr(obj, ptr,...) {
        if (<obj is ok>) {
          if (<need new object>) {
            *ptr = <ptr to new allocated object>;
            return SWIG_NEWOBJ;
          } else {
            *ptr = <ptr to old object>;
            return SWIG_OLDOBJ;
          }
        } else {
          return SWIG_BADOBJ;
        }
      }

   Of course, returning the plain '0(success)/-1(fail)' still works, but you can be
   more explicit by returning SWIG_BADOBJ, SWIG_ERROR or any of the
   SWIG errors code.

   Finally, if the SWIG_CASTRANK_MODE is enabled, the result code
   allows returning the 'cast rank', for example, if you have this

       int food(double)
       int fooi(int);

   and you call

      food(1)   // cast rank '1'  (1 -> 1.0)
      fooi(1)   // cast rank '0'

   just use the SWIG_AddCast()/SWIG_CheckState()
*/

#define SWIG_OK                    (0)
/* Runtime errors are < 0 */
#define SWIG_ERROR                 (-1)
/* Errors in range -1 to -99 are in swigerrors.swg (errors for all languages including those not using the runtime) */
/* Errors in range -100 to -199 are language specific errors defined in *errors.swg */
/* Errors < -200 are generic runtime specific errors */
#define SWIG_ERROR_RELEASE_NOT_OWNED (-200)

#define SWIG_IsOK(r)               (r >= 0)
#define SWIG_ArgError(r)           ((r != SWIG_ERROR) ? r : SWIG_TypeError)

/* The CastRankLimit says how many bits are used for the cast rank */
#define SWIG_CASTRANKLIMIT         (1 << 8)
/* The NewMask denotes the object was created (using new/malloc) */
#define SWIG_NEWOBJMASK            (SWIG_CASTRANKLIMIT  << 1)
/* The TmpMask is for in/out typemaps that use temporary objects */
#define SWIG_TMPOBJMASK            (SWIG_NEWOBJMASK << 1)
/* Simple returning values */
#define SWIG_BADOBJ                (SWIG_ERROR)
#define SWIG_OLDOBJ                (SWIG_OK)
#define SWIG_NEWOBJ                (SWIG_OK | SWIG_NEWOBJMASK)
#define SWIG_TMPOBJ                (SWIG_OK | SWIG_TMPOBJMASK)
/* Check, add and del object mask methods */
#define SWIG_AddNewMask(r)         (SWIG_IsOK(r) ? (r | SWIG_NEWOBJMASK) : r)
#define SWIG_DelNewMask(r)         (SWIG_IsOK(r) ? (r & ~SWIG_NEWOBJMASK) : r)
#define SWIG_IsNewObj(r)           (SWIG_IsOK(r) && (r & SWIG_NEWOBJMASK))
#define SWIG_AddTmpMask(r)         (SWIG_IsOK(r) ? (r | SWIG_TMPOBJMASK) : r)
#define SWIG_DelTmpMask(r)         (SWIG_IsOK(r) ? (r & ~SWIG_TMPOBJMASK) : r)
#define SWIG_IsTmpObj(r)           (SWIG_IsOK(r) && (r & SWIG_TMPOBJMASK))

/* Cast-Rank Mode */
#if defined(SWIG_CASTRANK_MODE)
#  ifndef SWIG_TypeRank
#    define SWIG_TypeRank             unsigned long
#  endif
#  ifndef SWIG_MAXCASTRANK            /* Default cast allowed */
#    define SWIG_MAXCASTRANK          (2)
#  endif
#  define SWIG_CASTRANKMASK          ((SWIG_CASTRANKLIMIT) -1)
#  define SWIG_CastRank(r)           (r & SWIG_CASTRANKMASK)
SWIGINTERNINLINE int SWIG_AddCast(int r) {
  return SWIG_IsOK(r) ? ((SWIG_CastRank(r) < SWIG_MAXCASTRANK) ? (r + 1) : SWIG_ERROR) : r;
}
SWIGINTERNINLINE int SWIG_CheckState(int r) {
  return SWIG_IsOK(r) ? SWIG_CastRank(r) + 1 : 0;
}
#else /* no cast-rank mode */
#  define SWIG_AddCast(r) (r)
#  define SWIG_CheckState(r) (SWIG_IsOK(r) ? 1 : 0)
#endif


#include <string.h>

#ifdef __cplusplus
extern "C" {
#endif

typedef void *(*swig_converter_func)(void *, int *);
typedef struct swig_type_info *(*swig_dycast_func)(void **);

/* Structure to store information on one type */
typedef struct swig_type_info {
  const char             *name;			/* mangled name of this type */
  const char             *str;			/* human readable name of this type */
  swig_dycast_func        dcast;		/* dynamic cast function down a hierarchy */
  struct swig_cast_info  *cast;			/* linked list of types that can cast into this type */
  void                   *clientdata;		/* language specific type data */
  int                    owndata;		/* flag if the structure owns the clientdata */
} swig_type_info;

/* Structure to store a type and conversion function used for casting */
typedef struct swig_cast_info {
  swig_type_info         *type;			/* pointer to type that is equivalent to this type */
  swig_converter_func     converter;		/* function to cast the void pointers */
  struct swig_cast_info  *next;			/* pointer to next cast in linked list */
  struct swig_cast_info  *prev;			/* pointer to the previous cast */
} swig_cast_info;

/* Structure used to store module information
 * Each module generates one structure like this, and the runtime collects
 * all of these structures and stores them in a circularly linked list.*/
typedef struct swig_module_info {
  swig_type_info         **types;		/* Array of pointers to swig_type_info structures that are in this module */
  size_t                 size;		        /* Number of types in this module */
  struct swig_module_info *next;		/* Pointer to next element in circularly linked list */
  swig_type_info         **type_initial;	/* Array of initially generated type structures */
  swig_cast_info         **cast_initial;	/* Array of initially generated casting structures */
  void                    *clientdata;		/* Language specific module data */
} swig_module_info;

/*
  Compare two type names skipping the space characters, therefore
  "char*" == "char *" and "Class<int>" == "Class<int >", etc.

  Return 0 when the two name types are equivalent, as in
  strncmp, but skipping ' '.
*/
SWIGRUNTIME int
SWIG_TypeNameComp(const char *f1, const char *l1,
		  const char *f2, const char *l2) {
  for (;(f1 != l1) && (f2 != l2); ++f1, ++f2) {
    while ((*f1 == ' ') && (f1 != l1)) ++f1;
    while ((*f2 == ' ') && (f2 != l2)) ++f2;
    if (*f1 != *f2) return (*f1 > *f2) ? 1 : -1;
  }
  return (int)((l1 - f1) - (l2 - f2));
}

/*
  Check type equivalence in a name list like <name1>|<name2>|...
  Return 0 if equal, -1 if nb < tb, 1 if nb > tb
*/
SWIGRUNTIME int
SWIG_TypeCmp(const char *nb, const char *tb) {
  int equiv = 1;
  const char* te = tb + strlen(tb);
  const char* ne = nb;
  while (equiv != 0 && *ne) {
    for (nb = ne; *ne; ++ne) {
      if (*ne == '|') break;
    }
    equiv = SWIG_TypeNameComp(nb, ne, tb, te);
    if (*ne) ++ne;
  }
  return equiv;
}

/*
  Check type equivalence in a name list like <name1>|<name2>|...
  Return 0 if not equal, 1 if equal
*/
SWIGRUNTIME int
SWIG_TypeEquiv(const char *nb, const char *tb) {
  return SWIG_TypeCmp(nb, tb) == 0 ? 1 : 0;
}

/*
  Check the typename
*/
SWIGRUNTIME swig_cast_info *
SWIG_TypeCheck(const char *c, swig_type_info *ty) {
  if (ty) {
    swig_cast_info *iter = ty->cast;
    while (iter) {
      if (strcmp(iter->type->name, c) == 0) {
        if (iter == ty->cast)
          return iter;
        /* Move iter to the top of the linked list */
        iter->prev->next = iter->next;
        if (iter->next)
          iter->next->prev = iter->prev;
        iter->next = ty->cast;
        iter->prev = 0;
        if (ty->cast) ty->cast->prev = iter;
        ty->cast = iter;
        return iter;
      }
      iter = iter->next;
    }
  }
  return 0;
}

/*
  Identical to SWIG_TypeCheck, except strcmp is replaced with a pointer comparison
*/
SWIGRUNTIME swig_cast_info *
SWIG_TypeCheckStruct(const swig_type_info *from, swig_type_info *ty) {
  if (ty) {
    swig_cast_info *iter = ty->cast;
    while (iter) {
      if (iter->type == from) {
        if (iter == ty->cast)
          return iter;
        /* Move iter to the top of the linked list */
        iter->prev->next = iter->next;
        if (iter->next)
          iter->next->prev = iter->prev;
        iter->next = ty->cast;
        iter->prev = 0;
        if (ty->cast) ty->cast->prev = iter;
        ty->cast = iter;
        return iter;
      }
      iter = iter->next;
    }
  }
  return 0;
}

/*
  Cast a pointer up an inheritance hierarchy
*/
SWIGRUNTIMEINLINE void *
SWIG_TypeCast(swig_cast_info *ty, void *ptr, int *newmemory) {
  return ((!ty) || (!ty->converter)) ? ptr : (*ty->converter)(ptr, newmemory);
}

/*
   Dynamic pointer casting. Down an inheritance hierarchy
*/
SWIGRUNTIME swig_type_info *
SWIG_TypeDynamicCast(swig_type_info *ty, void **ptr) {
  swig_type_info *lastty = ty;
  if (!ty || !ty->dcast) return ty;
  while (ty && (ty->dcast)) {
    ty = (*ty->dcast)(ptr);
    if (ty) lastty = ty;
  }
  return lastty;
}

/*
  Return the name associated with this type
*/
SWIGRUNTIMEINLINE const char *
SWIG_TypeName(const swig_type_info *ty) {
  return ty->name;
}

/*
  Return the pretty name associated with this type,
  that is an unmangled type name in a form presentable to the user.
*/
SWIGRUNTIME const char *
SWIG_TypePrettyName(const swig_type_info *type) {
  /* The "str" field contains the equivalent pretty names of the
     type, separated by vertical-bar characters.  Choose the last
     name. It should be the most specific; a fully resolved name
     but not necessarily with default template parameters expanded. */
  if (!type) return NULL;
  if (type->str != NULL) {
    const char *last_name = type->str;
    const char *s;
    for (s = type->str; *s; s++)
      if (*s == '|') last_name = s+1;
    return last_name;
  }
  else
    return type->name;
}

/*
   Set the clientdata field for a type
*/
SWIGRUNTIME void
SWIG_TypeClientData(swig_type_info *ti, void *clientdata) {
  swig_cast_info *cast = ti->cast;
  /* if (ti->clientdata == clientdata) return; */
  ti->clientdata = clientdata;

  while (cast) {
    if (!cast->converter) {
      swig_type_info *tc = cast->type;
      if (!tc->clientdata) {
	SWIG_TypeClientData(tc, clientdata);
      }
    }
    cast = cast->next;
  }
}
SWIGRUNTIME void
SWIG_TypeNewClientData(swig_type_info *ti, void *clientdata) {
  SWIG_TypeClientData(ti, clientdata);
  ti->owndata = 1;
}

/*
  Search for a swig_type_info structure only by mangled name
  Search is a O(log #types)

  We start searching at module start, and finish searching when start == end.
  Note: if start == end at the beginning of the function, we go all the way around
  the circular list.
*/
SWIGRUNTIME swig_type_info *
SWIG_MangledTypeQueryModule(swig_module_info *start,
                            swig_module_info *end,
		            const char *name) {
  swig_module_info *iter = start;
  do {
    if (iter->size) {
      size_t l = 0;
      size_t r = iter->size - 1;
      do {
	/* since l+r >= 0, we can (>> 1) instead (/ 2) */
	size_t i = (l + r) >> 1;
	const char *iname = iter->types[i]->name;
	if (iname) {
	  int compare = strcmp(name, iname);
	  if (compare == 0) {
	    return iter->types[i];
	  } else if (compare < 0) {
	    if (i) {
	      r = i - 1;
	    } else {
	      break;
	    }
	  } else if (compare > 0) {
	    l = i + 1;
	  }
	} else {
	  break; /* should never happen */
	}
      } while (l <= r);
    }
    iter = iter->next;
  } while (iter != end);
  return 0;
}

/*
  Search for a swig_type_info structure for either a mangled name or a human readable name.
  It first searches the mangled names of the types, which is a O(log #types)
  If a type is not found it then searches the human readable names, which is O(#types).

  We start searching at module start, and finish searching when start == end.
  Note: if start == end at the beginning of the function, we go all the way around
  the circular list.
*/
SWIGRUNTIME swig_type_info *
SWIG_TypeQueryModule(swig_module_info *start,
                     swig_module_info *end,
		     const char *name) {
  /* STEP 1: Search the name field using binary search */
  swig_type_info *ret = SWIG_MangledTypeQueryModule(start, end, name);
  if (ret) {
    return ret;
  } else {
    /* STEP 2: If the type hasn't been found, do a complete search
       of the str field (the human readable name) */
    swig_module_info *iter = start;
    do {
      size_t i = 0;
      for (; i < iter->size; ++i) {
	if (iter->types[i]->str && (SWIG_TypeEquiv(iter->types[i]->str, name)))
	  return iter->types[i];
      }
      iter = iter->next;
    } while (iter != end);
  }

  /* neither found a match */
  return 0;
}

/*
   Pack binary data into a string
*/
SWIGRUNTIME char *
SWIG_PackData(char *c, void *ptr, size_t sz) {
  static const char hex[17] = "0123456789abcdef";
  const unsigned char *u = (unsigned char *) ptr;
  const unsigned char *eu =  u + sz;
  for (; u != eu; ++u) {
    unsigned char uu = *u;
    *(c++) = hex[(uu & 0xf0) >> 4];
    *(c++) = hex[uu & 0xf];
  }
  return c;
}

/*
   Unpack binary data from a string
*/
SWIGRUNTIME const char *
SWIG_UnpackData(const char *c, void *ptr, size_t sz) {
  unsigned char *u = (unsigned char *) ptr;
  const unsigned char *eu = u + sz;
  for (; u != eu; ++u) {
    char d = *(c++);
    unsigned char uu;
    if ((d >= '0') && (d <= '9'))
      uu = (unsigned char)((d - '0') << 4);
    else if ((d >= 'a') && (d <= 'f'))
      uu = (unsigned char)((d - ('a'-10)) << 4);
    else
      return (char *) 0;
    d = *(c++);
    if ((d >= '0') && (d <= '9'))
      uu |= (unsigned char)(d - '0');
    else if ((d >= 'a') && (d <= 'f'))
      uu |= (unsigned char)(d - ('a'-10));
    else
      return (char *) 0;
    *u = uu;
  }
  return c;
}

/*
   Pack 'void *' into a string buffer.
*/
SWIGRUNTIME char *
SWIG_PackVoidPtr(char *buff, void *ptr, const char *name, size_t bsz) {
  char *r = buff;
  if ((2*sizeof(void *) + 2) > bsz) return 0;
  *(r++) = '_';
  r = SWIG_PackData(r,&ptr,sizeof(void *));
  if (strlen(name) + 1 > (bsz - (r - buff))) return 0;
  strcpy(r,name);
  return buff;
}

SWIGRUNTIME const char *
SWIG_UnpackVoidPtr(const char *c, void **ptr, const char *name) {
  if (*c != '_') {
    if (strcmp(c,"NULL") == 0) {
      *ptr = (void *) 0;
      return name;
    } else {
      return 0;
    }
  }
  return SWIG_UnpackData(++c,ptr,sizeof(void *));
}

SWIGRUNTIME char *
SWIG_PackDataName(char *buff, void *ptr, size_t sz, const char *name, size_t bsz) {
  char *r = buff;
  size_t lname = (name ? strlen(name) : 0);
  if ((2*sz + 2 + lname) > bsz) return 0;
  *(r++) = '_';
  r = SWIG_PackData(r,ptr,sz);
  if (lname) {
    strncpy(r,name,lname+1);
  } else {
    *r = 0;
  }
  return buff;
}

SWIGRUNTIME const char *
SWIG_UnpackDataName(const char *c, void *ptr, size_t sz, const char *name) {
  if (*c != '_') {
    if (strcmp(c,"NULL") == 0) {
      memset(ptr,0,sz);
      return name;
    } else {
      return 0;
    }
  }
  return SWIG_UnpackData(++c,ptr,sz);
}

#ifdef __cplusplus
}
#endif

/* SWIG Errors applicable to all language modules, values are reserved from -1 to -99 */
#define  SWIG_UnknownError    	   -1
#define  SWIG_IOError        	   -2
#define  SWIG_RuntimeError   	   -3
#define  SWIG_IndexError     	   -4
#define  SWIG_TypeError      	   -5
#define  SWIG_DivisionByZero 	   -6
#define  SWIG_OverflowError  	   -7
#define  SWIG_SyntaxError    	   -8
#define  SWIG_ValueError     	   -9
#define  SWIG_SystemError    	   -10
#define  SWIG_AttributeError 	   -11
#define  SWIG_MemoryError    	   -12
#define  SWIG_NullReferenceError   -13


/* ---------------------------------------------------------------------------
 * Error handling
 *
 * ---------------------------------------------------------------------------*/

/*
 * We support several forms:
 *
 * SWIG_Raise("Error message")
 * which creates an Error object with the error message
 *
 * SWIG_Raise(SWIG_TypeError, "Type error")
 * which creates the specified error type with the message
 *
 * SWIG_Raise(obj)
 * which throws the object itself
 *
 * SWIG_Raise(obj, "Exception const &", SWIGType_p_Exception)
 * which also throws the object itself and discards the unneeded extra type info
 *
 * These must be functions instead of macros to use the C++ overloading to
 * resolve the arguments
 */
#define SWIG_exception(code, msg)               SWIG_Error(code, msg)
#define SWIG_fail                               goto fail

#ifdef NAPI_CPP_EXCEPTIONS

#define SWIG_Error(code, msg)                   SWIG_NAPI_Raise(env, code, msg)
#define NAPI_CHECK_MAYBE(maybe)                 (maybe)
#define NAPI_CHECK_RESULT(maybe, result)        (result = maybe)

SWIGINTERN void SWIG_NAPI_Raise(Napi::Env env, const char *msg) {
  throw Napi::Error::New(env, msg);
}

SWIGINTERN void SWIG_NAPI_Raise(Napi::Env env, int type, const char *msg) {
  switch(type) {
    default:
    case SWIG_IOError:
    case SWIG_MemoryError:
    case SWIG_SystemError:
    case SWIG_RuntimeError:
    case SWIG_DivisionByZero:
    case SWIG_SyntaxError:
      throw Napi::Error::New(env, msg);
    case SWIG_OverflowError:
    case SWIG_IndexError:
      throw Napi::RangeError::New(env, msg);
    case SWIG_ValueError:
    case SWIG_TypeError:
      throw Napi::TypeError::New(env, msg);
  }
}

SWIGINTERN void SWIG_NAPI_Raise(Napi::Env env, Napi::Value obj,
        const char *msg = nullptr, swig_type_info *info = nullptr) {
  throw Napi::Error(env, obj);
}

#else

#define SWIG_Error(code, msg)     do { SWIG_NAPI_Raise(env, code, msg); SWIG_fail; } while (0)
#define NAPI_CHECK_MAYBE(maybe)   do { if (maybe.IsNothing()) SWIG_fail; } while (0)
#define NAPI_CHECK_RESULT(maybe, result)          \
        do {                                      \
                auto r = maybe;                   \
                if (r.IsNothing()) SWIG_fail;     \
                result = r.Unwrap();              \
        } while (0)

SWIGINTERN void SWIG_NAPI_Raise(Napi::Env env, const char *msg) {
  Napi::Error::New(env, msg).ThrowAsJavaScriptException();
}

SWIGINTERN void SWIG_NAPI_Raise(Napi::Env env, int type, const char *msg) {
  switch(type) {
    default:
    case SWIG_IOError:
    case SWIG_MemoryError:
    case SWIG_SystemError:
    case SWIG_RuntimeError:
    case SWIG_DivisionByZero:
    case SWIG_SyntaxError:
      Napi::Error::New(env, msg).ThrowAsJavaScriptException();
      return;
    case SWIG_OverflowError:
    case SWIG_IndexError:
      Napi::RangeError::New(env, msg).ThrowAsJavaScriptException();
      return;
    case SWIG_ValueError:
    case SWIG_TypeError:
      Napi::TypeError::New(env, msg).ThrowAsJavaScriptException();
      return;
  }
}

SWIGINTERN void SWIG_NAPI_Raise(Napi::Env env, Napi::Value obj,
        const char *msg = nullptr, swig_type_info *info = nullptr) {
  Napi::Error(env, obj).ThrowAsJavaScriptException();
}

#endif

void JS_veto_set_variable(const Napi::CallbackInfo &info) {
  SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}

struct EnvInstanceData {
  Napi::Env env;
  // Base class per-environment constructor, used to check
  // if a JS object is a SWIG wrapper
  Napi::FunctionReference *SWIG_NAPI_ObjectWrapCtor;
  // Per-environment wrapper constructors, indexed by the number in
  // swig_type->clientdata
  Napi::FunctionReference **ctor;
  swig_module_info *swig_module;
  EnvInstanceData(Napi::Env, swig_module_info *);
  ~EnvInstanceData();
};

typedef size_t SWIG_NAPI_ClientData;

// Base class for all wrapped objects,
// used mostly when unwrapping unknown objects
template <typename SWIG_OBJ_WRAP>
class SWIG_NAPI_ObjectWrap_templ : public Napi::ObjectWrap<SWIG_OBJ_WRAP> {
  public:
    void *self;
    bool owned;
    size_t size;
    swig_type_info *info;
    SWIG_NAPI_ObjectWrap_templ(const Napi::CallbackInfo &info);
    SWIG_NAPI_ObjectWrap_templ(bool, const Napi::CallbackInfo &info) :
        Napi::ObjectWrap<SWIG_OBJ_WRAP>(info),
        self(nullptr),
        owned(true),
        size(0),
        info(nullptr)
        {}
    virtual ~SWIG_NAPI_ObjectWrap_templ() {};

    Napi::Value ToString(const Napi::CallbackInfo &info);
};

template <typename SWIG_OBJ_WRAP>
SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>::SWIG_NAPI_ObjectWrap_templ(const Napi::CallbackInfo &info) :
        Napi::ObjectWrap<SWIG_OBJ_WRAP>(info), size(0), info(nullptr) { 
  Napi::Env env = info.Env();
  if (info.Length() == 1 && info[0].IsExternal()) {
    // This constructor has been called internally from C++/SWIG
    // to wrap an already existing C++ object of unknown type in JS
    this->self = info[0].As<Napi::External<void>>().Data();
    this->owned = false;
  } else {
    SWIG_Error(SWIG_ERROR, "This constructor is not accessible from JS");
  }
  return;
  goto fail;
fail:
  return;
}

template <typename SWIG_OBJ_WRAP>
Napi::Value SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>::ToString(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  static char repr[128];
  const char *name = SWIG_TypePrettyName(this->info);
  snprintf(repr, sizeof(repr), "{SwigObject %s (%s) at %p %s}",
    this->info ? this->info->name : "unknown",
    name ? name : "unknown",
    this->self,
    this->owned ? "[owned]" : "[copy]");
  return Napi::String::New(env, repr);
}

class SWIG_NAPI_ObjectWrap_inst : public SWIG_NAPI_ObjectWrap_templ<SWIG_NAPI_ObjectWrap_inst> {
public:
  using SWIG_NAPI_ObjectWrap_templ::SWIG_NAPI_ObjectWrap_templ;
  static Napi::Function GetClass(Napi::Env);
  static void GetMembers(
    Napi::Env,
    std::map<std::string, SWIG_NAPI_ObjectWrap_templ::PropertyDescriptor> &,
    std::map<std::string, SWIG_NAPI_ObjectWrap_templ::PropertyDescriptor> &
  );
};

void SWIG_NAPI_ObjectWrap_inst::GetMembers(
        Napi::Env env,
        std::map<std::string, SWIG_NAPI_ObjectWrap_templ::PropertyDescriptor> &members,
        std::map<std::string, SWIG_NAPI_ObjectWrap_templ::PropertyDescriptor> &
) {
  members.erase("toString");
  members.insert({"toString", SWIG_NAPI_ObjectWrap_templ::InstanceMethod("toString", &SWIG_NAPI_ObjectWrap_templ::ToString)});
}

Napi::Function SWIG_NAPI_ObjectWrap_inst::GetClass(Napi::Env env) {
  return Napi::ObjectWrap<SWIG_NAPI_ObjectWrap_inst>::DefineClass(env, "SwigObject", {});
}

SWIGRUNTIME int SWIG_NAPI_ConvertInstancePtr(Napi::Object objRef, void **ptr, swig_type_info *info, int flags) {
  SWIG_NAPI_ObjectWrap_inst *ow;
  Napi::Env env = objRef.Env();
  if(!objRef.IsObject()) return SWIG_ERROR;

  // Check if this is a SWIG wrapper
  Napi::FunctionReference *ctor = env.GetInstanceData<EnvInstanceData>()->SWIG_NAPI_ObjectWrapCtor;
  bool instanceOf;
  NAPI_CHECK_RESULT(objRef.InstanceOf(ctor->Value()), instanceOf);
  if (!instanceOf) {
    return SWIG_TypeError;
  }

  ow = Napi::ObjectWrap<SWIG_NAPI_ObjectWrap_inst>::Unwrap(objRef);

  // Now check if the SWIG type is compatible unless the types match exactly or the type is unknown
  if(info && ow->info != info && ow->info != nullptr) {
    swig_cast_info *tc = SWIG_TypeCheckStruct(ow->info, info);
    if (!tc && ow->info->name) {
      tc = SWIG_TypeCheck(ow->info->name, info);
    }
    bool type_valid = tc != 0;
    if(!type_valid) {
      return SWIG_TypeError;
    }
    int newmemory = 0;
    *ptr = SWIG_TypeCast(tc, ow->self, &newmemory);
    assert(!newmemory); /* newmemory handling not yet implemented */
  } else {
    *ptr = ow->self;
  }

  if (((flags & SWIG_POINTER_RELEASE) == SWIG_POINTER_RELEASE) && !ow->owned) {
    return SWIG_ERROR_RELEASE_NOT_OWNED;
  } else {
    if (flags & SWIG_POINTER_DISOWN) {
      ow->owned = false;
    }
    if (flags & SWIG_POINTER_CLEAR) {
      ow->self = nullptr;
    }
  }
  return SWIG_OK;
  goto fail;
fail:
  return SWIG_ERROR;
}


SWIGRUNTIME int SWIG_NAPI_GetInstancePtr(Napi::Value valRef, void **ptr) {
  SWIG_NAPI_ObjectWrap_inst *ow;
  if(!valRef.IsObject()) {
    return SWIG_TypeError;
  }
  Napi::Object objRef;
  NAPI_CHECK_RESULT(valRef.ToObject(), objRef);
  ow = Napi::ObjectWrap<SWIG_NAPI_ObjectWrap_inst>::Unwrap(objRef);

  if(ow->self == nullptr) {
    return SWIG_ERROR;
  }

  *ptr = ow->self;
  return SWIG_OK;
  goto fail;
fail:
  return SWIG_ERROR;
}


SWIGRUNTIME int SWIG_NAPI_ConvertPtr(Napi::Value valRef, void **ptr, swig_type_info *info, int flags) {
  // special case: JavaScript null => C NULL pointer
  if (valRef.IsNull()) {
    *ptr=0;
    return (flags & SWIG_POINTER_NO_NULL) ? SWIG_NullReferenceError : SWIG_OK;
  }

  if (!valRef.IsObject()) {
    return SWIG_TypeError;
  }

  Napi::Object objRef;
  NAPI_CHECK_RESULT(valRef.ToObject(), objRef);
  return SWIG_NAPI_ConvertInstancePtr(objRef, ptr, info, flags);
  goto fail;
fail:
  return SWIG_ERROR;
}

SWIGRUNTIME Napi::Value SWIG_NAPI_NewPointerObj(Napi::Env env, void *ptr, swig_type_info *info, int flags) {
  Napi::External<void> native;
  Napi::FunctionReference *ctor;

  if (ptr == nullptr) {
    return env.Null();
  }
  native = Napi::External<void>::New(env, ptr);

  size_t *idx = info != nullptr ?
        reinterpret_cast<SWIG_NAPI_ClientData *>(info->clientdata) :
        nullptr;
  if (idx == nullptr) {
    // This type does not have a dedicated wrapper
    ctor = env.GetInstanceData<EnvInstanceData>()->SWIG_NAPI_ObjectWrapCtor;
  } else {
    ctor = env.GetInstanceData<EnvInstanceData>()->ctor[*idx];
  }

  Napi::Value wrapped;
  NAPI_CHECK_RESULT(ctor->New({native}), wrapped);

  // Preserve the type even if using the generic wrapper
  if (idx == nullptr && info != nullptr) {
    Napi::Object obj;
    NAPI_CHECK_RESULT(wrapped.ToObject(), obj);
    Napi::ObjectWrap<SWIG_NAPI_ObjectWrap_inst>::Unwrap(obj)->info = info;
  }

  if ((flags & SWIG_POINTER_OWN) == SWIG_POINTER_OWN) {
    Napi::Object obj;
    NAPI_CHECK_RESULT(wrapped.ToObject(), obj);
    Napi::ObjectWrap<SWIG_NAPI_ObjectWrap_inst>::Unwrap(obj)->owned = true;
  }

  return wrapped;
  goto fail;
fail:
  return Napi::Value();
}

#define SWIG_ConvertPtr(obj, ptr, info, flags)          SWIG_NAPI_ConvertPtr(obj, ptr, info, flags)
#define SWIG_NewPointerObj(ptr, info, flags)            SWIG_NAPI_NewPointerObj(env, ptr, info, flags)

#define SWIG_ConvertInstance(obj, pptr, type, flags)    SWIG_NAPI_ConvertInstancePtr(obj, pptr, type, flags)
#define SWIG_NewInstanceObj(thisvalue, type, flags)     SWIG_NAPI_NewPointerObj(env, thisvalue, type, flags)

#define SWIG_ConvertFunctionPtr(obj, pptr, type)        SWIG_NAPI_ConvertPtr(obj, pptr, type, 0)
#define SWIG_NewFunctionPtrObj(ptr, type)               SWIG_NAPI_NewPointerObj(env, ptr, type, 0)

#define SWIG_GetInstancePtr(obj, ptr)                   SWIG_NAPI_GetInstancePtr(obj, ptr)

SWIGRUNTIME Napi::Value _SWIG_NAPI_wrap_equals(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  void *arg1 = (void *) 0 ;
  void *arg2 = (void *) 0 ;
  bool result;
  int res1;
  int res2;

  if(info.Length() != 1) SWIG_Error(SWIG_ERROR, "Illegal number of arguments for equals.");

  res1 = SWIG_GetInstancePtr(info.This(), &arg1);
  if (!SWIG_IsOK(res1)) {
    SWIG_Error(SWIG_ERROR, "Could not get pointer from 'this' object for equals.");
  }
  res2 = SWIG_GetInstancePtr(info[0], &arg2);
  if (!SWIG_IsOK(res2)) {
    SWIG_Error(SWIG_ArgError(res2), " in method '" "equals" "', argument " "1"" of type '" "void *""'");
  }

  result = (bool)(arg1 == arg2);
  jsresult = Napi::Boolean::New(env, result);

  return jsresult;
  goto fail;
fail:
  return Napi::Value();
}

SWIGRUNTIME Napi::Value _wrap_getCPtr(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  void *arg1 = (void *) 0 ;
  long result;
  int res1;

  res1 = SWIG_GetInstancePtr(info.This(), &arg1);
  if (!SWIG_IsOK(res1)) {
    SWIG_Error(SWIG_ArgError(res1), " in method '" "getCPtr" "', argument " "1"" of type '" "void *""'");
  }

  result = (long)arg1;
  jsresult = Napi::Number::New(env, result);

  return jsresult;
  goto fail;
fail:
  return Napi::Value();
}


/* ---------------------------------------------------------------------------
 * PackedData object
 * (objects visible to JS that do not have a dedicated wrapper but must preserve type)
 * ---------------------------------------------------------------------------*/

SWIGRUNTIME
Napi::Value SWIG_NAPI_NewPackedObj(Napi::Env env, void *data, size_t size, swig_type_info *type) {
  void *data_copy = new uint8_t[size];
  memcpy(data_copy, data, size);
  Napi::Value val = SWIG_NAPI_NewPointerObj(env, data_copy, type, SWIG_POINTER_OWN);
  Napi::Object obj;
  if (val.IsEmpty()) goto fail;

  NAPI_CHECK_RESULT(val.ToObject(), obj);
  Napi::ObjectWrap<SWIG_NAPI_ObjectWrap_inst>::Unwrap(obj)->size = size;

fail:
  return val;
}

SWIGRUNTIME
int SWIG_NAPI_ConvertPacked(Napi::Value valRef, void *ptr, size_t size, swig_type_info *type) {
  void *tmp;
  if (!SWIG_IsOK(SWIG_NAPI_ConvertPtr(valRef, &tmp, type, 0))) {
    return SWIG_ERROR;
  }
  memcpy(ptr, tmp, size);
  return SWIG_OK;
}

#define SWIG_ConvertMember(obj, ptr, sz, ty)            SWIG_NAPI_ConvertPacked(obj, ptr, sz, ty)
#define SWIG_NewMemberObj(ptr, sz, type)                SWIG_NAPI_NewPackedObj(env, ptr, sz, type)


/* ---------------------------------------------------------------------------
 * Support for IN/OUTPUT typemaps (see Lib/typemaps/inoutlist.swg)
 *
 * ---------------------------------------------------------------------------*/

SWIGRUNTIME

Napi::Value SWIG_NAPI_AppendOutput(Napi::Env env, Napi::Value result, Napi::Value obj) {
  if (result.IsUndefined()) {
    result = Napi::Array::New(env);
  } else if (!result.IsArray()) {
    Napi::Array tmparr = Napi::Array::New(env);
    tmparr.Set(static_cast<uint32_t>(0), result);
    result = tmparr;
  }

  Napi::Array arr = result.As<Napi::Array>();
  arr.Set(arr.Length(), obj);
  return arr;
}


/* -------- TYPES TABLE (BEGIN) -------- */

#define SWIGTYPE_p_ByteArray swig_types[0]
#define SWIGTYPE_p_HLINCLIENT_JS swig_types[1]
#define SWIGTYPE_p_HLINHW_JS swig_types[2]
#define SWIGTYPE_p_INT64_JS swig_types[3]
#define SWIGTYPE_p_INT_JS swig_types[4]
#define SWIGTYPE_p_TLINFrameEntry swig_types[5]
#define SWIGTYPE_p_TLINHardwareStatus swig_types[6]
#define SWIGTYPE_p_TLINMsg swig_types[7]
#define SWIGTYPE_p_TLINRcvMsg swig_types[8]
#define SWIGTYPE_p_TLINScheduleSlot swig_types[9]
#define SWIGTYPE_p_TLINVersion swig_types[10]
#define SWIGTYPE_p___int64 swig_types[11]
#define SWIGTYPE_p_char swig_types[12]
#define SWIGTYPE_p_float swig_types[13]
#define SWIGTYPE_p_int swig_types[14]
#define SWIGTYPE_p_long swig_types[15]
#define SWIGTYPE_p_long_long swig_types[16]
#define SWIGTYPE_p_p_char swig_types[17]
#define SWIGTYPE_p_p_unsigned_long swig_types[18]
#define SWIGTYPE_p_short swig_types[19]
#define SWIGTYPE_p_signed___int64 swig_types[20]
#define SWIGTYPE_p_signed_char swig_types[21]
#define SWIGTYPE_p_unsigned___int64 swig_types[22]
#define SWIGTYPE_p_unsigned_char swig_types[23]
#define SWIGTYPE_p_unsigned_int swig_types[24]
#define SWIGTYPE_p_unsigned_long swig_types[25]
#define SWIGTYPE_p_unsigned_long_long swig_types[26]
#define SWIGTYPE_p_unsigned_short swig_types[27]
static swig_type_info *swig_types[29];
static swig_module_info swig_module = {swig_types, 28, 0, 0, 0, 0};
#define SWIG_TypeQuery(name) SWIG_TypeQueryModule(&swig_module, &swig_module, name)
#define SWIG_MangledTypeQuery(name) SWIG_MangledTypeQueryModule(&swig_module, &swig_module, name)

/* -------- TYPES TABLE (END) -------- */



#ifdef __cplusplus
#include <utility>
/* SwigValueWrapper is described in swig.swg */
template<typename T> class SwigValueWrapper {
  struct SwigSmartPointer {
    T *ptr;
    SwigSmartPointer(T *p) : ptr(p) { }
    ~SwigSmartPointer() { delete ptr; }
    SwigSmartPointer& operator=(SwigSmartPointer& rhs) { T* oldptr = ptr; ptr = 0; delete oldptr; ptr = rhs.ptr; rhs.ptr = 0; return *this; }
    void reset(T *p) { T* oldptr = ptr; ptr = 0; delete oldptr; ptr = p; }
  } pointer;
  SwigValueWrapper& operator=(const SwigValueWrapper<T>& rhs);
  SwigValueWrapper(const SwigValueWrapper<T>& rhs);
public:
  SwigValueWrapper() : pointer(0) { }
  SwigValueWrapper& operator=(const T& t) { SwigSmartPointer tmp(new T(t)); pointer = tmp; return *this; }
#if __cplusplus >=201103L
  SwigValueWrapper& operator=(T&& t) { SwigSmartPointer tmp(new T(std::move(t))); pointer = tmp; return *this; }
  operator T&&() const { return std::move(*pointer.ptr); }
#else
  operator T&() const { return *pointer.ptr; }
#endif
  T *operator&() const { return pointer.ptr; }
  static void reset(SwigValueWrapper& t, T *p) { t.pointer.reset(p); }
};

/*
 * SwigValueInit() is a generic initialisation solution as the following approach:
 * 
 *       T c_result = T();
 * 
 * doesn't compile for all types for example:
 * 
 *       unsigned int c_result = unsigned int();
 */
template <typename T> T SwigValueInit() {
  return T();
}

#if __cplusplus >=201103L
# define SWIG_STD_MOVE(OBJ) std::move(OBJ)
#else
# define SWIG_STD_MOVE(OBJ) OBJ
#endif

#endif


#define SWIG_as_voidptr(a) const_cast< void * >(static_cast< const void * >(a)) 
#define SWIG_as_voidptrptr(a) ((void)SWIG_as_voidptr(*a),reinterpret_cast< void** >(a)) 


#include <stdexcept>


#include <assert.h>


#include <windows.h>
#include <stdlib.h>
#include "PLinApi.h"


#include <stdint.h>		// Use the C99 official header


typedef __int64 INT64_JS;

SWIGINTERN INT64_JS *new_INT64_JS(){
  return new __int64();
}

SWIGINTERN
int SWIG_AsVal_double (Napi::Value obj, double *val)
{
  if(!obj.IsNumber()) {
    return SWIG_TypeError;
  }

  if(val) {
    Napi::Number num;
    NAPI_CHECK_RESULT(obj.ToNumber(), num);
    *val = static_cast<double>(num.DoubleValue());
  }

  return SWIG_OK;
  goto fail;
fail:
  return SWIG_ERROR;
}


#include <float.h>


#include <math.h>


SWIGINTERNINLINE int
SWIG_CanCastAsInteger(double *d, double min, double max) {
  double x = *d;
  if ((min <= x && x <= max)) {
   double fx, cx, rd;
   errno = 0;
   fx = floor(x);
   cx = ceil(x);
   rd =  ((x - fx) < 0.5) ? fx : cx; /* simple rint */
   if ((errno == EDOM) || (errno == ERANGE)) {
     errno = 0;
   } else {
     double summ, reps, diff;
     if (rd < x) {
       diff = x - rd;
     } else if (rd > x) {
       diff = rd - x;
     } else {
       return 1;
     }
     summ = rd + x;
     reps = diff/summ;
     if (reps < 8*DBL_EPSILON) {
       *d = rd;
       return 1;
     }
   }
  }
  return 0;
}


SWIGINTERN
int SWIG_AsVal_long (Napi::Value obj, long* val)
{
  if (!obj.IsNumber()) {
    return SWIG_TypeError;
  }
  if (val) {
    Napi::Number num;
    NAPI_CHECK_RESULT(obj.ToNumber(), num);
    *val = static_cast<long>(num.Int64Value());
  }

  return SWIG_OK;
  goto fail;
fail:
  return SWIG_ERROR;
}


#include <limits.h>
#if !defined(SWIG_NO_LLONG_MAX)
# if !defined(LLONG_MAX) && defined(__GNUC__) && defined (__LONG_LONG_MAX__)
#   define LLONG_MAX __LONG_LONG_MAX__
#   define LLONG_MIN (-LLONG_MAX - 1LL)
#   define ULLONG_MAX (LLONG_MAX * 2ULL + 1ULL)
# endif
#endif


#if defined(LLONG_MAX) && !defined(SWIG_LONG_LONG_AVAILABLE)
#  define SWIG_LONG_LONG_AVAILABLE
#endif


#ifdef SWIG_LONG_LONG_AVAILABLE
SWIGINTERN
int SWIG_AsVal_long_SS_long (Napi::Value obj, long long* val)
{
  if(!obj.IsNumber()) {
    return SWIG_TypeError;
  }

  if (val) {
    Napi::Number num;
    NAPI_CHECK_RESULT(obj.ToNumber(), num);
    *val = static_cast<long long>(num.Int64Value());
  }
  return SWIG_OK;
  goto fail;
fail:
  return SWIG_ERROR;
}
#endif

SWIGINTERN void INT64_JS_assign(INT64_JS *self,__int64 value){
  *self = value;
}
SWIGINTERN __int64 INT64_JS_value(INT64_JS *self){
  return *self;
}

#ifdef SWIG_LONG_LONG_AVAILABLE
SWIGINTERN
Napi::Value SWIG_From_long_SS_long(Napi::Env env, long long val)
{
  return Napi::Number::New(env, val);
}
#endif

SWIGINTERN __int64 *INT64_JS_cast(INT64_JS *self){
  return self;
}
SWIGINTERN INT64_JS *INT64_JS_frompointer(__int64 *t){
  return (INT64_JS *) t;
}

typedef int INT_JS;

SWIGINTERN INT_JS *new_INT_JS(){
  return new int();
}

SWIGINTERN
int SWIG_AsVal_int (Napi::Value valRef, int* val)
{
  if (!valRef.IsNumber()) {
    return SWIG_TypeError;
  }
  if (val) {
    Napi::Number num;
    NAPI_CHECK_RESULT(valRef.ToNumber(), num);
    *val = static_cast<int>(num.Int32Value());
  }

  return SWIG_OK;
  goto fail;
fail:
  return SWIG_ERROR;
}

SWIGINTERN void INT_JS_assign(INT_JS *self,int value){
  *self = value;
}
SWIGINTERN int INT_JS_value(INT_JS *self){
  return *self;
}

SWIGINTERN
Napi::Value SWIG_From_int(Napi::Env env, int val)
{
  return Napi::Number::New(env, val);
}

SWIGINTERN int *INT_JS_cast(INT_JS *self){
  return self;
}
SWIGINTERN INT_JS *INT_JS_frompointer(int *t){
  return (INT_JS *) t;
}

typedef HLINCLIENT HLINCLIENT_JS;

SWIGINTERN HLINCLIENT_JS *new_HLINCLIENT_JS(){
  return new HLINCLIENT();
}

SWIGINTERN
int SWIG_AsVal_unsigned_SS_long (Napi::Value obj, unsigned long *val)
{
  if(!obj.IsNumber()) {
    return SWIG_TypeError;
  }
  if (val) {
    Napi::Number num;
    NAPI_CHECK_RESULT(obj.ToNumber(), num);
    if (num.Int64Value() < 0) {
      return SWIG_TypeError;
    }
    *val = static_cast<unsigned long>(num.Int64Value());
  }
  return SWIG_OK;
  goto fail;
fail:
  return SWIG_ERROR;
}


SWIGINTERN int
SWIG_AsVal_unsigned_SS_char (Napi::Value obj, unsigned char *val)
{
  unsigned long v;
  int res = SWIG_AsVal_unsigned_SS_long (obj, &v);
  if (SWIG_IsOK(res)) {
    if ((v > UCHAR_MAX)) {
      return SWIG_OverflowError;
    } else {
      if (val) *val = static_cast< unsigned char >(v);
    }
  }  
  return res;
}

SWIGINTERN void HLINCLIENT_JS_assign(HLINCLIENT_JS *self,HLINCLIENT value){
  *self = value;
}
SWIGINTERN HLINCLIENT HLINCLIENT_JS_value(HLINCLIENT_JS *self){
  return *self;
}

SWIGINTERNINLINE Napi::Value
SWIG_From_unsigned_SS_char(Napi::Env env, unsigned char c)
{
  return Napi::Number::New(env, static_cast<double>(c));
}

SWIGINTERN HLINCLIENT *HLINCLIENT_JS_cast(HLINCLIENT_JS *self){
  return self;
}
SWIGINTERN HLINCLIENT_JS *HLINCLIENT_JS_frompointer(HLINCLIENT *t){
  return (HLINCLIENT_JS *) t;
}

typedef BYTE ByteArray;


#ifdef SWIG_LONG_LONG_AVAILABLE
SWIGINTERN
int SWIG_AsVal_unsigned_SS_long_SS_long (Napi::Value obj, unsigned long long *val)
{
  if(!obj.IsNumber()) {
    return SWIG_TypeError;
  }
  if (obj.ToNumber().Int64Value() < 0) {
    return SWIG_TypeError;
  }
  if (val) {
    Napi::Number num;
    NAPI_CHECK_RESULT(obj.ToNumber(), num);
    *val = static_cast<unsigned long long>(num.Int64Value());
  }
  return SWIG_OK;
  goto fail;
fail:
  return SWIG_ERROR;
}
#endif


SWIGINTERNINLINE int
SWIG_AsVal_size_t (Napi::Value obj, size_t *val)
{
  int res = SWIG_TypeError;
#ifdef SWIG_LONG_LONG_AVAILABLE
  if (sizeof(size_t) <= sizeof(unsigned long)) {
#endif
    unsigned long v;
    res = SWIG_AsVal_unsigned_SS_long (obj, val ? &v : 0);
    if (SWIG_IsOK(res) && val) *val = static_cast< size_t >(v);
#ifdef SWIG_LONG_LONG_AVAILABLE
  } else if (sizeof(size_t) <= sizeof(unsigned long long)) {
    unsigned long long v;
    res = SWIG_AsVal_unsigned_SS_long_SS_long (obj, val ? &v : 0);
    if (SWIG_IsOK(res) && val) *val = static_cast< size_t >(v);
  }
#endif
  return res;
}

SWIGINTERN ByteArray *new_ByteArray(size_t nelements){
  return new BYTE[nelements]();
}
SWIGINTERN BYTE ByteArray_getitem(ByteArray *self,size_t index){
  return self[index];
}
SWIGINTERN void ByteArray_setitem(ByteArray *self,size_t index,BYTE value){
  self[index] = value;
}
SWIGINTERN BYTE *ByteArray_cast(ByteArray *self){
  return self;
}
SWIGINTERN ByteArray *ByteArray_frompointer(BYTE *t){
  return (ByteArray *) t;
}

typedef HLINHW HLINHW_JS;

SWIGINTERN HLINHW_JS *new_HLINHW_JS(size_t nelements){
  return new HLINHW[nelements]();
}
SWIGINTERN HLINHW HLINHW_JS_getitem(HLINHW_JS *self,size_t index){
  return self[index];
}

SWIGINTERN
Napi::Value SWIG_From_unsigned_SS_short(Napi::Env env, unsigned short val)
{
  return Napi::Number::New(env, val);
}


SWIGINTERN
int SWIG_AsVal_unsigned_SS_short (Napi::Value valRef, unsigned short* val)
{
  if (!valRef.IsNumber()) {
    return SWIG_TypeError;
  }
  if (val) {
    Napi::Number num;
    NAPI_CHECK_RESULT(valRef.ToNumber(), num);
    if (num.Int64Value() < 0) {
      return SWIG_TypeError;
    }
    *val = static_cast<unsigned short>(num.Uint32Value());
  }

  return SWIG_OK;
  goto fail;
fail:
  return SWIG_ERROR;
}

SWIGINTERN void HLINHW_JS_setitem(HLINHW_JS *self,size_t index,HLINHW value){
  self[index] = value;
}
SWIGINTERN HLINHW *HLINHW_JS_cast(HLINHW_JS *self){
  return self;
}
SWIGINTERN HLINHW_JS *HLINHW_JS_frompointer(HLINHW *t){
  return (HLINHW_JS *) t;
}

SWIGINTERN
Napi::Value SWIG_From_unsigned_SS_int(Napi::Env env, unsigned int val)
{
  return Napi::Number::New(env, val);
}


SWIGINTERN
int SWIG_AsVal_short (Napi::Value valRef, short* val)
{
  if (!valRef.IsNumber()) {
    return SWIG_TypeError;
  }
  if (val) {
    Napi::Number num;
    NAPI_CHECK_RESULT(valRef.ToNumber(), num);
    *val = static_cast<short>(num.Int32Value());
  }

  return SWIG_OK;
  goto fail;
fail:
  return SWIG_ERROR;
}


SWIGINTERN
Napi::Value SWIG_From_long(Napi::Env env, long val)
{
  return Napi::Number::New(env, val);
}


SWIGINTERNINLINE Napi::Value
SWIG_From_short  SWIG_NAPI_FROM_DECL_ARGS(short value)
{    
  return SWIG_From_long  SWIG_NAPI_FROM_CALL_ARGS(value);
}


#ifdef SWIG_LONG_LONG_AVAILABLE
SWIGINTERN
Napi::Value SWIG_From_unsigned_SS_long_SS_long(Napi::Env env, unsigned long long val)
{
  return Napi::Number::New(env, val);
}
#endif


SWIGINTERN
Napi::Value SWIG_From_unsigned_SS_long(Napi::Env env, unsigned long val)
{
  return Napi::Number::New(env, val);
}


SWIGINTERN swig_type_info*
SWIG_pchar_descriptor(void)
{
  static int init = 0;
  static swig_type_info* info = 0;
  if (!init) {
    info = SWIG_TypeQuery("_p_char");
    init = 1;
  }
  return info;
}


SWIGINTERN int
SWIG_AsCharPtrAndSize(Napi::Value valRef, char** cptr, size_t* psize, int *alloc)
{
  if(valRef.IsString()) {
    Napi::String js_str;
    NAPI_CHECK_RESULT(valRef.ToString(), js_str);

    std::string str = js_str.Utf8Value();
    size_t len = str.size() + 1;
    char* cstr = (char*) (new char[len]());
    memcpy(cstr, str.data(), len);
    
    if(alloc) *alloc = SWIG_NEWOBJ;
    if(psize) *psize = len;
    if(cptr) *cptr = cstr;
    
    return SWIG_OK;
  } else {
    if(valRef.IsObject()) {
      swig_type_info* pchar_descriptor = SWIG_pchar_descriptor();
      Napi::Object obj;
      NAPI_CHECK_RESULT(valRef.ToObject(), obj);
      // try if the object is a wrapped char[]
      if (pchar_descriptor) {
        void* vptr = 0;
        if (SWIG_ConvertPtr(obj, &vptr, pchar_descriptor, 0) == SWIG_OK) {
          if (cptr) *cptr = (char *) vptr;
          if (psize) *psize = vptr ? (strlen((char *)vptr) + 1) : 0;
          if (alloc) *alloc = SWIG_OLDOBJ;
          return SWIG_OK;
        }
      }
    }
  }
  goto fail;
fail:
  return SWIG_TypeError;
}





void LoadDll(const char* path) {
  SetDllDirectory(path);
}


#define SWIG_NAPI_INIT xmlpp_initialize


// jsnapi_class_prologue_template
template <typename SWIG_OBJ_WRAP>
class _exports_INT64_JS_templ : public SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP> {
public:
  _exports_INT64_JS_templ(const Napi::CallbackInfo &);
_exports_INT64_JS_templ(bool, const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_new_INT64_JS(const Napi::CallbackInfo &);
virtual ~_exports_INT64_JS_templ();
// jsnapi_class_method_declaration
Napi::Value _wrap_INT64_JS_assign(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_INT64_JS_value(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_INT64_JS_cast(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
static Napi::Value _wrap_INT64_JS_frompointer(const Napi::CallbackInfo &);
// jsnapi_class_epilogue_template
static void JS_veto_set_static_variable(const Napi::CallbackInfo &, const Napi::Value &);
void JS_veto_set_variable(const Napi::CallbackInfo &, const Napi::Value &);
};

template <typename SWIG_OBJ_WRAP>
void _exports_INT64_JS_templ<SWIG_OBJ_WRAP>::JS_veto_set_static_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}

template <typename SWIG_OBJ_WRAP>
void _exports_INT64_JS_templ<SWIG_OBJ_WRAP>::JS_veto_set_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}
// jsnapi_class_instance
class _exports_INT64_JS_inst : public _exports_INT64_JS_templ<_exports_INT64_JS_inst> {
public:
  using _exports_INT64_JS_templ::_exports_INT64_JS_templ;
  virtual ~_exports_INT64_JS_inst() {
    
  };
  static void GetMembers(
    Napi::Env,
    std::map<std::string, _exports_INT64_JS_templ::PropertyDescriptor> &,
    std::map<std::string, _exports_INT64_JS_templ::PropertyDescriptor> &
    );
  static Napi::Function GetClass(Napi::Env);
};
/* Class: INT64_JS (_exports_INT64_JS) */
// jsnapi_getclass
Napi::Function _exports_INT64_JS_inst::GetClass(Napi::Env env) {
  std::map<std::string, _exports_INT64_JS_templ::PropertyDescriptor> members, staticMembers;
  GetMembers(env, members, staticMembers);
  
  std::vector<_exports_INT64_JS_inst::PropertyDescriptor> symbolTable;
  for (auto it = members.begin(); it != members.end(); it++)
  symbolTable.push_back(it->second);
  for (auto it = staticMembers.begin(); it != staticMembers.end(); it++)
  symbolTable.push_back(it->second);
  
  return Napi::ObjectWrap<_exports_INT64_JS_inst>::DefineClass(env, "INT64_JS", symbolTable);
}

void _exports_INT64_JS_inst::GetMembers(
  Napi::Env env,
  std::map<std::string, _exports_INT64_JS_templ::PropertyDescriptor> &members,
  std::map<std::string, _exports_INT64_JS_templ::PropertyDescriptor> &staticMembers
  ) {
  std::map<std::string, SWIG_NAPI_ObjectWrap_templ<SWIG_NAPI_ObjectWrap_inst>::PropertyDescriptor> baseMembers, baseStaticMembers;
  SWIG_NAPI_ObjectWrap_inst::GetMembers(env, baseMembers, baseStaticMembers);
  members.insert(baseMembers.begin(), baseMembers.end());
  staticMembers.insert(staticMembers.begin(), staticMembers.end());
  
  /* register wrapper functions */
  // jsnapi_member_function_descriptor
  members.erase("assign");
  members.insert({
    "assign",
      _exports_INT64_JS_templ::InstanceMethod("assign",
        &_exports_INT64_JS_templ::_wrap_INT64_JS_assign,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  // jsnapi_member_function_descriptor
  members.erase("value");
  members.insert({
    "value",
      _exports_INT64_JS_templ::InstanceMethod("value",
        &_exports_INT64_JS_templ::_wrap_INT64_JS_value,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  // jsnapi_member_function_descriptor
  members.erase("cast");
  members.insert({
    "cast",
      _exports_INT64_JS_templ::InstanceMethod("cast",
        &_exports_INT64_JS_templ::_wrap_INT64_JS_cast,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  
  /* add static class functions and variables */
  // jsnapi_register_static_function
  staticMembers.erase("frompointer");
  staticMembers.insert({
    "frompointer",
      StaticMethod("frompointer",
        &_exports_INT64_JS_templ::_wrap_INT64_JS_frompointer,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  
}
// jsnapi_class_prologue_template
template <typename SWIG_OBJ_WRAP>
class _exports_INT_JS_templ : public SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP> {
public:
  _exports_INT_JS_templ(const Napi::CallbackInfo &);
_exports_INT_JS_templ(bool, const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_new_INT_JS(const Napi::CallbackInfo &);
virtual ~_exports_INT_JS_templ();
// jsnapi_class_method_declaration
Napi::Value _wrap_INT_JS_assign(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_INT_JS_value(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_INT_JS_cast(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
static Napi::Value _wrap_INT_JS_frompointer(const Napi::CallbackInfo &);
// jsnapi_class_epilogue_template
static void JS_veto_set_static_variable(const Napi::CallbackInfo &, const Napi::Value &);
void JS_veto_set_variable(const Napi::CallbackInfo &, const Napi::Value &);
};

template <typename SWIG_OBJ_WRAP>
void _exports_INT_JS_templ<SWIG_OBJ_WRAP>::JS_veto_set_static_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}

template <typename SWIG_OBJ_WRAP>
void _exports_INT_JS_templ<SWIG_OBJ_WRAP>::JS_veto_set_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}
// jsnapi_class_instance
class _exports_INT_JS_inst : public _exports_INT_JS_templ<_exports_INT_JS_inst> {
public:
  using _exports_INT_JS_templ::_exports_INT_JS_templ;
  virtual ~_exports_INT_JS_inst() {
    
  };
  static void GetMembers(
    Napi::Env,
    std::map<std::string, _exports_INT_JS_templ::PropertyDescriptor> &,
    std::map<std::string, _exports_INT_JS_templ::PropertyDescriptor> &
    );
  static Napi::Function GetClass(Napi::Env);
};
/* Class: INT_JS (_exports_INT_JS) */
// jsnapi_getclass
Napi::Function _exports_INT_JS_inst::GetClass(Napi::Env env) {
  std::map<std::string, _exports_INT_JS_templ::PropertyDescriptor> members, staticMembers;
  GetMembers(env, members, staticMembers);
  
  std::vector<_exports_INT_JS_inst::PropertyDescriptor> symbolTable;
  for (auto it = members.begin(); it != members.end(); it++)
  symbolTable.push_back(it->second);
  for (auto it = staticMembers.begin(); it != staticMembers.end(); it++)
  symbolTable.push_back(it->second);
  
  return Napi::ObjectWrap<_exports_INT_JS_inst>::DefineClass(env, "INT_JS", symbolTable);
}

void _exports_INT_JS_inst::GetMembers(
  Napi::Env env,
  std::map<std::string, _exports_INT_JS_templ::PropertyDescriptor> &members,
  std::map<std::string, _exports_INT_JS_templ::PropertyDescriptor> &staticMembers
  ) {
  std::map<std::string, SWIG_NAPI_ObjectWrap_templ<SWIG_NAPI_ObjectWrap_inst>::PropertyDescriptor> baseMembers, baseStaticMembers;
  SWIG_NAPI_ObjectWrap_inst::GetMembers(env, baseMembers, baseStaticMembers);
  members.insert(baseMembers.begin(), baseMembers.end());
  staticMembers.insert(staticMembers.begin(), staticMembers.end());
  
  /* register wrapper functions */
  // jsnapi_member_function_descriptor
  members.erase("assign");
  members.insert({
    "assign",
      _exports_INT_JS_templ::InstanceMethod("assign",
        &_exports_INT_JS_templ::_wrap_INT_JS_assign,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  // jsnapi_member_function_descriptor
  members.erase("value");
  members.insert({
    "value",
      _exports_INT_JS_templ::InstanceMethod("value",
        &_exports_INT_JS_templ::_wrap_INT_JS_value,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  // jsnapi_member_function_descriptor
  members.erase("cast");
  members.insert({
    "cast",
      _exports_INT_JS_templ::InstanceMethod("cast",
        &_exports_INT_JS_templ::_wrap_INT_JS_cast,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  
  /* add static class functions and variables */
  // jsnapi_register_static_function
  staticMembers.erase("frompointer");
  staticMembers.insert({
    "frompointer",
      StaticMethod("frompointer",
        &_exports_INT_JS_templ::_wrap_INT_JS_frompointer,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  
}
// jsnapi_class_prologue_template
template <typename SWIG_OBJ_WRAP>
class _exports_HLINCLIENT_JS_templ : public SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP> {
public:
  _exports_HLINCLIENT_JS_templ(const Napi::CallbackInfo &);
_exports_HLINCLIENT_JS_templ(bool, const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_new_HLINCLIENT_JS(const Napi::CallbackInfo &);
virtual ~_exports_HLINCLIENT_JS_templ();
// jsnapi_class_method_declaration
Napi::Value _wrap_HLINCLIENT_JS_assign(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_HLINCLIENT_JS_value(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_HLINCLIENT_JS_cast(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
static Napi::Value _wrap_HLINCLIENT_JS_frompointer(const Napi::CallbackInfo &);
// jsnapi_class_epilogue_template
static void JS_veto_set_static_variable(const Napi::CallbackInfo &, const Napi::Value &);
void JS_veto_set_variable(const Napi::CallbackInfo &, const Napi::Value &);
};

template <typename SWIG_OBJ_WRAP>
void _exports_HLINCLIENT_JS_templ<SWIG_OBJ_WRAP>::JS_veto_set_static_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}

template <typename SWIG_OBJ_WRAP>
void _exports_HLINCLIENT_JS_templ<SWIG_OBJ_WRAP>::JS_veto_set_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}
// jsnapi_class_instance
class _exports_HLINCLIENT_JS_inst : public _exports_HLINCLIENT_JS_templ<_exports_HLINCLIENT_JS_inst> {
public:
  using _exports_HLINCLIENT_JS_templ::_exports_HLINCLIENT_JS_templ;
  virtual ~_exports_HLINCLIENT_JS_inst() {
    
  };
  static void GetMembers(
    Napi::Env,
    std::map<std::string, _exports_HLINCLIENT_JS_templ::PropertyDescriptor> &,
    std::map<std::string, _exports_HLINCLIENT_JS_templ::PropertyDescriptor> &
    );
  static Napi::Function GetClass(Napi::Env);
};
/* Class: HLINCLIENT_JS (_exports_HLINCLIENT_JS) */
// jsnapi_getclass
Napi::Function _exports_HLINCLIENT_JS_inst::GetClass(Napi::Env env) {
  std::map<std::string, _exports_HLINCLIENT_JS_templ::PropertyDescriptor> members, staticMembers;
  GetMembers(env, members, staticMembers);
  
  std::vector<_exports_HLINCLIENT_JS_inst::PropertyDescriptor> symbolTable;
  for (auto it = members.begin(); it != members.end(); it++)
  symbolTable.push_back(it->second);
  for (auto it = staticMembers.begin(); it != staticMembers.end(); it++)
  symbolTable.push_back(it->second);
  
  return Napi::ObjectWrap<_exports_HLINCLIENT_JS_inst>::DefineClass(env, "HLINCLIENT_JS", symbolTable);
}

void _exports_HLINCLIENT_JS_inst::GetMembers(
  Napi::Env env,
  std::map<std::string, _exports_HLINCLIENT_JS_templ::PropertyDescriptor> &members,
  std::map<std::string, _exports_HLINCLIENT_JS_templ::PropertyDescriptor> &staticMembers
  ) {
  std::map<std::string, SWIG_NAPI_ObjectWrap_templ<SWIG_NAPI_ObjectWrap_inst>::PropertyDescriptor> baseMembers, baseStaticMembers;
  SWIG_NAPI_ObjectWrap_inst::GetMembers(env, baseMembers, baseStaticMembers);
  members.insert(baseMembers.begin(), baseMembers.end());
  staticMembers.insert(staticMembers.begin(), staticMembers.end());
  
  /* register wrapper functions */
  // jsnapi_member_function_descriptor
  members.erase("assign");
  members.insert({
    "assign",
      _exports_HLINCLIENT_JS_templ::InstanceMethod("assign",
        &_exports_HLINCLIENT_JS_templ::_wrap_HLINCLIENT_JS_assign,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  // jsnapi_member_function_descriptor
  members.erase("value");
  members.insert({
    "value",
      _exports_HLINCLIENT_JS_templ::InstanceMethod("value",
        &_exports_HLINCLIENT_JS_templ::_wrap_HLINCLIENT_JS_value,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  // jsnapi_member_function_descriptor
  members.erase("cast");
  members.insert({
    "cast",
      _exports_HLINCLIENT_JS_templ::InstanceMethod("cast",
        &_exports_HLINCLIENT_JS_templ::_wrap_HLINCLIENT_JS_cast,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  
  /* add static class functions and variables */
  // jsnapi_register_static_function
  staticMembers.erase("frompointer");
  staticMembers.insert({
    "frompointer",
      StaticMethod("frompointer",
        &_exports_HLINCLIENT_JS_templ::_wrap_HLINCLIENT_JS_frompointer,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  
}
// jsnapi_class_prologue_template
template <typename SWIG_OBJ_WRAP>
class _exports_ByteArray_templ : public SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP> {
public:
  _exports_ByteArray_templ(const Napi::CallbackInfo &);
_exports_ByteArray_templ(bool, const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_new_ByteArray(const Napi::CallbackInfo &);
virtual ~_exports_ByteArray_templ();
// jsnapi_class_method_declaration
Napi::Value _wrap_ByteArray_getitem(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_ByteArray_setitem(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_ByteArray_cast(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
static Napi::Value _wrap_ByteArray_frompointer(const Napi::CallbackInfo &);
// jsnapi_class_epilogue_template
static void JS_veto_set_static_variable(const Napi::CallbackInfo &, const Napi::Value &);
void JS_veto_set_variable(const Napi::CallbackInfo &, const Napi::Value &);
};

template <typename SWIG_OBJ_WRAP>
void _exports_ByteArray_templ<SWIG_OBJ_WRAP>::JS_veto_set_static_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}

template <typename SWIG_OBJ_WRAP>
void _exports_ByteArray_templ<SWIG_OBJ_WRAP>::JS_veto_set_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}
// jsnapi_class_instance
class _exports_ByteArray_inst : public _exports_ByteArray_templ<_exports_ByteArray_inst> {
public:
  using _exports_ByteArray_templ::_exports_ByteArray_templ;
  virtual ~_exports_ByteArray_inst() {
    
  };
  static void GetMembers(
    Napi::Env,
    std::map<std::string, _exports_ByteArray_templ::PropertyDescriptor> &,
    std::map<std::string, _exports_ByteArray_templ::PropertyDescriptor> &
    );
  static Napi::Function GetClass(Napi::Env);
};
/* Class: ByteArray (_exports_ByteArray) */
// jsnapi_getclass
Napi::Function _exports_ByteArray_inst::GetClass(Napi::Env env) {
  std::map<std::string, _exports_ByteArray_templ::PropertyDescriptor> members, staticMembers;
  GetMembers(env, members, staticMembers);
  
  std::vector<_exports_ByteArray_inst::PropertyDescriptor> symbolTable;
  for (auto it = members.begin(); it != members.end(); it++)
  symbolTable.push_back(it->second);
  for (auto it = staticMembers.begin(); it != staticMembers.end(); it++)
  symbolTable.push_back(it->second);
  
  return Napi::ObjectWrap<_exports_ByteArray_inst>::DefineClass(env, "ByteArray", symbolTable);
}

void _exports_ByteArray_inst::GetMembers(
  Napi::Env env,
  std::map<std::string, _exports_ByteArray_templ::PropertyDescriptor> &members,
  std::map<std::string, _exports_ByteArray_templ::PropertyDescriptor> &staticMembers
  ) {
  std::map<std::string, SWIG_NAPI_ObjectWrap_templ<SWIG_NAPI_ObjectWrap_inst>::PropertyDescriptor> baseMembers, baseStaticMembers;
  SWIG_NAPI_ObjectWrap_inst::GetMembers(env, baseMembers, baseStaticMembers);
  members.insert(baseMembers.begin(), baseMembers.end());
  staticMembers.insert(staticMembers.begin(), staticMembers.end());
  
  /* register wrapper functions */
  // jsnapi_member_function_descriptor
  members.erase("getitem");
  members.insert({
    "getitem",
      _exports_ByteArray_templ::InstanceMethod("getitem",
        &_exports_ByteArray_templ::_wrap_ByteArray_getitem,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  // jsnapi_member_function_descriptor
  members.erase("setitem");
  members.insert({
    "setitem",
      _exports_ByteArray_templ::InstanceMethod("setitem",
        &_exports_ByteArray_templ::_wrap_ByteArray_setitem,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  // jsnapi_member_function_descriptor
  members.erase("cast");
  members.insert({
    "cast",
      _exports_ByteArray_templ::InstanceMethod("cast",
        &_exports_ByteArray_templ::_wrap_ByteArray_cast,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  
  /* add static class functions and variables */
  // jsnapi_register_static_function
  staticMembers.erase("frompointer");
  staticMembers.insert({
    "frompointer",
      StaticMethod("frompointer",
        &_exports_ByteArray_templ::_wrap_ByteArray_frompointer,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  
}
// jsnapi_class_prologue_template
template <typename SWIG_OBJ_WRAP>
class _exports_HLINHW_JS_templ : public SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP> {
public:
  _exports_HLINHW_JS_templ(const Napi::CallbackInfo &);
_exports_HLINHW_JS_templ(bool, const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_new_HLINHW_JS(const Napi::CallbackInfo &);
virtual ~_exports_HLINHW_JS_templ();
// jsnapi_class_method_declaration
Napi::Value _wrap_HLINHW_JS_getitem(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_HLINHW_JS_setitem(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_HLINHW_JS_cast(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
static Napi::Value _wrap_HLINHW_JS_frompointer(const Napi::CallbackInfo &);
// jsnapi_class_epilogue_template
static void JS_veto_set_static_variable(const Napi::CallbackInfo &, const Napi::Value &);
void JS_veto_set_variable(const Napi::CallbackInfo &, const Napi::Value &);
};

template <typename SWIG_OBJ_WRAP>
void _exports_HLINHW_JS_templ<SWIG_OBJ_WRAP>::JS_veto_set_static_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}

template <typename SWIG_OBJ_WRAP>
void _exports_HLINHW_JS_templ<SWIG_OBJ_WRAP>::JS_veto_set_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}
// jsnapi_class_instance
class _exports_HLINHW_JS_inst : public _exports_HLINHW_JS_templ<_exports_HLINHW_JS_inst> {
public:
  using _exports_HLINHW_JS_templ::_exports_HLINHW_JS_templ;
  virtual ~_exports_HLINHW_JS_inst() {
    
  };
  static void GetMembers(
    Napi::Env,
    std::map<std::string, _exports_HLINHW_JS_templ::PropertyDescriptor> &,
    std::map<std::string, _exports_HLINHW_JS_templ::PropertyDescriptor> &
    );
  static Napi::Function GetClass(Napi::Env);
};
/* Class: HLINHW_JS (_exports_HLINHW_JS) */
// jsnapi_getclass
Napi::Function _exports_HLINHW_JS_inst::GetClass(Napi::Env env) {
  std::map<std::string, _exports_HLINHW_JS_templ::PropertyDescriptor> members, staticMembers;
  GetMembers(env, members, staticMembers);
  
  std::vector<_exports_HLINHW_JS_inst::PropertyDescriptor> symbolTable;
  for (auto it = members.begin(); it != members.end(); it++)
  symbolTable.push_back(it->second);
  for (auto it = staticMembers.begin(); it != staticMembers.end(); it++)
  symbolTable.push_back(it->second);
  
  return Napi::ObjectWrap<_exports_HLINHW_JS_inst>::DefineClass(env, "HLINHW_JS", symbolTable);
}

void _exports_HLINHW_JS_inst::GetMembers(
  Napi::Env env,
  std::map<std::string, _exports_HLINHW_JS_templ::PropertyDescriptor> &members,
  std::map<std::string, _exports_HLINHW_JS_templ::PropertyDescriptor> &staticMembers
  ) {
  std::map<std::string, SWIG_NAPI_ObjectWrap_templ<SWIG_NAPI_ObjectWrap_inst>::PropertyDescriptor> baseMembers, baseStaticMembers;
  SWIG_NAPI_ObjectWrap_inst::GetMembers(env, baseMembers, baseStaticMembers);
  members.insert(baseMembers.begin(), baseMembers.end());
  staticMembers.insert(staticMembers.begin(), staticMembers.end());
  
  /* register wrapper functions */
  // jsnapi_member_function_descriptor
  members.erase("getitem");
  members.insert({
    "getitem",
      _exports_HLINHW_JS_templ::InstanceMethod("getitem",
        &_exports_HLINHW_JS_templ::_wrap_HLINHW_JS_getitem,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  // jsnapi_member_function_descriptor
  members.erase("setitem");
  members.insert({
    "setitem",
      _exports_HLINHW_JS_templ::InstanceMethod("setitem",
        &_exports_HLINHW_JS_templ::_wrap_HLINHW_JS_setitem,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  // jsnapi_member_function_descriptor
  members.erase("cast");
  members.insert({
    "cast",
      _exports_HLINHW_JS_templ::InstanceMethod("cast",
        &_exports_HLINHW_JS_templ::_wrap_HLINHW_JS_cast,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  
  /* add static class functions and variables */
  // jsnapi_register_static_function
  staticMembers.erase("frompointer");
  staticMembers.insert({
    "frompointer",
      StaticMethod("frompointer",
        &_exports_HLINHW_JS_templ::_wrap_HLINHW_JS_frompointer,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  
}
// jsnapi_class_prologue_template
template <typename SWIG_OBJ_WRAP>
class _exports_TLINVersion_templ : public SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP> {
public:
  _exports_TLINVersion_templ(const Napi::CallbackInfo &);
_exports_TLINVersion_templ(bool, const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_TLINVersion_Major_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_TLINVersion_Major_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_TLINVersion_Minor_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_TLINVersion_Minor_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_TLINVersion_Revision_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_TLINVersion_Revision_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_TLINVersion_Build_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_TLINVersion_Build_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_new_TLINVersion(const Napi::CallbackInfo &);
virtual ~_exports_TLINVersion_templ();
// jsnapi_class_epilogue_template
static void JS_veto_set_static_variable(const Napi::CallbackInfo &, const Napi::Value &);
void JS_veto_set_variable(const Napi::CallbackInfo &, const Napi::Value &);
};

template <typename SWIG_OBJ_WRAP>
void _exports_TLINVersion_templ<SWIG_OBJ_WRAP>::JS_veto_set_static_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}

template <typename SWIG_OBJ_WRAP>
void _exports_TLINVersion_templ<SWIG_OBJ_WRAP>::JS_veto_set_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}
// jsnapi_class_instance
class _exports_TLINVersion_inst : public _exports_TLINVersion_templ<_exports_TLINVersion_inst> {
public:
  using _exports_TLINVersion_templ::_exports_TLINVersion_templ;
  virtual ~_exports_TLINVersion_inst() {
    
  };
  static void GetMembers(
    Napi::Env,
    std::map<std::string, _exports_TLINVersion_templ::PropertyDescriptor> &,
    std::map<std::string, _exports_TLINVersion_templ::PropertyDescriptor> &
    );
  static Napi::Function GetClass(Napi::Env);
};
/* Class: TLINVersion (_exports_TLINVersion) */
// jsnapi_getclass
Napi::Function _exports_TLINVersion_inst::GetClass(Napi::Env env) {
  std::map<std::string, _exports_TLINVersion_templ::PropertyDescriptor> members, staticMembers;
  GetMembers(env, members, staticMembers);
  
  std::vector<_exports_TLINVersion_inst::PropertyDescriptor> symbolTable;
  for (auto it = members.begin(); it != members.end(); it++)
  symbolTable.push_back(it->second);
  for (auto it = staticMembers.begin(); it != staticMembers.end(); it++)
  symbolTable.push_back(it->second);
  
  return Napi::ObjectWrap<_exports_TLINVersion_inst>::DefineClass(env, "TLINVersion", symbolTable);
}

void _exports_TLINVersion_inst::GetMembers(
  Napi::Env env,
  std::map<std::string, _exports_TLINVersion_templ::PropertyDescriptor> &members,
  std::map<std::string, _exports_TLINVersion_templ::PropertyDescriptor> &staticMembers
  ) {
  std::map<std::string, SWIG_NAPI_ObjectWrap_templ<SWIG_NAPI_ObjectWrap_inst>::PropertyDescriptor> baseMembers, baseStaticMembers;
  SWIG_NAPI_ObjectWrap_inst::GetMembers(env, baseMembers, baseStaticMembers);
  members.insert(baseMembers.begin(), baseMembers.end());
  staticMembers.insert(staticMembers.begin(), staticMembers.end());
  
  /* register wrapper functions */
  // jsnapi_register_member_variable
  members.erase("Major");
  members.insert({
    "Major",
      _exports_TLINVersion_templ::InstanceAccessor("Major",
        &_exports_TLINVersion_templ::_wrap_TLINVersion_Major_get,
        &_exports_TLINVersion_templ::_wrap_TLINVersion_Major_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("Minor");
  members.insert({
    "Minor",
      _exports_TLINVersion_templ::InstanceAccessor("Minor",
        &_exports_TLINVersion_templ::_wrap_TLINVersion_Minor_get,
        &_exports_TLINVersion_templ::_wrap_TLINVersion_Minor_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("Revision");
  members.insert({
    "Revision",
      _exports_TLINVersion_templ::InstanceAccessor("Revision",
        &_exports_TLINVersion_templ::_wrap_TLINVersion_Revision_get,
        &_exports_TLINVersion_templ::_wrap_TLINVersion_Revision_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("Build");
  members.insert({
    "Build",
      _exports_TLINVersion_templ::InstanceAccessor("Build",
        &_exports_TLINVersion_templ::_wrap_TLINVersion_Build_get,
        &_exports_TLINVersion_templ::_wrap_TLINVersion_Build_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  
  /* add static class functions and variables */
  
}
// jsnapi_class_prologue_template
template <typename SWIG_OBJ_WRAP>
class _exports_TLINMsg_templ : public SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP> {
public:
  _exports_TLINMsg_templ(const Napi::CallbackInfo &);
_exports_TLINMsg_templ(bool, const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_TLINMsg_FrameId_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_TLINMsg_FrameId_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_TLINMsg_Length_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_TLINMsg_Length_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_TLINMsg_Direction_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_TLINMsg_Direction_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_TLINMsg_ChecksumType_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_TLINMsg_ChecksumType_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_TLINMsg_Data_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_TLINMsg_Data_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_TLINMsg_Checksum_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_TLINMsg_Checksum_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_new_TLINMsg(const Napi::CallbackInfo &);
virtual ~_exports_TLINMsg_templ();
// jsnapi_class_epilogue_template
static void JS_veto_set_static_variable(const Napi::CallbackInfo &, const Napi::Value &);
void JS_veto_set_variable(const Napi::CallbackInfo &, const Napi::Value &);
};

template <typename SWIG_OBJ_WRAP>
void _exports_TLINMsg_templ<SWIG_OBJ_WRAP>::JS_veto_set_static_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}

template <typename SWIG_OBJ_WRAP>
void _exports_TLINMsg_templ<SWIG_OBJ_WRAP>::JS_veto_set_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}
// jsnapi_class_instance
class _exports_TLINMsg_inst : public _exports_TLINMsg_templ<_exports_TLINMsg_inst> {
public:
  using _exports_TLINMsg_templ::_exports_TLINMsg_templ;
  virtual ~_exports_TLINMsg_inst() {
    
  };
  static void GetMembers(
    Napi::Env,
    std::map<std::string, _exports_TLINMsg_templ::PropertyDescriptor> &,
    std::map<std::string, _exports_TLINMsg_templ::PropertyDescriptor> &
    );
  static Napi::Function GetClass(Napi::Env);
};
/* Class: TLINMsg (_exports_TLINMsg) */
// jsnapi_getclass
Napi::Function _exports_TLINMsg_inst::GetClass(Napi::Env env) {
  std::map<std::string, _exports_TLINMsg_templ::PropertyDescriptor> members, staticMembers;
  GetMembers(env, members, staticMembers);
  
  std::vector<_exports_TLINMsg_inst::PropertyDescriptor> symbolTable;
  for (auto it = members.begin(); it != members.end(); it++)
  symbolTable.push_back(it->second);
  for (auto it = staticMembers.begin(); it != staticMembers.end(); it++)
  symbolTable.push_back(it->second);
  
  return Napi::ObjectWrap<_exports_TLINMsg_inst>::DefineClass(env, "TLINMsg", symbolTable);
}

void _exports_TLINMsg_inst::GetMembers(
  Napi::Env env,
  std::map<std::string, _exports_TLINMsg_templ::PropertyDescriptor> &members,
  std::map<std::string, _exports_TLINMsg_templ::PropertyDescriptor> &staticMembers
  ) {
  std::map<std::string, SWIG_NAPI_ObjectWrap_templ<SWIG_NAPI_ObjectWrap_inst>::PropertyDescriptor> baseMembers, baseStaticMembers;
  SWIG_NAPI_ObjectWrap_inst::GetMembers(env, baseMembers, baseStaticMembers);
  members.insert(baseMembers.begin(), baseMembers.end());
  staticMembers.insert(staticMembers.begin(), staticMembers.end());
  
  /* register wrapper functions */
  // jsnapi_register_member_variable
  members.erase("FrameId");
  members.insert({
    "FrameId",
      _exports_TLINMsg_templ::InstanceAccessor("FrameId",
        &_exports_TLINMsg_templ::_wrap_TLINMsg_FrameId_get,
        &_exports_TLINMsg_templ::_wrap_TLINMsg_FrameId_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("Length");
  members.insert({
    "Length",
      _exports_TLINMsg_templ::InstanceAccessor("Length",
        &_exports_TLINMsg_templ::_wrap_TLINMsg_Length_get,
        &_exports_TLINMsg_templ::_wrap_TLINMsg_Length_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("Direction");
  members.insert({
    "Direction",
      _exports_TLINMsg_templ::InstanceAccessor("Direction",
        &_exports_TLINMsg_templ::_wrap_TLINMsg_Direction_get,
        &_exports_TLINMsg_templ::_wrap_TLINMsg_Direction_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("ChecksumType");
  members.insert({
    "ChecksumType",
      _exports_TLINMsg_templ::InstanceAccessor("ChecksumType",
        &_exports_TLINMsg_templ::_wrap_TLINMsg_ChecksumType_get,
        &_exports_TLINMsg_templ::_wrap_TLINMsg_ChecksumType_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("Data");
  members.insert({
    "Data",
      _exports_TLINMsg_templ::InstanceAccessor("Data",
        &_exports_TLINMsg_templ::_wrap_TLINMsg_Data_get,
        &_exports_TLINMsg_templ::_wrap_TLINMsg_Data_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("Checksum");
  members.insert({
    "Checksum",
      _exports_TLINMsg_templ::InstanceAccessor("Checksum",
        &_exports_TLINMsg_templ::_wrap_TLINMsg_Checksum_get,
        &_exports_TLINMsg_templ::_wrap_TLINMsg_Checksum_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  
  /* add static class functions and variables */
  
}
// jsnapi_class_prologue_template
template <typename SWIG_OBJ_WRAP>
class _exports_TLINRcvMsg_templ : public SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP> {
public:
  _exports_TLINRcvMsg_templ(const Napi::CallbackInfo &);
_exports_TLINRcvMsg_templ(bool, const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_TLINRcvMsg_Type_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_TLINRcvMsg_Type_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_TLINRcvMsg_FrameId_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_TLINRcvMsg_FrameId_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_TLINRcvMsg_Length_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_TLINRcvMsg_Length_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_TLINRcvMsg_Direction_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_TLINRcvMsg_Direction_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_TLINRcvMsg_ChecksumType_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_TLINRcvMsg_ChecksumType_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_TLINRcvMsg_Data_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_TLINRcvMsg_Data_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_TLINRcvMsg_Checksum_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_TLINRcvMsg_Checksum_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_TLINRcvMsg_ErrorFlags_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_TLINRcvMsg_ErrorFlags_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_TLINRcvMsg_TimeStamp_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_TLINRcvMsg_TimeStamp_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_TLINRcvMsg_hHw_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_TLINRcvMsg_hHw_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_new_TLINRcvMsg(const Napi::CallbackInfo &);
virtual ~_exports_TLINRcvMsg_templ();
// jsnapi_class_epilogue_template
static void JS_veto_set_static_variable(const Napi::CallbackInfo &, const Napi::Value &);
void JS_veto_set_variable(const Napi::CallbackInfo &, const Napi::Value &);
};

template <typename SWIG_OBJ_WRAP>
void _exports_TLINRcvMsg_templ<SWIG_OBJ_WRAP>::JS_veto_set_static_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}

template <typename SWIG_OBJ_WRAP>
void _exports_TLINRcvMsg_templ<SWIG_OBJ_WRAP>::JS_veto_set_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}
// jsnapi_class_instance
class _exports_TLINRcvMsg_inst : public _exports_TLINRcvMsg_templ<_exports_TLINRcvMsg_inst> {
public:
  using _exports_TLINRcvMsg_templ::_exports_TLINRcvMsg_templ;
  virtual ~_exports_TLINRcvMsg_inst() {
    
  };
  static void GetMembers(
    Napi::Env,
    std::map<std::string, _exports_TLINRcvMsg_templ::PropertyDescriptor> &,
    std::map<std::string, _exports_TLINRcvMsg_templ::PropertyDescriptor> &
    );
  static Napi::Function GetClass(Napi::Env);
};
/* Class: TLINRcvMsg (_exports_TLINRcvMsg) */
// jsnapi_getclass
Napi::Function _exports_TLINRcvMsg_inst::GetClass(Napi::Env env) {
  std::map<std::string, _exports_TLINRcvMsg_templ::PropertyDescriptor> members, staticMembers;
  GetMembers(env, members, staticMembers);
  
  std::vector<_exports_TLINRcvMsg_inst::PropertyDescriptor> symbolTable;
  for (auto it = members.begin(); it != members.end(); it++)
  symbolTable.push_back(it->second);
  for (auto it = staticMembers.begin(); it != staticMembers.end(); it++)
  symbolTable.push_back(it->second);
  
  return Napi::ObjectWrap<_exports_TLINRcvMsg_inst>::DefineClass(env, "TLINRcvMsg", symbolTable);
}

void _exports_TLINRcvMsg_inst::GetMembers(
  Napi::Env env,
  std::map<std::string, _exports_TLINRcvMsg_templ::PropertyDescriptor> &members,
  std::map<std::string, _exports_TLINRcvMsg_templ::PropertyDescriptor> &staticMembers
  ) {
  std::map<std::string, SWIG_NAPI_ObjectWrap_templ<SWIG_NAPI_ObjectWrap_inst>::PropertyDescriptor> baseMembers, baseStaticMembers;
  SWIG_NAPI_ObjectWrap_inst::GetMembers(env, baseMembers, baseStaticMembers);
  members.insert(baseMembers.begin(), baseMembers.end());
  staticMembers.insert(staticMembers.begin(), staticMembers.end());
  
  /* register wrapper functions */
  // jsnapi_register_member_variable
  members.erase("Type");
  members.insert({
    "Type",
      _exports_TLINRcvMsg_templ::InstanceAccessor("Type",
        &_exports_TLINRcvMsg_templ::_wrap_TLINRcvMsg_Type_get,
        &_exports_TLINRcvMsg_templ::_wrap_TLINRcvMsg_Type_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("FrameId");
  members.insert({
    "FrameId",
      _exports_TLINRcvMsg_templ::InstanceAccessor("FrameId",
        &_exports_TLINRcvMsg_templ::_wrap_TLINRcvMsg_FrameId_get,
        &_exports_TLINRcvMsg_templ::_wrap_TLINRcvMsg_FrameId_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("Length");
  members.insert({
    "Length",
      _exports_TLINRcvMsg_templ::InstanceAccessor("Length",
        &_exports_TLINRcvMsg_templ::_wrap_TLINRcvMsg_Length_get,
        &_exports_TLINRcvMsg_templ::_wrap_TLINRcvMsg_Length_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("Direction");
  members.insert({
    "Direction",
      _exports_TLINRcvMsg_templ::InstanceAccessor("Direction",
        &_exports_TLINRcvMsg_templ::_wrap_TLINRcvMsg_Direction_get,
        &_exports_TLINRcvMsg_templ::_wrap_TLINRcvMsg_Direction_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("ChecksumType");
  members.insert({
    "ChecksumType",
      _exports_TLINRcvMsg_templ::InstanceAccessor("ChecksumType",
        &_exports_TLINRcvMsg_templ::_wrap_TLINRcvMsg_ChecksumType_get,
        &_exports_TLINRcvMsg_templ::_wrap_TLINRcvMsg_ChecksumType_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("Data");
  members.insert({
    "Data",
      _exports_TLINRcvMsg_templ::InstanceAccessor("Data",
        &_exports_TLINRcvMsg_templ::_wrap_TLINRcvMsg_Data_get,
        &_exports_TLINRcvMsg_templ::_wrap_TLINRcvMsg_Data_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("Checksum");
  members.insert({
    "Checksum",
      _exports_TLINRcvMsg_templ::InstanceAccessor("Checksum",
        &_exports_TLINRcvMsg_templ::_wrap_TLINRcvMsg_Checksum_get,
        &_exports_TLINRcvMsg_templ::_wrap_TLINRcvMsg_Checksum_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("ErrorFlags");
  members.insert({
    "ErrorFlags",
      _exports_TLINRcvMsg_templ::InstanceAccessor("ErrorFlags",
        &_exports_TLINRcvMsg_templ::_wrap_TLINRcvMsg_ErrorFlags_get,
        &_exports_TLINRcvMsg_templ::_wrap_TLINRcvMsg_ErrorFlags_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("TimeStamp");
  members.insert({
    "TimeStamp",
      _exports_TLINRcvMsg_templ::InstanceAccessor("TimeStamp",
        &_exports_TLINRcvMsg_templ::_wrap_TLINRcvMsg_TimeStamp_get,
        &_exports_TLINRcvMsg_templ::_wrap_TLINRcvMsg_TimeStamp_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("hHw");
  members.insert({
    "hHw",
      _exports_TLINRcvMsg_templ::InstanceAccessor("hHw",
        &_exports_TLINRcvMsg_templ::_wrap_TLINRcvMsg_hHw_get,
        &_exports_TLINRcvMsg_templ::_wrap_TLINRcvMsg_hHw_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  
  /* add static class functions and variables */
  
}
// jsnapi_class_prologue_template
template <typename SWIG_OBJ_WRAP>
class _exports_TLINFrameEntry_templ : public SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP> {
public:
  _exports_TLINFrameEntry_templ(const Napi::CallbackInfo &);
_exports_TLINFrameEntry_templ(bool, const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_TLINFrameEntry_FrameId_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_TLINFrameEntry_FrameId_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_TLINFrameEntry_Length_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_TLINFrameEntry_Length_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_TLINFrameEntry_Direction_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_TLINFrameEntry_Direction_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_TLINFrameEntry_ChecksumType_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_TLINFrameEntry_ChecksumType_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_TLINFrameEntry_Flags_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_TLINFrameEntry_Flags_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_TLINFrameEntry_InitialData_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_TLINFrameEntry_InitialData_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_new_TLINFrameEntry(const Napi::CallbackInfo &);
virtual ~_exports_TLINFrameEntry_templ();
// jsnapi_class_epilogue_template
static void JS_veto_set_static_variable(const Napi::CallbackInfo &, const Napi::Value &);
void JS_veto_set_variable(const Napi::CallbackInfo &, const Napi::Value &);
};

template <typename SWIG_OBJ_WRAP>
void _exports_TLINFrameEntry_templ<SWIG_OBJ_WRAP>::JS_veto_set_static_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}

template <typename SWIG_OBJ_WRAP>
void _exports_TLINFrameEntry_templ<SWIG_OBJ_WRAP>::JS_veto_set_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}
// jsnapi_class_instance
class _exports_TLINFrameEntry_inst : public _exports_TLINFrameEntry_templ<_exports_TLINFrameEntry_inst> {
public:
  using _exports_TLINFrameEntry_templ::_exports_TLINFrameEntry_templ;
  virtual ~_exports_TLINFrameEntry_inst() {
    
  };
  static void GetMembers(
    Napi::Env,
    std::map<std::string, _exports_TLINFrameEntry_templ::PropertyDescriptor> &,
    std::map<std::string, _exports_TLINFrameEntry_templ::PropertyDescriptor> &
    );
  static Napi::Function GetClass(Napi::Env);
};
/* Class: TLINFrameEntry (_exports_TLINFrameEntry) */
// jsnapi_getclass
Napi::Function _exports_TLINFrameEntry_inst::GetClass(Napi::Env env) {
  std::map<std::string, _exports_TLINFrameEntry_templ::PropertyDescriptor> members, staticMembers;
  GetMembers(env, members, staticMembers);
  
  std::vector<_exports_TLINFrameEntry_inst::PropertyDescriptor> symbolTable;
  for (auto it = members.begin(); it != members.end(); it++)
  symbolTable.push_back(it->second);
  for (auto it = staticMembers.begin(); it != staticMembers.end(); it++)
  symbolTable.push_back(it->second);
  
  return Napi::ObjectWrap<_exports_TLINFrameEntry_inst>::DefineClass(env, "TLINFrameEntry", symbolTable);
}

void _exports_TLINFrameEntry_inst::GetMembers(
  Napi::Env env,
  std::map<std::string, _exports_TLINFrameEntry_templ::PropertyDescriptor> &members,
  std::map<std::string, _exports_TLINFrameEntry_templ::PropertyDescriptor> &staticMembers
  ) {
  std::map<std::string, SWIG_NAPI_ObjectWrap_templ<SWIG_NAPI_ObjectWrap_inst>::PropertyDescriptor> baseMembers, baseStaticMembers;
  SWIG_NAPI_ObjectWrap_inst::GetMembers(env, baseMembers, baseStaticMembers);
  members.insert(baseMembers.begin(), baseMembers.end());
  staticMembers.insert(staticMembers.begin(), staticMembers.end());
  
  /* register wrapper functions */
  // jsnapi_register_member_variable
  members.erase("FrameId");
  members.insert({
    "FrameId",
      _exports_TLINFrameEntry_templ::InstanceAccessor("FrameId",
        &_exports_TLINFrameEntry_templ::_wrap_TLINFrameEntry_FrameId_get,
        &_exports_TLINFrameEntry_templ::_wrap_TLINFrameEntry_FrameId_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("Length");
  members.insert({
    "Length",
      _exports_TLINFrameEntry_templ::InstanceAccessor("Length",
        &_exports_TLINFrameEntry_templ::_wrap_TLINFrameEntry_Length_get,
        &_exports_TLINFrameEntry_templ::_wrap_TLINFrameEntry_Length_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("Direction");
  members.insert({
    "Direction",
      _exports_TLINFrameEntry_templ::InstanceAccessor("Direction",
        &_exports_TLINFrameEntry_templ::_wrap_TLINFrameEntry_Direction_get,
        &_exports_TLINFrameEntry_templ::_wrap_TLINFrameEntry_Direction_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("ChecksumType");
  members.insert({
    "ChecksumType",
      _exports_TLINFrameEntry_templ::InstanceAccessor("ChecksumType",
        &_exports_TLINFrameEntry_templ::_wrap_TLINFrameEntry_ChecksumType_get,
        &_exports_TLINFrameEntry_templ::_wrap_TLINFrameEntry_ChecksumType_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("Flags");
  members.insert({
    "Flags",
      _exports_TLINFrameEntry_templ::InstanceAccessor("Flags",
        &_exports_TLINFrameEntry_templ::_wrap_TLINFrameEntry_Flags_get,
        &_exports_TLINFrameEntry_templ::_wrap_TLINFrameEntry_Flags_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("InitialData");
  members.insert({
    "InitialData",
      _exports_TLINFrameEntry_templ::InstanceAccessor("InitialData",
        &_exports_TLINFrameEntry_templ::_wrap_TLINFrameEntry_InitialData_get,
        &_exports_TLINFrameEntry_templ::_wrap_TLINFrameEntry_InitialData_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  
  /* add static class functions and variables */
  
}
// jsnapi_class_prologue_template
template <typename SWIG_OBJ_WRAP>
class _exports_TLINScheduleSlot_templ : public SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP> {
public:
  _exports_TLINScheduleSlot_templ(const Napi::CallbackInfo &);
_exports_TLINScheduleSlot_templ(bool, const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_TLINScheduleSlot_Type_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_TLINScheduleSlot_Type_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_TLINScheduleSlot_Delay_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_TLINScheduleSlot_Delay_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_TLINScheduleSlot_FrameId_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_TLINScheduleSlot_FrameId_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_TLINScheduleSlot_CountResolve_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_TLINScheduleSlot_CountResolve_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_TLINScheduleSlot_Handle_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_TLINScheduleSlot_Handle_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_new_TLINScheduleSlot(const Napi::CallbackInfo &);
virtual ~_exports_TLINScheduleSlot_templ();
// jsnapi_class_epilogue_template
static void JS_veto_set_static_variable(const Napi::CallbackInfo &, const Napi::Value &);
void JS_veto_set_variable(const Napi::CallbackInfo &, const Napi::Value &);
};

template <typename SWIG_OBJ_WRAP>
void _exports_TLINScheduleSlot_templ<SWIG_OBJ_WRAP>::JS_veto_set_static_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}

template <typename SWIG_OBJ_WRAP>
void _exports_TLINScheduleSlot_templ<SWIG_OBJ_WRAP>::JS_veto_set_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}
// jsnapi_class_instance
class _exports_TLINScheduleSlot_inst : public _exports_TLINScheduleSlot_templ<_exports_TLINScheduleSlot_inst> {
public:
  using _exports_TLINScheduleSlot_templ::_exports_TLINScheduleSlot_templ;
  virtual ~_exports_TLINScheduleSlot_inst() {
    
  };
  static void GetMembers(
    Napi::Env,
    std::map<std::string, _exports_TLINScheduleSlot_templ::PropertyDescriptor> &,
    std::map<std::string, _exports_TLINScheduleSlot_templ::PropertyDescriptor> &
    );
  static Napi::Function GetClass(Napi::Env);
};
/* Class: TLINScheduleSlot (_exports_TLINScheduleSlot) */
// jsnapi_getclass
Napi::Function _exports_TLINScheduleSlot_inst::GetClass(Napi::Env env) {
  std::map<std::string, _exports_TLINScheduleSlot_templ::PropertyDescriptor> members, staticMembers;
  GetMembers(env, members, staticMembers);
  
  std::vector<_exports_TLINScheduleSlot_inst::PropertyDescriptor> symbolTable;
  for (auto it = members.begin(); it != members.end(); it++)
  symbolTable.push_back(it->second);
  for (auto it = staticMembers.begin(); it != staticMembers.end(); it++)
  symbolTable.push_back(it->second);
  
  return Napi::ObjectWrap<_exports_TLINScheduleSlot_inst>::DefineClass(env, "TLINScheduleSlot", symbolTable);
}

void _exports_TLINScheduleSlot_inst::GetMembers(
  Napi::Env env,
  std::map<std::string, _exports_TLINScheduleSlot_templ::PropertyDescriptor> &members,
  std::map<std::string, _exports_TLINScheduleSlot_templ::PropertyDescriptor> &staticMembers
  ) {
  std::map<std::string, SWIG_NAPI_ObjectWrap_templ<SWIG_NAPI_ObjectWrap_inst>::PropertyDescriptor> baseMembers, baseStaticMembers;
  SWIG_NAPI_ObjectWrap_inst::GetMembers(env, baseMembers, baseStaticMembers);
  members.insert(baseMembers.begin(), baseMembers.end());
  staticMembers.insert(staticMembers.begin(), staticMembers.end());
  
  /* register wrapper functions */
  // jsnapi_register_member_variable
  members.erase("Type");
  members.insert({
    "Type",
      _exports_TLINScheduleSlot_templ::InstanceAccessor("Type",
        &_exports_TLINScheduleSlot_templ::_wrap_TLINScheduleSlot_Type_get,
        &_exports_TLINScheduleSlot_templ::_wrap_TLINScheduleSlot_Type_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("Delay");
  members.insert({
    "Delay",
      _exports_TLINScheduleSlot_templ::InstanceAccessor("Delay",
        &_exports_TLINScheduleSlot_templ::_wrap_TLINScheduleSlot_Delay_get,
        &_exports_TLINScheduleSlot_templ::_wrap_TLINScheduleSlot_Delay_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("FrameId");
  members.insert({
    "FrameId",
      _exports_TLINScheduleSlot_templ::InstanceAccessor("FrameId",
        &_exports_TLINScheduleSlot_templ::_wrap_TLINScheduleSlot_FrameId_get,
        &_exports_TLINScheduleSlot_templ::_wrap_TLINScheduleSlot_FrameId_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("CountResolve");
  members.insert({
    "CountResolve",
      _exports_TLINScheduleSlot_templ::InstanceAccessor("CountResolve",
        &_exports_TLINScheduleSlot_templ::_wrap_TLINScheduleSlot_CountResolve_get,
        &_exports_TLINScheduleSlot_templ::_wrap_TLINScheduleSlot_CountResolve_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("Handle");
  members.insert({
    "Handle",
      _exports_TLINScheduleSlot_templ::InstanceAccessor("Handle",
        &_exports_TLINScheduleSlot_templ::_wrap_TLINScheduleSlot_Handle_get,
        &_exports_TLINScheduleSlot_templ::_wrap_TLINScheduleSlot_Handle_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  
  /* add static class functions and variables */
  
}
// jsnapi_class_prologue_template
template <typename SWIG_OBJ_WRAP>
class _exports_TLINHardwareStatus_templ : public SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP> {
public:
  _exports_TLINHardwareStatus_templ(const Napi::CallbackInfo &);
_exports_TLINHardwareStatus_templ(bool, const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_TLINHardwareStatus_Mode_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_TLINHardwareStatus_Mode_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_TLINHardwareStatus_Status_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_TLINHardwareStatus_Status_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_TLINHardwareStatus_FreeOnSendQueue_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_TLINHardwareStatus_FreeOnSendQueue_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_TLINHardwareStatus_FreeOnSchedulePool_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_TLINHardwareStatus_FreeOnSchedulePool_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_TLINHardwareStatus_ReceiveBufferOverrun_get(const Napi::CallbackInfo &);
// jsnapi_class_setter_declaration
void _wrap_TLINHardwareStatus_ReceiveBufferOverrun_set(const Napi::CallbackInfo &, const Napi::Value &);
// jsnapi_class_method_declaration
Napi::Value _wrap_new_TLINHardwareStatus(const Napi::CallbackInfo &);
virtual ~_exports_TLINHardwareStatus_templ();
// jsnapi_class_epilogue_template
static void JS_veto_set_static_variable(const Napi::CallbackInfo &, const Napi::Value &);
void JS_veto_set_variable(const Napi::CallbackInfo &, const Napi::Value &);
};

template <typename SWIG_OBJ_WRAP>
void _exports_TLINHardwareStatus_templ<SWIG_OBJ_WRAP>::JS_veto_set_static_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}

template <typename SWIG_OBJ_WRAP>
void _exports_TLINHardwareStatus_templ<SWIG_OBJ_WRAP>::JS_veto_set_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}
// jsnapi_class_instance
class _exports_TLINHardwareStatus_inst : public _exports_TLINHardwareStatus_templ<_exports_TLINHardwareStatus_inst> {
public:
  using _exports_TLINHardwareStatus_templ::_exports_TLINHardwareStatus_templ;
  virtual ~_exports_TLINHardwareStatus_inst() {
    
  };
  static void GetMembers(
    Napi::Env,
    std::map<std::string, _exports_TLINHardwareStatus_templ::PropertyDescriptor> &,
    std::map<std::string, _exports_TLINHardwareStatus_templ::PropertyDescriptor> &
    );
  static Napi::Function GetClass(Napi::Env);
};
/* Class: TLINHardwareStatus (_exports_TLINHardwareStatus) */
// jsnapi_getclass
Napi::Function _exports_TLINHardwareStatus_inst::GetClass(Napi::Env env) {
  std::map<std::string, _exports_TLINHardwareStatus_templ::PropertyDescriptor> members, staticMembers;
  GetMembers(env, members, staticMembers);
  
  std::vector<_exports_TLINHardwareStatus_inst::PropertyDescriptor> symbolTable;
  for (auto it = members.begin(); it != members.end(); it++)
  symbolTable.push_back(it->second);
  for (auto it = staticMembers.begin(); it != staticMembers.end(); it++)
  symbolTable.push_back(it->second);
  
  return Napi::ObjectWrap<_exports_TLINHardwareStatus_inst>::DefineClass(env, "TLINHardwareStatus", symbolTable);
}

void _exports_TLINHardwareStatus_inst::GetMembers(
  Napi::Env env,
  std::map<std::string, _exports_TLINHardwareStatus_templ::PropertyDescriptor> &members,
  std::map<std::string, _exports_TLINHardwareStatus_templ::PropertyDescriptor> &staticMembers
  ) {
  std::map<std::string, SWIG_NAPI_ObjectWrap_templ<SWIG_NAPI_ObjectWrap_inst>::PropertyDescriptor> baseMembers, baseStaticMembers;
  SWIG_NAPI_ObjectWrap_inst::GetMembers(env, baseMembers, baseStaticMembers);
  members.insert(baseMembers.begin(), baseMembers.end());
  staticMembers.insert(staticMembers.begin(), staticMembers.end());
  
  /* register wrapper functions */
  // jsnapi_register_member_variable
  members.erase("Mode");
  members.insert({
    "Mode",
      _exports_TLINHardwareStatus_templ::InstanceAccessor("Mode",
        &_exports_TLINHardwareStatus_templ::_wrap_TLINHardwareStatus_Mode_get,
        &_exports_TLINHardwareStatus_templ::_wrap_TLINHardwareStatus_Mode_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("Status");
  members.insert({
    "Status",
      _exports_TLINHardwareStatus_templ::InstanceAccessor("Status",
        &_exports_TLINHardwareStatus_templ::_wrap_TLINHardwareStatus_Status_get,
        &_exports_TLINHardwareStatus_templ::_wrap_TLINHardwareStatus_Status_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("FreeOnSendQueue");
  members.insert({
    "FreeOnSendQueue",
      _exports_TLINHardwareStatus_templ::InstanceAccessor("FreeOnSendQueue",
        &_exports_TLINHardwareStatus_templ::_wrap_TLINHardwareStatus_FreeOnSendQueue_get,
        &_exports_TLINHardwareStatus_templ::_wrap_TLINHardwareStatus_FreeOnSendQueue_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("FreeOnSchedulePool");
  members.insert({
    "FreeOnSchedulePool",
      _exports_TLINHardwareStatus_templ::InstanceAccessor("FreeOnSchedulePool",
        &_exports_TLINHardwareStatus_templ::_wrap_TLINHardwareStatus_FreeOnSchedulePool_get,
        &_exports_TLINHardwareStatus_templ::_wrap_TLINHardwareStatus_FreeOnSchedulePool_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  // jsnapi_register_member_variable
  members.erase("ReceiveBufferOverrun");
  members.insert({
    "ReceiveBufferOverrun",
      _exports_TLINHardwareStatus_templ::InstanceAccessor("ReceiveBufferOverrun",
        &_exports_TLINHardwareStatus_templ::_wrap_TLINHardwareStatus_ReceiveBufferOverrun_get,
        &_exports_TLINHardwareStatus_templ::_wrap_TLINHardwareStatus_ReceiveBufferOverrun_set,
        static_cast<napi_property_attributes>(napi_writable | napi_enumerable | napi_configurable))
    });
  
  /* add static class functions and variables */
  
}





template <typename SWIG_OBJ_WRAP>
// js_ctor
// This is the main constructor
_exports_INT64_JS_templ<SWIG_OBJ_WRAP>::_exports_INT64_JS_templ(const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  Napi::Env env = info.Env();
  
  this->info = SWIGTYPE_p_INT64_JS;
  if (info.Length() == 1 && info[0].IsExternal()) {
    // This constructor has been called internally from C++/SWIG
    // to wrap an already existing C++ object in JS
    this->self = info[0].As<Napi::External<void>>().Data();
    this->owned = false;
    return;
  }
  this->owned = true;
  
  INT64_JS *result;
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_new_INT64_JS.");
  }
  result = (INT64_JS *)new_INT64_JS();
  
  
  this->self = result;
  return;
  goto fail;
fail:
  return;
}

// This is the bypass constructor to be used from child classes
template <typename SWIG_OBJ_WRAP>
_exports_INT64_JS_templ<SWIG_OBJ_WRAP>::_exports_INT64_JS_templ(bool, const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  
}

SWIGINTERN void delete_INT64_JS(INT64_JS *self){
  delete self;
}

// js_dtoroverride
template <typename SWIG_OBJ_WRAP>
_exports_INT64_JS_templ<SWIG_OBJ_WRAP>::~_exports_INT64_JS_templ() {
  auto arg1 = reinterpret_cast<INT64_JS *>(this->self);
  if (this->owned && arg1) {
    delete_INT64_JS(arg1);
    this->self = nullptr;
  }
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_INT64_JS_templ<SWIG_OBJ_WRAP>::_wrap_INT64_JS_assign(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  INT64_JS *arg1 = (INT64_JS *) 0 ;
  __int64 arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  long long val2 ;
  int ecode2 = 0 ;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_INT64_JS_assign.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_INT64_JS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "INT64_JS_assign" "', argument " "1"" of type '" "INT64_JS *""'"); 
  }
  arg1 = reinterpret_cast< INT64_JS * >(argp1);ecode2 = SWIG_AsVal_long_SS_long(info[0], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "INT64_JS_assign" "', argument " "2"" of type '" "__int64""'");
  } 
  arg2 = static_cast< __int64 >(val2);INT64_JS_assign(arg1,SWIG_STD_MOVE(arg2));
  jsresult = env.Undefined();
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_INT64_JS_templ<SWIG_OBJ_WRAP>::_wrap_INT64_JS_value(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  INT64_JS *arg1 = (INT64_JS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  __int64 result;
  
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_INT64_JS_value.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_INT64_JS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "INT64_JS_value" "', argument " "1"" of type '" "INT64_JS *""'"); 
  }
  arg1 = reinterpret_cast< INT64_JS * >(argp1);result = INT64_JS_value(arg1);
  jsresult = SWIG_From_long_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< long long >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_INT64_JS_templ<SWIG_OBJ_WRAP>::_wrap_INT64_JS_cast(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  INT64_JS *arg1 = (INT64_JS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  __int64 *result = 0 ;
  
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_INT64_JS_cast.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_INT64_JS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "INT64_JS_cast" "', argument " "1"" of type '" "INT64_JS *""'"); 
  }
  arg1 = reinterpret_cast< INT64_JS * >(argp1);result = (__int64 *)INT64_JS_cast(arg1);
  jsresult = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p___int64, 0 |  0 );
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_INT64_JS_templ<SWIG_OBJ_WRAP>::_wrap_INT64_JS_frompointer(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  __int64 *arg1 = (__int64 *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  INT64_JS *result = 0 ;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_INT64_JS_frompointer.");
  }
  
  res1 = SWIG_ConvertPtr(info[0], &argp1,SWIGTYPE_p___int64, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "INT64_JS_frompointer" "', argument " "1"" of type '" "__int64 *""'"); 
  }
  arg1 = reinterpret_cast< __int64 * >(argp1);result = (INT64_JS *)INT64_JS_frompointer(arg1);
  jsresult = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_INT64_JS, 0 |  0 );
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


template <typename SWIG_OBJ_WRAP>
// js_ctor
// This is the main constructor
_exports_INT_JS_templ<SWIG_OBJ_WRAP>::_exports_INT_JS_templ(const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  Napi::Env env = info.Env();
  
  this->info = SWIGTYPE_p_INT_JS;
  if (info.Length() == 1 && info[0].IsExternal()) {
    // This constructor has been called internally from C++/SWIG
    // to wrap an already existing C++ object in JS
    this->self = info[0].As<Napi::External<void>>().Data();
    this->owned = false;
    return;
  }
  this->owned = true;
  
  INT_JS *result;
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_new_INT_JS.");
  }
  result = (INT_JS *)new_INT_JS();
  
  
  this->self = result;
  return;
  goto fail;
fail:
  return;
}

// This is the bypass constructor to be used from child classes
template <typename SWIG_OBJ_WRAP>
_exports_INT_JS_templ<SWIG_OBJ_WRAP>::_exports_INT_JS_templ(bool, const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  
}

SWIGINTERN void delete_INT_JS(INT_JS *self){
  delete self;
}

// js_dtoroverride
template <typename SWIG_OBJ_WRAP>
_exports_INT_JS_templ<SWIG_OBJ_WRAP>::~_exports_INT_JS_templ() {
  auto arg1 = reinterpret_cast<INT_JS *>(this->self);
  if (this->owned && arg1) {
    delete_INT_JS(arg1);
    this->self = nullptr;
  }
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_INT_JS_templ<SWIG_OBJ_WRAP>::_wrap_INT_JS_assign(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  INT_JS *arg1 = (INT_JS *) 0 ;
  int arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int val2 ;
  int ecode2 = 0 ;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_INT_JS_assign.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_INT_JS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "INT_JS_assign" "', argument " "1"" of type '" "INT_JS *""'"); 
  }
  arg1 = reinterpret_cast< INT_JS * >(argp1);ecode2 = SWIG_AsVal_int(info[0], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "INT_JS_assign" "', argument " "2"" of type '" "int""'");
  } 
  arg2 = static_cast< int >(val2);INT_JS_assign(arg1,arg2);
  jsresult = env.Undefined();
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_INT_JS_templ<SWIG_OBJ_WRAP>::_wrap_INT_JS_value(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  INT_JS *arg1 = (INT_JS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int result;
  
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_INT_JS_value.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_INT_JS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "INT_JS_value" "', argument " "1"" of type '" "INT_JS *""'"); 
  }
  arg1 = reinterpret_cast< INT_JS * >(argp1);result = (int)INT_JS_value(arg1);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_INT_JS_templ<SWIG_OBJ_WRAP>::_wrap_INT_JS_cast(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  INT_JS *arg1 = (INT_JS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int *result = 0 ;
  
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_INT_JS_cast.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_INT_JS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "INT_JS_cast" "', argument " "1"" of type '" "INT_JS *""'"); 
  }
  arg1 = reinterpret_cast< INT_JS * >(argp1);result = (int *)INT_JS_cast(arg1);
  jsresult = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_int, 0 |  0 );
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_INT_JS_templ<SWIG_OBJ_WRAP>::_wrap_INT_JS_frompointer(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  int *arg1 = (int *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  INT_JS *result = 0 ;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_INT_JS_frompointer.");
  }
  
  res1 = SWIG_ConvertPtr(info[0], &argp1,SWIGTYPE_p_int, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "INT_JS_frompointer" "', argument " "1"" of type '" "int *""'"); 
  }
  arg1 = reinterpret_cast< int * >(argp1);result = (INT_JS *)INT_JS_frompointer(arg1);
  jsresult = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_INT_JS, 0 |  0 );
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


template <typename SWIG_OBJ_WRAP>
// js_ctor
// This is the main constructor
_exports_HLINCLIENT_JS_templ<SWIG_OBJ_WRAP>::_exports_HLINCLIENT_JS_templ(const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  Napi::Env env = info.Env();
  
  this->info = SWIGTYPE_p_HLINCLIENT_JS;
  if (info.Length() == 1 && info[0].IsExternal()) {
    // This constructor has been called internally from C++/SWIG
    // to wrap an already existing C++ object in JS
    this->self = info[0].As<Napi::External<void>>().Data();
    this->owned = false;
    return;
  }
  this->owned = true;
  
  HLINCLIENT_JS *result;
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_new_HLINCLIENT_JS.");
  }
  result = (HLINCLIENT_JS *)new_HLINCLIENT_JS();
  
  
  this->self = result;
  return;
  goto fail;
fail:
  return;
}

// This is the bypass constructor to be used from child classes
template <typename SWIG_OBJ_WRAP>
_exports_HLINCLIENT_JS_templ<SWIG_OBJ_WRAP>::_exports_HLINCLIENT_JS_templ(bool, const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  
}

SWIGINTERN void delete_HLINCLIENT_JS(HLINCLIENT_JS *self){
  delete self;
}

// js_dtoroverride
template <typename SWIG_OBJ_WRAP>
_exports_HLINCLIENT_JS_templ<SWIG_OBJ_WRAP>::~_exports_HLINCLIENT_JS_templ() {
  auto arg1 = reinterpret_cast<HLINCLIENT_JS *>(this->self);
  if (this->owned && arg1) {
    delete_HLINCLIENT_JS(arg1);
    this->self = nullptr;
  }
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_HLINCLIENT_JS_templ<SWIG_OBJ_WRAP>::_wrap_HLINCLIENT_JS_assign(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINCLIENT_JS *arg1 = (HLINCLIENT_JS *) 0 ;
  HLINCLIENT arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_HLINCLIENT_JS_assign.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HLINCLIENT_JS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HLINCLIENT_JS_assign" "', argument " "1"" of type '" "HLINCLIENT_JS *""'"); 
  }
  arg1 = reinterpret_cast< HLINCLIENT_JS * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(info[0], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "HLINCLIENT_JS_assign" "', argument " "2"" of type '" "HLINCLIENT""'");
  } 
  arg2 = static_cast< HLINCLIENT >(val2);HLINCLIENT_JS_assign(arg1,arg2);
  jsresult = env.Undefined();
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_HLINCLIENT_JS_templ<SWIG_OBJ_WRAP>::_wrap_HLINCLIENT_JS_value(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINCLIENT_JS *arg1 = (HLINCLIENT_JS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  HLINCLIENT result;
  
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_HLINCLIENT_JS_value.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HLINCLIENT_JS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HLINCLIENT_JS_value" "', argument " "1"" of type '" "HLINCLIENT_JS *""'"); 
  }
  arg1 = reinterpret_cast< HLINCLIENT_JS * >(argp1);result = (HLINCLIENT)HLINCLIENT_JS_value(arg1);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_HLINCLIENT_JS_templ<SWIG_OBJ_WRAP>::_wrap_HLINCLIENT_JS_cast(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINCLIENT_JS *arg1 = (HLINCLIENT_JS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  HLINCLIENT *result = 0 ;
  
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_HLINCLIENT_JS_cast.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HLINCLIENT_JS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HLINCLIENT_JS_cast" "', argument " "1"" of type '" "HLINCLIENT_JS *""'"); 
  }
  arg1 = reinterpret_cast< HLINCLIENT_JS * >(argp1);result = (HLINCLIENT *)HLINCLIENT_JS_cast(arg1);
  jsresult = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_unsigned_char, 0 |  0 );
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_HLINCLIENT_JS_templ<SWIG_OBJ_WRAP>::_wrap_HLINCLIENT_JS_frompointer(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINCLIENT *arg1 = (HLINCLIENT *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  HLINCLIENT_JS *result = 0 ;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_HLINCLIENT_JS_frompointer.");
  }
  
  res1 = SWIG_ConvertPtr(info[0], &argp1,SWIGTYPE_p_unsigned_char, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HLINCLIENT_JS_frompointer" "', argument " "1"" of type '" "HLINCLIENT *""'"); 
  }
  arg1 = reinterpret_cast< HLINCLIENT * >(argp1);result = (HLINCLIENT_JS *)HLINCLIENT_JS_frompointer(arg1);
  jsresult = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_HLINCLIENT_JS, 0 |  0 );
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


template <typename SWIG_OBJ_WRAP>
// js_ctor
// This is the main constructor
_exports_ByteArray_templ<SWIG_OBJ_WRAP>::_exports_ByteArray_templ(const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  Napi::Env env = info.Env();
  
  this->info = SWIGTYPE_p_ByteArray;
  if (info.Length() == 1 && info[0].IsExternal()) {
    // This constructor has been called internally from C++/SWIG
    // to wrap an already existing C++ object in JS
    this->self = info[0].As<Napi::External<void>>().Data();
    this->owned = false;
    return;
  }
  this->owned = true;
  
  size_t arg1 ;
  size_t val1 ;
  int ecode1 = 0 ;
  ByteArray *result;
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_new_ByteArray.");
  }
  ecode1 = SWIG_AsVal_size_t(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "new_ByteArray" "', argument " "1"" of type '" "size_t""'");
  } 
  arg1 = static_cast< size_t >(val1);result = (ByteArray *)new_ByteArray(SWIG_STD_MOVE(arg1));
  
  
  
  this->self = result;
  return;
  goto fail;
fail:
  return;
}

// This is the bypass constructor to be used from child classes
template <typename SWIG_OBJ_WRAP>
_exports_ByteArray_templ<SWIG_OBJ_WRAP>::_exports_ByteArray_templ(bool, const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  
}

SWIGINTERN void delete_ByteArray(ByteArray *self){
  delete [] self;
}

// js_dtoroverride
template <typename SWIG_OBJ_WRAP>
_exports_ByteArray_templ<SWIG_OBJ_WRAP>::~_exports_ByteArray_templ() {
  auto arg1 = reinterpret_cast<ByteArray *>(this->self);
  if (this->owned && arg1) {
    delete_ByteArray(arg1);
    this->self = nullptr;
  }
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_ByteArray_templ<SWIG_OBJ_WRAP>::_wrap_ByteArray_getitem(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  ByteArray *arg1 = (ByteArray *) 0 ;
  size_t arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  size_t val2 ;
  int ecode2 = 0 ;
  BYTE result;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_ByteArray_getitem.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_ByteArray, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "ByteArray_getitem" "', argument " "1"" of type '" "ByteArray *""'"); 
  }
  arg1 = reinterpret_cast< ByteArray * >(argp1);ecode2 = SWIG_AsVal_size_t(info[0], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "ByteArray_getitem" "', argument " "2"" of type '" "size_t""'");
  } 
  arg2 = static_cast< size_t >(val2);result = (BYTE)ByteArray_getitem(arg1,SWIG_STD_MOVE(arg2));
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_ByteArray_templ<SWIG_OBJ_WRAP>::_wrap_ByteArray_setitem(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  ByteArray *arg1 = (ByteArray *) 0 ;
  size_t arg2 ;
  BYTE arg3 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  size_t val2 ;
  int ecode2 = 0 ;
  unsigned char val3 ;
  int ecode3 = 0 ;
  
  if(static_cast<int>(info.Length()) < 2 || static_cast<int>(info.Length()) > 2) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_ByteArray_setitem.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_ByteArray, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "ByteArray_setitem" "', argument " "1"" of type '" "ByteArray *""'"); 
  }
  arg1 = reinterpret_cast< ByteArray * >(argp1);ecode2 = SWIG_AsVal_size_t(info[0], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "ByteArray_setitem" "', argument " "2"" of type '" "size_t""'");
  } 
  arg2 = static_cast< size_t >(val2);ecode3 = SWIG_AsVal_unsigned_SS_char(info[1], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "ByteArray_setitem" "', argument " "3"" of type '" "BYTE""'");
  } 
  arg3 = static_cast< BYTE >(val3);ByteArray_setitem(arg1,SWIG_STD_MOVE(arg2),arg3);
  jsresult = env.Undefined();
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_ByteArray_templ<SWIG_OBJ_WRAP>::_wrap_ByteArray_cast(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  ByteArray *arg1 = (ByteArray *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  BYTE *result = 0 ;
  
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_ByteArray_cast.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_ByteArray, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "ByteArray_cast" "', argument " "1"" of type '" "ByteArray *""'"); 
  }
  arg1 = reinterpret_cast< ByteArray * >(argp1);result = (BYTE *)ByteArray_cast(arg1);
  jsresult = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_unsigned_char, 0 |  0 );
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_ByteArray_templ<SWIG_OBJ_WRAP>::_wrap_ByteArray_frompointer(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  BYTE *arg1 = (BYTE *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  ByteArray *result = 0 ;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_ByteArray_frompointer.");
  }
  
  res1 = SWIG_ConvertPtr(info[0], &argp1,SWIGTYPE_p_unsigned_char, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "ByteArray_frompointer" "', argument " "1"" of type '" "BYTE *""'"); 
  }
  arg1 = reinterpret_cast< BYTE * >(argp1);result = (ByteArray *)ByteArray_frompointer(arg1);
  jsresult = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_ByteArray, 0 |  0 );
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


template <typename SWIG_OBJ_WRAP>
// js_ctor
// This is the main constructor
_exports_HLINHW_JS_templ<SWIG_OBJ_WRAP>::_exports_HLINHW_JS_templ(const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  Napi::Env env = info.Env();
  
  this->info = SWIGTYPE_p_HLINHW_JS;
  if (info.Length() == 1 && info[0].IsExternal()) {
    // This constructor has been called internally from C++/SWIG
    // to wrap an already existing C++ object in JS
    this->self = info[0].As<Napi::External<void>>().Data();
    this->owned = false;
    return;
  }
  this->owned = true;
  
  size_t arg1 ;
  size_t val1 ;
  int ecode1 = 0 ;
  HLINHW_JS *result;
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_new_HLINHW_JS.");
  }
  ecode1 = SWIG_AsVal_size_t(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "new_HLINHW_JS" "', argument " "1"" of type '" "size_t""'");
  } 
  arg1 = static_cast< size_t >(val1);result = (HLINHW_JS *)new_HLINHW_JS(SWIG_STD_MOVE(arg1));
  
  
  
  this->self = result;
  return;
  goto fail;
fail:
  return;
}

// This is the bypass constructor to be used from child classes
template <typename SWIG_OBJ_WRAP>
_exports_HLINHW_JS_templ<SWIG_OBJ_WRAP>::_exports_HLINHW_JS_templ(bool, const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  
}

SWIGINTERN void delete_HLINHW_JS(HLINHW_JS *self){
  delete [] self;
}

// js_dtoroverride
template <typename SWIG_OBJ_WRAP>
_exports_HLINHW_JS_templ<SWIG_OBJ_WRAP>::~_exports_HLINHW_JS_templ() {
  auto arg1 = reinterpret_cast<HLINHW_JS *>(this->self);
  if (this->owned && arg1) {
    delete_HLINHW_JS(arg1);
    this->self = nullptr;
  }
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_HLINHW_JS_templ<SWIG_OBJ_WRAP>::_wrap_HLINHW_JS_getitem(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINHW_JS *arg1 = (HLINHW_JS *) 0 ;
  size_t arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  size_t val2 ;
  int ecode2 = 0 ;
  HLINHW result;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_HLINHW_JS_getitem.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HLINHW_JS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HLINHW_JS_getitem" "', argument " "1"" of type '" "HLINHW_JS *""'"); 
  }
  arg1 = reinterpret_cast< HLINHW_JS * >(argp1);ecode2 = SWIG_AsVal_size_t(info[0], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "HLINHW_JS_getitem" "', argument " "2"" of type '" "size_t""'");
  } 
  arg2 = static_cast< size_t >(val2);result = (HLINHW)HLINHW_JS_getitem(arg1,SWIG_STD_MOVE(arg2));
  jsresult = SWIG_From_unsigned_SS_short  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned short >(result));
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_HLINHW_JS_templ<SWIG_OBJ_WRAP>::_wrap_HLINHW_JS_setitem(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINHW_JS *arg1 = (HLINHW_JS *) 0 ;
  size_t arg2 ;
  HLINHW arg3 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  size_t val2 ;
  int ecode2 = 0 ;
  unsigned short val3 ;
  int ecode3 = 0 ;
  
  if(static_cast<int>(info.Length()) < 2 || static_cast<int>(info.Length()) > 2) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_HLINHW_JS_setitem.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HLINHW_JS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HLINHW_JS_setitem" "', argument " "1"" of type '" "HLINHW_JS *""'"); 
  }
  arg1 = reinterpret_cast< HLINHW_JS * >(argp1);ecode2 = SWIG_AsVal_size_t(info[0], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "HLINHW_JS_setitem" "', argument " "2"" of type '" "size_t""'");
  } 
  arg2 = static_cast< size_t >(val2);ecode3 = SWIG_AsVal_unsigned_SS_short(info[1], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "HLINHW_JS_setitem" "', argument " "3"" of type '" "HLINHW""'");
  } 
  arg3 = static_cast< HLINHW >(val3);HLINHW_JS_setitem(arg1,SWIG_STD_MOVE(arg2),arg3);
  jsresult = env.Undefined();
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_HLINHW_JS_templ<SWIG_OBJ_WRAP>::_wrap_HLINHW_JS_cast(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINHW_JS *arg1 = (HLINHW_JS *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  HLINHW *result = 0 ;
  
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_HLINHW_JS_cast.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_HLINHW_JS, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HLINHW_JS_cast" "', argument " "1"" of type '" "HLINHW_JS *""'"); 
  }
  arg1 = reinterpret_cast< HLINHW_JS * >(argp1);result = (HLINHW *)HLINHW_JS_cast(arg1);
  jsresult = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_unsigned_short, 0 |  0 );
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_HLINHW_JS_templ<SWIG_OBJ_WRAP>::_wrap_HLINHW_JS_frompointer(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINHW *arg1 = (HLINHW *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  HLINHW_JS *result = 0 ;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_HLINHW_JS_frompointer.");
  }
  
  res1 = SWIG_ConvertPtr(info[0], &argp1,SWIGTYPE_p_unsigned_short, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "HLINHW_JS_frompointer" "', argument " "1"" of type '" "HLINHW *""'"); 
  }
  arg1 = reinterpret_cast< HLINHW * >(argp1);result = (HLINHW_JS *)HLINHW_JS_frompointer(arg1);
  jsresult = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_HLINHW_JS, 0 |  0 );
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_INVALID_LIN_HANDLE_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(0));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_HW_TYPE_USB_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(1));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_HW_TYPE_USB_PRO_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(1));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_HW_TYPE_USB_PRO_FD_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(2));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_HW_TYPE_PLIN_USB_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(3));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_MAX_FRAME_ID_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(63));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_MAX_SCHEDULES_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(8));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_MIN_SCHEDULE_NUMBER_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(0));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_MAX_SCHEDULE_NUMBER_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(7));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_MAX_SCHEDULE_SLOTS_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(256));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_MIN_BAUDRATE_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(1000));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_MAX_BAUDRATE_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(20000));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_MAX_NAME_LENGTH_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(48));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_MAX_USER_DATA_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(24));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_MIN_BREAK_LENGTH_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(13));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_MAX_BREAK_LENGTH_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(32));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LIN_MAX_RCV_QUEUE_COUNT_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(65535));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_FRAME_FLAG_RESPONSE_ENABLE_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(0x1));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_FRAME_FLAG_SINGLE_SHOT_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(0x2));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_FRAME_FLAG_IGNORE_INIT_DATA_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(0x4));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LOG_FLAG_DEFAULT_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_unsigned_SS_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned int >(0x00U));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LOG_FLAG_ENTRY_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_unsigned_SS_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned int >(0x01U));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LOG_FLAG_PARAMETERS_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_unsigned_SS_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned int >(0x02U));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LOG_FLAG_LEAVE_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_unsigned_SS_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned int >(0x04U));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LOG_FLAG_WRITE_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_unsigned_SS_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned int >(0x08U));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LOG_FLAG_READ_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_unsigned_SS_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned int >(0x10U));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_LOG_FLAG_ALL_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_unsigned_SS_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned int >(0xFFFFU));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_MSG_ERR_INCONSISTENT_SYNC_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(0x1));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_MSG_ERR_ID_PARITY_BIT0_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(0x2));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_MSG_ERR_ID_PARITY_BIT1_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(0x4));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_MSG_ERR_SLAVE_NOT_RESPONDING_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(0x8));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_MSG_ERR_TIMEOUT_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(0x10));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_MSG_ERR_CHECKSUM_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(0x20));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_MSG_ERR_GND_SHORT_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(0x40));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_MSG_ERR_VBAT_SHORT_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(0x80));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_MSG_ERR_SLOT_DELAY_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(0x100));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_MSG_ERR_OTHER_RESPONSE_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(0x200));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_clpName_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(1));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_clpMessagesOnQueue_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(2));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_clpWindowHandle_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(3));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_clpConnectedHardware_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(4));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_clpTransmittedMessages_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(5));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_clpReceivedMessages_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(6));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_clpReceiveStatusFrames_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(7));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_clpOnReceiveEventHandle_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(8));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_clpOnPluginEventHandle_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(9));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_clpLogStatus_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(10));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_clpLogConfiguration_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(11));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_hwpName_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(1));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_hwpDeviceNumber_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(2));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_hwpChannelNumber_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(3));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_hwpConnectedClients_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(4));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_hwpMessageFilter_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(5));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_hwpBaudrate_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(6));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_hwpMode_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(7));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_hwpFirmwareVersion_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(8));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_hwpBufferOverrunCount_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(9));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_hwpBossClient_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(10));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_hwpSerialNumber_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(11));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_hwpVersion_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(12));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_hwpType_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(13));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_hwpQueueOverrunCount_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(14));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_hwpIdNumber_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(15));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_hwpUserData_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(16));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_hwpBreakLength_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(17));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_hwpLinTermination_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(18));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_hwpFlashMode_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(19));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_hwpScheduleActive_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(20));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_hwpScheduleState_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(21));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_hwpScheduleSuspendedSlot_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(22));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_mstStandard_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(0));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_mstBusSleep_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(1));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_mstBusWakeUp_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(2));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_mstAutobaudrateTimeOut_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(3));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_mstAutobaudrateReply_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(4));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_mstOverrun_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(5));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_mstQueueOverrun_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(6));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_mstClientQueueOverrun_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(7));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_sltUnconditional_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(0));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_sltEvent_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(1));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_sltSporadic_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(2));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_sltMasterRequest_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(3));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_sltSlaveResponse_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(4));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_dirDisabled_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(0));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_dirPublisher_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(1));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_dirSubscriber_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(2));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_dirSubscriberAutoLength_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(3));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_cstCustom_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(0));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_cstClassic_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(1));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_cstEnhanced_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(2));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_cstAuto_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(3));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_modNone_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(0));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_modSlave_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(1));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_modMaster_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(2));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_hwsNotInitialized_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(0));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_hwsAutobaudrate_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(1));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_hwsActive_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(2));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_hwsSleep_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(3));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_hwsShortGround_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(6));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_hwsVBatMissing_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(7));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_schNotRunning_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(0));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_schSuspended_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(1));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_schRunning_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(2));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_errOK_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(0));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_errXmtQueueFull_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(1));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_errIllegalPeriod_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(2));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_errRcvQueueEmpty_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(3));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_errIllegalChecksumType_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(4));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_errIllegalHardware_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(5));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_errIllegalClient_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(6));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_errWrongParameterType_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(7));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_errWrongParameterValue_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(8));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_errIllegalDirection_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(9));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_errIllegalLength_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(10));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_errIllegalBaudrate_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(11));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_errIllegalFrameID_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(12));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_errBufferInsufficient_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(13));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_errIllegalScheduleNo_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(14));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_errIllegalSlotCount_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(15));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_errIllegalIndex_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(16));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_errIllegalRange_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(17));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_errIllegalHardwareState_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(18));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_errIllegalSchedulerState_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(19));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_errIllegalFrameConfiguration_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(20));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_errScheduleSlotPoolFull_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(21));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_errIllegalSchedule_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(22));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_errIllegalHardwareMode_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(23));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_errOutOfResource_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(1001));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_errManagerNotLoaded_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(1002));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_errManagerNotResponding_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(1003));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_errMemoryAccess_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(1004));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_errNotImplemented_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(0xFFFE));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_getter
Napi::Value exports_errUnknown_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(0xFFFF));
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_TLINVersion_templ<SWIG_OBJ_WRAP>::_wrap_TLINVersion_Major_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINVersion *arg1 = (TLINVersion *) 0 ;
  short arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  short val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINVersion, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINVersion_Major_set" "', argument " "1"" of type '" "TLINVersion *""'"); 
  }
  arg1 = reinterpret_cast< TLINVersion * >(argp1);ecode2 = SWIG_AsVal_short(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "TLINVersion_Major_set" "', argument " "2"" of type '" "short""'");
  } 
  arg2 = static_cast< short >(val2);if (arg1) (arg1)->Major = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_TLINVersion_templ<SWIG_OBJ_WRAP>::_wrap_TLINVersion_Major_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINVersion *arg1 = (TLINVersion *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  short result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINVersion, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINVersion_Major_get" "', argument " "1"" of type '" "TLINVersion *""'"); 
  }
  arg1 = reinterpret_cast< TLINVersion * >(argp1);result = (short) ((arg1)->Major);
  jsresult = SWIG_From_short  SWIG_NAPI_FROM_CALL_ARGS(static_cast< short >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_TLINVersion_templ<SWIG_OBJ_WRAP>::_wrap_TLINVersion_Minor_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINVersion *arg1 = (TLINVersion *) 0 ;
  short arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  short val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINVersion, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINVersion_Minor_set" "', argument " "1"" of type '" "TLINVersion *""'"); 
  }
  arg1 = reinterpret_cast< TLINVersion * >(argp1);ecode2 = SWIG_AsVal_short(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "TLINVersion_Minor_set" "', argument " "2"" of type '" "short""'");
  } 
  arg2 = static_cast< short >(val2);if (arg1) (arg1)->Minor = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_TLINVersion_templ<SWIG_OBJ_WRAP>::_wrap_TLINVersion_Minor_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINVersion *arg1 = (TLINVersion *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  short result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINVersion, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINVersion_Minor_get" "', argument " "1"" of type '" "TLINVersion *""'"); 
  }
  arg1 = reinterpret_cast< TLINVersion * >(argp1);result = (short) ((arg1)->Minor);
  jsresult = SWIG_From_short  SWIG_NAPI_FROM_CALL_ARGS(static_cast< short >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_TLINVersion_templ<SWIG_OBJ_WRAP>::_wrap_TLINVersion_Revision_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINVersion *arg1 = (TLINVersion *) 0 ;
  short arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  short val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINVersion, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINVersion_Revision_set" "', argument " "1"" of type '" "TLINVersion *""'"); 
  }
  arg1 = reinterpret_cast< TLINVersion * >(argp1);ecode2 = SWIG_AsVal_short(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "TLINVersion_Revision_set" "', argument " "2"" of type '" "short""'");
  } 
  arg2 = static_cast< short >(val2);if (arg1) (arg1)->Revision = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_TLINVersion_templ<SWIG_OBJ_WRAP>::_wrap_TLINVersion_Revision_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINVersion *arg1 = (TLINVersion *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  short result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINVersion, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINVersion_Revision_get" "', argument " "1"" of type '" "TLINVersion *""'"); 
  }
  arg1 = reinterpret_cast< TLINVersion * >(argp1);result = (short) ((arg1)->Revision);
  jsresult = SWIG_From_short  SWIG_NAPI_FROM_CALL_ARGS(static_cast< short >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_TLINVersion_templ<SWIG_OBJ_WRAP>::_wrap_TLINVersion_Build_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINVersion *arg1 = (TLINVersion *) 0 ;
  short arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  short val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINVersion, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINVersion_Build_set" "', argument " "1"" of type '" "TLINVersion *""'"); 
  }
  arg1 = reinterpret_cast< TLINVersion * >(argp1);ecode2 = SWIG_AsVal_short(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "TLINVersion_Build_set" "', argument " "2"" of type '" "short""'");
  } 
  arg2 = static_cast< short >(val2);if (arg1) (arg1)->Build = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_TLINVersion_templ<SWIG_OBJ_WRAP>::_wrap_TLINVersion_Build_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINVersion *arg1 = (TLINVersion *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  short result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINVersion, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINVersion_Build_get" "', argument " "1"" of type '" "TLINVersion *""'"); 
  }
  arg1 = reinterpret_cast< TLINVersion * >(argp1);result = (short) ((arg1)->Build);
  jsresult = SWIG_From_short  SWIG_NAPI_FROM_CALL_ARGS(static_cast< short >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


template <typename SWIG_OBJ_WRAP>
// js_ctor
// This is the main constructor
_exports_TLINVersion_templ<SWIG_OBJ_WRAP>::_exports_TLINVersion_templ(const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  Napi::Env env = info.Env();
  
  this->info = SWIGTYPE_p_TLINVersion;
  if (info.Length() == 1 && info[0].IsExternal()) {
    // This constructor has been called internally from C++/SWIG
    // to wrap an already existing C++ object in JS
    this->self = info[0].As<Napi::External<void>>().Data();
    this->owned = false;
    return;
  }
  this->owned = true;
  
  TLINVersion *result;
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_new_TLINVersion.");
  }
  result = (TLINVersion *)new TLINVersion();
  
  
  this->self = result;
  return;
  goto fail;
fail:
  return;
}

// This is the bypass constructor to be used from child classes
template <typename SWIG_OBJ_WRAP>
_exports_TLINVersion_templ<SWIG_OBJ_WRAP>::_exports_TLINVersion_templ(bool, const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  
}


// js_dtoroverride
template <typename SWIG_OBJ_WRAP>
_exports_TLINVersion_templ<SWIG_OBJ_WRAP>::~_exports_TLINVersion_templ() {
  auto arg1 = reinterpret_cast<TLINVersion *>(this->self);
  if (this->owned && arg1) {
    delete arg1;
    this->self = nullptr;
  }
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_TLINMsg_templ<SWIG_OBJ_WRAP>::_wrap_TLINMsg_FrameId_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINMsg *arg1 = (TLINMsg *) 0 ;
  BYTE arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINMsg, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINMsg_FrameId_set" "', argument " "1"" of type '" "TLINMsg *""'"); 
  }
  arg1 = reinterpret_cast< TLINMsg * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "TLINMsg_FrameId_set" "', argument " "2"" of type '" "BYTE""'");
  } 
  arg2 = static_cast< BYTE >(val2);if (arg1) (arg1)->FrameId = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_TLINMsg_templ<SWIG_OBJ_WRAP>::_wrap_TLINMsg_FrameId_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINMsg *arg1 = (TLINMsg *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  BYTE result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINMsg, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINMsg_FrameId_get" "', argument " "1"" of type '" "TLINMsg *""'"); 
  }
  arg1 = reinterpret_cast< TLINMsg * >(argp1);result = (BYTE) ((arg1)->FrameId);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_TLINMsg_templ<SWIG_OBJ_WRAP>::_wrap_TLINMsg_Length_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINMsg *arg1 = (TLINMsg *) 0 ;
  BYTE arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINMsg, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINMsg_Length_set" "', argument " "1"" of type '" "TLINMsg *""'"); 
  }
  arg1 = reinterpret_cast< TLINMsg * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "TLINMsg_Length_set" "', argument " "2"" of type '" "BYTE""'");
  } 
  arg2 = static_cast< BYTE >(val2);if (arg1) (arg1)->Length = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_TLINMsg_templ<SWIG_OBJ_WRAP>::_wrap_TLINMsg_Length_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINMsg *arg1 = (TLINMsg *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  BYTE result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINMsg, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINMsg_Length_get" "', argument " "1"" of type '" "TLINMsg *""'"); 
  }
  arg1 = reinterpret_cast< TLINMsg * >(argp1);result = (BYTE) ((arg1)->Length);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_TLINMsg_templ<SWIG_OBJ_WRAP>::_wrap_TLINMsg_Direction_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINMsg *arg1 = (TLINMsg *) 0 ;
  BYTE arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINMsg, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINMsg_Direction_set" "', argument " "1"" of type '" "TLINMsg *""'"); 
  }
  arg1 = reinterpret_cast< TLINMsg * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "TLINMsg_Direction_set" "', argument " "2"" of type '" "BYTE""'");
  } 
  arg2 = static_cast< BYTE >(val2);if (arg1) (arg1)->Direction = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_TLINMsg_templ<SWIG_OBJ_WRAP>::_wrap_TLINMsg_Direction_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINMsg *arg1 = (TLINMsg *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  BYTE result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINMsg, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINMsg_Direction_get" "', argument " "1"" of type '" "TLINMsg *""'"); 
  }
  arg1 = reinterpret_cast< TLINMsg * >(argp1);result = (BYTE) ((arg1)->Direction);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_TLINMsg_templ<SWIG_OBJ_WRAP>::_wrap_TLINMsg_ChecksumType_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINMsg *arg1 = (TLINMsg *) 0 ;
  BYTE arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINMsg, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINMsg_ChecksumType_set" "', argument " "1"" of type '" "TLINMsg *""'"); 
  }
  arg1 = reinterpret_cast< TLINMsg * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "TLINMsg_ChecksumType_set" "', argument " "2"" of type '" "BYTE""'");
  } 
  arg2 = static_cast< BYTE >(val2);if (arg1) (arg1)->ChecksumType = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_TLINMsg_templ<SWIG_OBJ_WRAP>::_wrap_TLINMsg_ChecksumType_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINMsg *arg1 = (TLINMsg *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  BYTE result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINMsg, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINMsg_ChecksumType_get" "', argument " "1"" of type '" "TLINMsg *""'"); 
  }
  arg1 = reinterpret_cast< TLINMsg * >(argp1);result = (BYTE) ((arg1)->ChecksumType);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_TLINMsg_templ<SWIG_OBJ_WRAP>::_wrap_TLINMsg_Data_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINMsg *arg1 = (TLINMsg *) 0 ;
  BYTE *arg2 = (BYTE *) (BYTE *)0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  void *argp2 = 0 ;
  int res2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINMsg, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINMsg_Data_set" "', argument " "1"" of type '" "TLINMsg *""'"); 
  }
  arg1 = reinterpret_cast< TLINMsg * >(argp1);res2 = SWIG_ConvertPtr(value, &argp2,SWIGTYPE_p_unsigned_char, 0 |  0 );
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "TLINMsg_Data_set" "', argument " "2"" of type '" "BYTE [8]""'"); 
  } 
  arg2 = reinterpret_cast< BYTE * >(argp2);{
    if (arg2) {
      size_t ii = 0;
      for (; ii < (size_t)8; ++ii) *(BYTE *)&arg1->Data[ii] = *((BYTE *)arg2 + ii);
    } else {
      SWIG_exception_fail(SWIG_ValueError, "invalid null reference " "in variable '""Data""' of type '""BYTE [8]""'");
    }
  }
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_TLINMsg_templ<SWIG_OBJ_WRAP>::_wrap_TLINMsg_Data_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINMsg *arg1 = (TLINMsg *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  BYTE *result = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINMsg, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINMsg_Data_get" "', argument " "1"" of type '" "TLINMsg *""'"); 
  }
  arg1 = reinterpret_cast< TLINMsg * >(argp1);result = (BYTE *)(BYTE *) ((arg1)->Data);
  jsresult = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_unsigned_char, 0 |  0 );
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_TLINMsg_templ<SWIG_OBJ_WRAP>::_wrap_TLINMsg_Checksum_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINMsg *arg1 = (TLINMsg *) 0 ;
  BYTE arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINMsg, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINMsg_Checksum_set" "', argument " "1"" of type '" "TLINMsg *""'"); 
  }
  arg1 = reinterpret_cast< TLINMsg * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "TLINMsg_Checksum_set" "', argument " "2"" of type '" "BYTE""'");
  } 
  arg2 = static_cast< BYTE >(val2);if (arg1) (arg1)->Checksum = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_TLINMsg_templ<SWIG_OBJ_WRAP>::_wrap_TLINMsg_Checksum_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINMsg *arg1 = (TLINMsg *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  BYTE result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINMsg, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINMsg_Checksum_get" "', argument " "1"" of type '" "TLINMsg *""'"); 
  }
  arg1 = reinterpret_cast< TLINMsg * >(argp1);result = (BYTE) ((arg1)->Checksum);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


template <typename SWIG_OBJ_WRAP>
// js_ctor
// This is the main constructor
_exports_TLINMsg_templ<SWIG_OBJ_WRAP>::_exports_TLINMsg_templ(const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  Napi::Env env = info.Env();
  
  this->info = SWIGTYPE_p_TLINMsg;
  if (info.Length() == 1 && info[0].IsExternal()) {
    // This constructor has been called internally from C++/SWIG
    // to wrap an already existing C++ object in JS
    this->self = info[0].As<Napi::External<void>>().Data();
    this->owned = false;
    return;
  }
  this->owned = true;
  
  TLINMsg *result;
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_new_TLINMsg.");
  }
  result = (TLINMsg *)new TLINMsg();
  
  
  this->self = result;
  return;
  goto fail;
fail:
  return;
}

// This is the bypass constructor to be used from child classes
template <typename SWIG_OBJ_WRAP>
_exports_TLINMsg_templ<SWIG_OBJ_WRAP>::_exports_TLINMsg_templ(bool, const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  
}


// js_dtoroverride
template <typename SWIG_OBJ_WRAP>
_exports_TLINMsg_templ<SWIG_OBJ_WRAP>::~_exports_TLINMsg_templ() {
  auto arg1 = reinterpret_cast<TLINMsg *>(this->self);
  if (this->owned && arg1) {
    delete arg1;
    this->self = nullptr;
  }
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_TLINRcvMsg_templ<SWIG_OBJ_WRAP>::_wrap_TLINRcvMsg_Type_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINRcvMsg *arg1 = (TLINRcvMsg *) 0 ;
  BYTE arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINRcvMsg, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINRcvMsg_Type_set" "', argument " "1"" of type '" "TLINRcvMsg *""'"); 
  }
  arg1 = reinterpret_cast< TLINRcvMsg * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "TLINRcvMsg_Type_set" "', argument " "2"" of type '" "BYTE""'");
  } 
  arg2 = static_cast< BYTE >(val2);if (arg1) (arg1)->Type = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_TLINRcvMsg_templ<SWIG_OBJ_WRAP>::_wrap_TLINRcvMsg_Type_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINRcvMsg *arg1 = (TLINRcvMsg *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  BYTE result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINRcvMsg, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINRcvMsg_Type_get" "', argument " "1"" of type '" "TLINRcvMsg *""'"); 
  }
  arg1 = reinterpret_cast< TLINRcvMsg * >(argp1);result = (BYTE) ((arg1)->Type);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_TLINRcvMsg_templ<SWIG_OBJ_WRAP>::_wrap_TLINRcvMsg_FrameId_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINRcvMsg *arg1 = (TLINRcvMsg *) 0 ;
  BYTE arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINRcvMsg, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINRcvMsg_FrameId_set" "', argument " "1"" of type '" "TLINRcvMsg *""'"); 
  }
  arg1 = reinterpret_cast< TLINRcvMsg * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "TLINRcvMsg_FrameId_set" "', argument " "2"" of type '" "BYTE""'");
  } 
  arg2 = static_cast< BYTE >(val2);if (arg1) (arg1)->FrameId = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_TLINRcvMsg_templ<SWIG_OBJ_WRAP>::_wrap_TLINRcvMsg_FrameId_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINRcvMsg *arg1 = (TLINRcvMsg *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  BYTE result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINRcvMsg, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINRcvMsg_FrameId_get" "', argument " "1"" of type '" "TLINRcvMsg *""'"); 
  }
  arg1 = reinterpret_cast< TLINRcvMsg * >(argp1);result = (BYTE) ((arg1)->FrameId);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_TLINRcvMsg_templ<SWIG_OBJ_WRAP>::_wrap_TLINRcvMsg_Length_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINRcvMsg *arg1 = (TLINRcvMsg *) 0 ;
  BYTE arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINRcvMsg, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINRcvMsg_Length_set" "', argument " "1"" of type '" "TLINRcvMsg *""'"); 
  }
  arg1 = reinterpret_cast< TLINRcvMsg * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "TLINRcvMsg_Length_set" "', argument " "2"" of type '" "BYTE""'");
  } 
  arg2 = static_cast< BYTE >(val2);if (arg1) (arg1)->Length = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_TLINRcvMsg_templ<SWIG_OBJ_WRAP>::_wrap_TLINRcvMsg_Length_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINRcvMsg *arg1 = (TLINRcvMsg *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  BYTE result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINRcvMsg, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINRcvMsg_Length_get" "', argument " "1"" of type '" "TLINRcvMsg *""'"); 
  }
  arg1 = reinterpret_cast< TLINRcvMsg * >(argp1);result = (BYTE) ((arg1)->Length);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_TLINRcvMsg_templ<SWIG_OBJ_WRAP>::_wrap_TLINRcvMsg_Direction_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINRcvMsg *arg1 = (TLINRcvMsg *) 0 ;
  BYTE arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINRcvMsg, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINRcvMsg_Direction_set" "', argument " "1"" of type '" "TLINRcvMsg *""'"); 
  }
  arg1 = reinterpret_cast< TLINRcvMsg * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "TLINRcvMsg_Direction_set" "', argument " "2"" of type '" "BYTE""'");
  } 
  arg2 = static_cast< BYTE >(val2);if (arg1) (arg1)->Direction = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_TLINRcvMsg_templ<SWIG_OBJ_WRAP>::_wrap_TLINRcvMsg_Direction_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINRcvMsg *arg1 = (TLINRcvMsg *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  BYTE result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINRcvMsg, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINRcvMsg_Direction_get" "', argument " "1"" of type '" "TLINRcvMsg *""'"); 
  }
  arg1 = reinterpret_cast< TLINRcvMsg * >(argp1);result = (BYTE) ((arg1)->Direction);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_TLINRcvMsg_templ<SWIG_OBJ_WRAP>::_wrap_TLINRcvMsg_ChecksumType_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINRcvMsg *arg1 = (TLINRcvMsg *) 0 ;
  BYTE arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINRcvMsg, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINRcvMsg_ChecksumType_set" "', argument " "1"" of type '" "TLINRcvMsg *""'"); 
  }
  arg1 = reinterpret_cast< TLINRcvMsg * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "TLINRcvMsg_ChecksumType_set" "', argument " "2"" of type '" "BYTE""'");
  } 
  arg2 = static_cast< BYTE >(val2);if (arg1) (arg1)->ChecksumType = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_TLINRcvMsg_templ<SWIG_OBJ_WRAP>::_wrap_TLINRcvMsg_ChecksumType_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINRcvMsg *arg1 = (TLINRcvMsg *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  BYTE result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINRcvMsg, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINRcvMsg_ChecksumType_get" "', argument " "1"" of type '" "TLINRcvMsg *""'"); 
  }
  arg1 = reinterpret_cast< TLINRcvMsg * >(argp1);result = (BYTE) ((arg1)->ChecksumType);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_TLINRcvMsg_templ<SWIG_OBJ_WRAP>::_wrap_TLINRcvMsg_Data_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINRcvMsg *arg1 = (TLINRcvMsg *) 0 ;
  BYTE *arg2 = (BYTE *) (BYTE *)0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  void *argp2 = 0 ;
  int res2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINRcvMsg, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINRcvMsg_Data_set" "', argument " "1"" of type '" "TLINRcvMsg *""'"); 
  }
  arg1 = reinterpret_cast< TLINRcvMsg * >(argp1);res2 = SWIG_ConvertPtr(value, &argp2,SWIGTYPE_p_unsigned_char, 0 |  0 );
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "TLINRcvMsg_Data_set" "', argument " "2"" of type '" "BYTE [8]""'"); 
  } 
  arg2 = reinterpret_cast< BYTE * >(argp2);{
    if (arg2) {
      size_t ii = 0;
      for (; ii < (size_t)8; ++ii) *(BYTE *)&arg1->Data[ii] = *((BYTE *)arg2 + ii);
    } else {
      SWIG_exception_fail(SWIG_ValueError, "invalid null reference " "in variable '""Data""' of type '""BYTE [8]""'");
    }
  }
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_TLINRcvMsg_templ<SWIG_OBJ_WRAP>::_wrap_TLINRcvMsg_Data_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINRcvMsg *arg1 = (TLINRcvMsg *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  BYTE *result = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINRcvMsg, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINRcvMsg_Data_get" "', argument " "1"" of type '" "TLINRcvMsg *""'"); 
  }
  arg1 = reinterpret_cast< TLINRcvMsg * >(argp1);result = (BYTE *)(BYTE *) ((arg1)->Data);
  jsresult = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_unsigned_char, 0 |  0 );
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_TLINRcvMsg_templ<SWIG_OBJ_WRAP>::_wrap_TLINRcvMsg_Checksum_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINRcvMsg *arg1 = (TLINRcvMsg *) 0 ;
  BYTE arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINRcvMsg, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINRcvMsg_Checksum_set" "', argument " "1"" of type '" "TLINRcvMsg *""'"); 
  }
  arg1 = reinterpret_cast< TLINRcvMsg * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "TLINRcvMsg_Checksum_set" "', argument " "2"" of type '" "BYTE""'");
  } 
  arg2 = static_cast< BYTE >(val2);if (arg1) (arg1)->Checksum = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_TLINRcvMsg_templ<SWIG_OBJ_WRAP>::_wrap_TLINRcvMsg_Checksum_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINRcvMsg *arg1 = (TLINRcvMsg *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  BYTE result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINRcvMsg, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINRcvMsg_Checksum_get" "', argument " "1"" of type '" "TLINRcvMsg *""'"); 
  }
  arg1 = reinterpret_cast< TLINRcvMsg * >(argp1);result = (BYTE) ((arg1)->Checksum);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_TLINRcvMsg_templ<SWIG_OBJ_WRAP>::_wrap_TLINRcvMsg_ErrorFlags_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINRcvMsg *arg1 = (TLINRcvMsg *) 0 ;
  INT32 arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINRcvMsg, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINRcvMsg_ErrorFlags_set" "', argument " "1"" of type '" "TLINRcvMsg *""'"); 
  }
  arg1 = reinterpret_cast< TLINRcvMsg * >(argp1);ecode2 = SWIG_AsVal_int(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "TLINRcvMsg_ErrorFlags_set" "', argument " "2"" of type '" "INT32""'");
  } 
  arg2 = static_cast< INT32 >(val2);if (arg1) (arg1)->ErrorFlags = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_TLINRcvMsg_templ<SWIG_OBJ_WRAP>::_wrap_TLINRcvMsg_ErrorFlags_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINRcvMsg *arg1 = (TLINRcvMsg *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  INT32 result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINRcvMsg, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINRcvMsg_ErrorFlags_get" "', argument " "1"" of type '" "TLINRcvMsg *""'"); 
  }
  arg1 = reinterpret_cast< TLINRcvMsg * >(argp1);result = (INT32) ((arg1)->ErrorFlags);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_TLINRcvMsg_templ<SWIG_OBJ_WRAP>::_wrap_TLINRcvMsg_TimeStamp_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINRcvMsg *arg1 = (TLINRcvMsg *) 0 ;
  unsigned __int64 arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned long long val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINRcvMsg, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINRcvMsg_TimeStamp_set" "', argument " "1"" of type '" "TLINRcvMsg *""'"); 
  }
  arg1 = reinterpret_cast< TLINRcvMsg * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_long_SS_long(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "TLINRcvMsg_TimeStamp_set" "', argument " "2"" of type '" "unsigned __int64""'");
  } 
  arg2 = static_cast< unsigned __int64 >(val2);if (arg1) (arg1)->TimeStamp = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_TLINRcvMsg_templ<SWIG_OBJ_WRAP>::_wrap_TLINRcvMsg_TimeStamp_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINRcvMsg *arg1 = (TLINRcvMsg *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned __int64 result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINRcvMsg, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINRcvMsg_TimeStamp_get" "', argument " "1"" of type '" "TLINRcvMsg *""'"); 
  }
  arg1 = reinterpret_cast< TLINRcvMsg * >(argp1);result =  ((arg1)->TimeStamp);
  jsresult = SWIG_From_unsigned_SS_long_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long long >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_TLINRcvMsg_templ<SWIG_OBJ_WRAP>::_wrap_TLINRcvMsg_hHw_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINRcvMsg *arg1 = (TLINRcvMsg *) 0 ;
  HLINHW arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned short val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINRcvMsg, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINRcvMsg_hHw_set" "', argument " "1"" of type '" "TLINRcvMsg *""'"); 
  }
  arg1 = reinterpret_cast< TLINRcvMsg * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_short(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "TLINRcvMsg_hHw_set" "', argument " "2"" of type '" "HLINHW""'");
  } 
  arg2 = static_cast< HLINHW >(val2);if (arg1) (arg1)->hHw = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_TLINRcvMsg_templ<SWIG_OBJ_WRAP>::_wrap_TLINRcvMsg_hHw_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINRcvMsg *arg1 = (TLINRcvMsg *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  HLINHW result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINRcvMsg, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINRcvMsg_hHw_get" "', argument " "1"" of type '" "TLINRcvMsg *""'"); 
  }
  arg1 = reinterpret_cast< TLINRcvMsg * >(argp1);result = (HLINHW) ((arg1)->hHw);
  jsresult = SWIG_From_unsigned_SS_short  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned short >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


template <typename SWIG_OBJ_WRAP>
// js_ctor
// This is the main constructor
_exports_TLINRcvMsg_templ<SWIG_OBJ_WRAP>::_exports_TLINRcvMsg_templ(const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  Napi::Env env = info.Env();
  
  this->info = SWIGTYPE_p_TLINRcvMsg;
  if (info.Length() == 1 && info[0].IsExternal()) {
    // This constructor has been called internally from C++/SWIG
    // to wrap an already existing C++ object in JS
    this->self = info[0].As<Napi::External<void>>().Data();
    this->owned = false;
    return;
  }
  this->owned = true;
  
  TLINRcvMsg *result;
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_new_TLINRcvMsg.");
  }
  result = (TLINRcvMsg *)new TLINRcvMsg();
  
  
  this->self = result;
  return;
  goto fail;
fail:
  return;
}

// This is the bypass constructor to be used from child classes
template <typename SWIG_OBJ_WRAP>
_exports_TLINRcvMsg_templ<SWIG_OBJ_WRAP>::_exports_TLINRcvMsg_templ(bool, const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  
}


// js_dtoroverride
template <typename SWIG_OBJ_WRAP>
_exports_TLINRcvMsg_templ<SWIG_OBJ_WRAP>::~_exports_TLINRcvMsg_templ() {
  auto arg1 = reinterpret_cast<TLINRcvMsg *>(this->self);
  if (this->owned && arg1) {
    delete arg1;
    this->self = nullptr;
  }
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_TLINFrameEntry_templ<SWIG_OBJ_WRAP>::_wrap_TLINFrameEntry_FrameId_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINFrameEntry *arg1 = (TLINFrameEntry *) 0 ;
  BYTE arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINFrameEntry, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINFrameEntry_FrameId_set" "', argument " "1"" of type '" "TLINFrameEntry *""'"); 
  }
  arg1 = reinterpret_cast< TLINFrameEntry * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "TLINFrameEntry_FrameId_set" "', argument " "2"" of type '" "BYTE""'");
  } 
  arg2 = static_cast< BYTE >(val2);if (arg1) (arg1)->FrameId = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_TLINFrameEntry_templ<SWIG_OBJ_WRAP>::_wrap_TLINFrameEntry_FrameId_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINFrameEntry *arg1 = (TLINFrameEntry *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  BYTE result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINFrameEntry, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINFrameEntry_FrameId_get" "', argument " "1"" of type '" "TLINFrameEntry *""'"); 
  }
  arg1 = reinterpret_cast< TLINFrameEntry * >(argp1);result = (BYTE) ((arg1)->FrameId);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_TLINFrameEntry_templ<SWIG_OBJ_WRAP>::_wrap_TLINFrameEntry_Length_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINFrameEntry *arg1 = (TLINFrameEntry *) 0 ;
  BYTE arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINFrameEntry, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINFrameEntry_Length_set" "', argument " "1"" of type '" "TLINFrameEntry *""'"); 
  }
  arg1 = reinterpret_cast< TLINFrameEntry * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "TLINFrameEntry_Length_set" "', argument " "2"" of type '" "BYTE""'");
  } 
  arg2 = static_cast< BYTE >(val2);if (arg1) (arg1)->Length = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_TLINFrameEntry_templ<SWIG_OBJ_WRAP>::_wrap_TLINFrameEntry_Length_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINFrameEntry *arg1 = (TLINFrameEntry *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  BYTE result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINFrameEntry, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINFrameEntry_Length_get" "', argument " "1"" of type '" "TLINFrameEntry *""'"); 
  }
  arg1 = reinterpret_cast< TLINFrameEntry * >(argp1);result = (BYTE) ((arg1)->Length);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_TLINFrameEntry_templ<SWIG_OBJ_WRAP>::_wrap_TLINFrameEntry_Direction_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINFrameEntry *arg1 = (TLINFrameEntry *) 0 ;
  BYTE arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINFrameEntry, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINFrameEntry_Direction_set" "', argument " "1"" of type '" "TLINFrameEntry *""'"); 
  }
  arg1 = reinterpret_cast< TLINFrameEntry * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "TLINFrameEntry_Direction_set" "', argument " "2"" of type '" "BYTE""'");
  } 
  arg2 = static_cast< BYTE >(val2);if (arg1) (arg1)->Direction = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_TLINFrameEntry_templ<SWIG_OBJ_WRAP>::_wrap_TLINFrameEntry_Direction_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINFrameEntry *arg1 = (TLINFrameEntry *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  BYTE result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINFrameEntry, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINFrameEntry_Direction_get" "', argument " "1"" of type '" "TLINFrameEntry *""'"); 
  }
  arg1 = reinterpret_cast< TLINFrameEntry * >(argp1);result = (BYTE) ((arg1)->Direction);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_TLINFrameEntry_templ<SWIG_OBJ_WRAP>::_wrap_TLINFrameEntry_ChecksumType_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINFrameEntry *arg1 = (TLINFrameEntry *) 0 ;
  BYTE arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINFrameEntry, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINFrameEntry_ChecksumType_set" "', argument " "1"" of type '" "TLINFrameEntry *""'"); 
  }
  arg1 = reinterpret_cast< TLINFrameEntry * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "TLINFrameEntry_ChecksumType_set" "', argument " "2"" of type '" "BYTE""'");
  } 
  arg2 = static_cast< BYTE >(val2);if (arg1) (arg1)->ChecksumType = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_TLINFrameEntry_templ<SWIG_OBJ_WRAP>::_wrap_TLINFrameEntry_ChecksumType_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINFrameEntry *arg1 = (TLINFrameEntry *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  BYTE result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINFrameEntry, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINFrameEntry_ChecksumType_get" "', argument " "1"" of type '" "TLINFrameEntry *""'"); 
  }
  arg1 = reinterpret_cast< TLINFrameEntry * >(argp1);result = (BYTE) ((arg1)->ChecksumType);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_TLINFrameEntry_templ<SWIG_OBJ_WRAP>::_wrap_TLINFrameEntry_Flags_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINFrameEntry *arg1 = (TLINFrameEntry *) 0 ;
  WORD arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned short val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINFrameEntry, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINFrameEntry_Flags_set" "', argument " "1"" of type '" "TLINFrameEntry *""'"); 
  }
  arg1 = reinterpret_cast< TLINFrameEntry * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_short(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "TLINFrameEntry_Flags_set" "', argument " "2"" of type '" "WORD""'");
  } 
  arg2 = static_cast< WORD >(val2);if (arg1) (arg1)->Flags = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_TLINFrameEntry_templ<SWIG_OBJ_WRAP>::_wrap_TLINFrameEntry_Flags_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINFrameEntry *arg1 = (TLINFrameEntry *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  WORD result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINFrameEntry, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINFrameEntry_Flags_get" "', argument " "1"" of type '" "TLINFrameEntry *""'"); 
  }
  arg1 = reinterpret_cast< TLINFrameEntry * >(argp1);result = (WORD) ((arg1)->Flags);
  jsresult = SWIG_From_unsigned_SS_short  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned short >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_TLINFrameEntry_templ<SWIG_OBJ_WRAP>::_wrap_TLINFrameEntry_InitialData_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINFrameEntry *arg1 = (TLINFrameEntry *) 0 ;
  BYTE *arg2 = (BYTE *) (BYTE *)0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  void *argp2 = 0 ;
  int res2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINFrameEntry, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINFrameEntry_InitialData_set" "', argument " "1"" of type '" "TLINFrameEntry *""'"); 
  }
  arg1 = reinterpret_cast< TLINFrameEntry * >(argp1);res2 = SWIG_ConvertPtr(value, &argp2,SWIGTYPE_p_unsigned_char, 0 |  0 );
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "TLINFrameEntry_InitialData_set" "', argument " "2"" of type '" "BYTE [8]""'"); 
  } 
  arg2 = reinterpret_cast< BYTE * >(argp2);{
    if (arg2) {
      size_t ii = 0;
      for (; ii < (size_t)8; ++ii) *(BYTE *)&arg1->InitialData[ii] = *((BYTE *)arg2 + ii);
    } else {
      SWIG_exception_fail(SWIG_ValueError, "invalid null reference " "in variable '""InitialData""' of type '""BYTE [8]""'");
    }
  }
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_TLINFrameEntry_templ<SWIG_OBJ_WRAP>::_wrap_TLINFrameEntry_InitialData_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINFrameEntry *arg1 = (TLINFrameEntry *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  BYTE *result = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINFrameEntry, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINFrameEntry_InitialData_get" "', argument " "1"" of type '" "TLINFrameEntry *""'"); 
  }
  arg1 = reinterpret_cast< TLINFrameEntry * >(argp1);result = (BYTE *)(BYTE *) ((arg1)->InitialData);
  jsresult = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_unsigned_char, 0 |  0 );
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


template <typename SWIG_OBJ_WRAP>
// js_ctor
// This is the main constructor
_exports_TLINFrameEntry_templ<SWIG_OBJ_WRAP>::_exports_TLINFrameEntry_templ(const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  Napi::Env env = info.Env();
  
  this->info = SWIGTYPE_p_TLINFrameEntry;
  if (info.Length() == 1 && info[0].IsExternal()) {
    // This constructor has been called internally from C++/SWIG
    // to wrap an already existing C++ object in JS
    this->self = info[0].As<Napi::External<void>>().Data();
    this->owned = false;
    return;
  }
  this->owned = true;
  
  TLINFrameEntry *result;
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_new_TLINFrameEntry.");
  }
  result = (TLINFrameEntry *)new TLINFrameEntry();
  
  
  this->self = result;
  return;
  goto fail;
fail:
  return;
}

// This is the bypass constructor to be used from child classes
template <typename SWIG_OBJ_WRAP>
_exports_TLINFrameEntry_templ<SWIG_OBJ_WRAP>::_exports_TLINFrameEntry_templ(bool, const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  
}


// js_dtoroverride
template <typename SWIG_OBJ_WRAP>
_exports_TLINFrameEntry_templ<SWIG_OBJ_WRAP>::~_exports_TLINFrameEntry_templ() {
  auto arg1 = reinterpret_cast<TLINFrameEntry *>(this->self);
  if (this->owned && arg1) {
    delete arg1;
    this->self = nullptr;
  }
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_TLINScheduleSlot_templ<SWIG_OBJ_WRAP>::_wrap_TLINScheduleSlot_Type_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINScheduleSlot *arg1 = (TLINScheduleSlot *) 0 ;
  BYTE arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINScheduleSlot, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINScheduleSlot_Type_set" "', argument " "1"" of type '" "TLINScheduleSlot *""'"); 
  }
  arg1 = reinterpret_cast< TLINScheduleSlot * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "TLINScheduleSlot_Type_set" "', argument " "2"" of type '" "BYTE""'");
  } 
  arg2 = static_cast< BYTE >(val2);if (arg1) (arg1)->Type = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_TLINScheduleSlot_templ<SWIG_OBJ_WRAP>::_wrap_TLINScheduleSlot_Type_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINScheduleSlot *arg1 = (TLINScheduleSlot *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  BYTE result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINScheduleSlot, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINScheduleSlot_Type_get" "', argument " "1"" of type '" "TLINScheduleSlot *""'"); 
  }
  arg1 = reinterpret_cast< TLINScheduleSlot * >(argp1);result = (BYTE) ((arg1)->Type);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_TLINScheduleSlot_templ<SWIG_OBJ_WRAP>::_wrap_TLINScheduleSlot_Delay_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINScheduleSlot *arg1 = (TLINScheduleSlot *) 0 ;
  WORD arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned short val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINScheduleSlot, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINScheduleSlot_Delay_set" "', argument " "1"" of type '" "TLINScheduleSlot *""'"); 
  }
  arg1 = reinterpret_cast< TLINScheduleSlot * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_short(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "TLINScheduleSlot_Delay_set" "', argument " "2"" of type '" "WORD""'");
  } 
  arg2 = static_cast< WORD >(val2);if (arg1) (arg1)->Delay = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_TLINScheduleSlot_templ<SWIG_OBJ_WRAP>::_wrap_TLINScheduleSlot_Delay_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINScheduleSlot *arg1 = (TLINScheduleSlot *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  WORD result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINScheduleSlot, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINScheduleSlot_Delay_get" "', argument " "1"" of type '" "TLINScheduleSlot *""'"); 
  }
  arg1 = reinterpret_cast< TLINScheduleSlot * >(argp1);result = (WORD) ((arg1)->Delay);
  jsresult = SWIG_From_unsigned_SS_short  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned short >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_TLINScheduleSlot_templ<SWIG_OBJ_WRAP>::_wrap_TLINScheduleSlot_FrameId_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINScheduleSlot *arg1 = (TLINScheduleSlot *) 0 ;
  BYTE *arg2 = (BYTE *) (BYTE *)0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  void *argp2 = 0 ;
  int res2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINScheduleSlot, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINScheduleSlot_FrameId_set" "', argument " "1"" of type '" "TLINScheduleSlot *""'"); 
  }
  arg1 = reinterpret_cast< TLINScheduleSlot * >(argp1);res2 = SWIG_ConvertPtr(value, &argp2,SWIGTYPE_p_unsigned_char, 0 |  0 );
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "TLINScheduleSlot_FrameId_set" "', argument " "2"" of type '" "BYTE [8]""'"); 
  } 
  arg2 = reinterpret_cast< BYTE * >(argp2);{
    if (arg2) {
      size_t ii = 0;
      for (; ii < (size_t)8; ++ii) *(BYTE *)&arg1->FrameId[ii] = *((BYTE *)arg2 + ii);
    } else {
      SWIG_exception_fail(SWIG_ValueError, "invalid null reference " "in variable '""FrameId""' of type '""BYTE [8]""'");
    }
  }
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_TLINScheduleSlot_templ<SWIG_OBJ_WRAP>::_wrap_TLINScheduleSlot_FrameId_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINScheduleSlot *arg1 = (TLINScheduleSlot *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  BYTE *result = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINScheduleSlot, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINScheduleSlot_FrameId_get" "', argument " "1"" of type '" "TLINScheduleSlot *""'"); 
  }
  arg1 = reinterpret_cast< TLINScheduleSlot * >(argp1);result = (BYTE *)(BYTE *) ((arg1)->FrameId);
  jsresult = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_unsigned_char, 0 |  0 );
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_TLINScheduleSlot_templ<SWIG_OBJ_WRAP>::_wrap_TLINScheduleSlot_CountResolve_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINScheduleSlot *arg1 = (TLINScheduleSlot *) 0 ;
  BYTE arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINScheduleSlot, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINScheduleSlot_CountResolve_set" "', argument " "1"" of type '" "TLINScheduleSlot *""'"); 
  }
  arg1 = reinterpret_cast< TLINScheduleSlot * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "TLINScheduleSlot_CountResolve_set" "', argument " "2"" of type '" "BYTE""'");
  } 
  arg2 = static_cast< BYTE >(val2);if (arg1) (arg1)->CountResolve = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_TLINScheduleSlot_templ<SWIG_OBJ_WRAP>::_wrap_TLINScheduleSlot_CountResolve_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINScheduleSlot *arg1 = (TLINScheduleSlot *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  BYTE result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINScheduleSlot, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINScheduleSlot_CountResolve_get" "', argument " "1"" of type '" "TLINScheduleSlot *""'"); 
  }
  arg1 = reinterpret_cast< TLINScheduleSlot * >(argp1);result = (BYTE) ((arg1)->CountResolve);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_TLINScheduleSlot_templ<SWIG_OBJ_WRAP>::_wrap_TLINScheduleSlot_Handle_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINScheduleSlot *arg1 = (TLINScheduleSlot *) 0 ;
  DWORD arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned long val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINScheduleSlot, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINScheduleSlot_Handle_set" "', argument " "1"" of type '" "TLINScheduleSlot *""'"); 
  }
  arg1 = reinterpret_cast< TLINScheduleSlot * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_long(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "TLINScheduleSlot_Handle_set" "', argument " "2"" of type '" "DWORD""'");
  } 
  arg2 = static_cast< DWORD >(val2);if (arg1) (arg1)->Handle = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_TLINScheduleSlot_templ<SWIG_OBJ_WRAP>::_wrap_TLINScheduleSlot_Handle_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINScheduleSlot *arg1 = (TLINScheduleSlot *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  DWORD result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINScheduleSlot, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINScheduleSlot_Handle_get" "', argument " "1"" of type '" "TLINScheduleSlot *""'"); 
  }
  arg1 = reinterpret_cast< TLINScheduleSlot * >(argp1);result = (DWORD) ((arg1)->Handle);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


template <typename SWIG_OBJ_WRAP>
// js_ctor
// This is the main constructor
_exports_TLINScheduleSlot_templ<SWIG_OBJ_WRAP>::_exports_TLINScheduleSlot_templ(const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  Napi::Env env = info.Env();
  
  this->info = SWIGTYPE_p_TLINScheduleSlot;
  if (info.Length() == 1 && info[0].IsExternal()) {
    // This constructor has been called internally from C++/SWIG
    // to wrap an already existing C++ object in JS
    this->self = info[0].As<Napi::External<void>>().Data();
    this->owned = false;
    return;
  }
  this->owned = true;
  
  TLINScheduleSlot *result;
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_new_TLINScheduleSlot.");
  }
  result = (TLINScheduleSlot *)new TLINScheduleSlot();
  
  
  this->self = result;
  return;
  goto fail;
fail:
  return;
}

// This is the bypass constructor to be used from child classes
template <typename SWIG_OBJ_WRAP>
_exports_TLINScheduleSlot_templ<SWIG_OBJ_WRAP>::_exports_TLINScheduleSlot_templ(bool, const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  
}


// js_dtoroverride
template <typename SWIG_OBJ_WRAP>
_exports_TLINScheduleSlot_templ<SWIG_OBJ_WRAP>::~_exports_TLINScheduleSlot_templ() {
  auto arg1 = reinterpret_cast<TLINScheduleSlot *>(this->self);
  if (this->owned && arg1) {
    delete arg1;
    this->self = nullptr;
  }
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_TLINHardwareStatus_templ<SWIG_OBJ_WRAP>::_wrap_TLINHardwareStatus_Mode_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINHardwareStatus *arg1 = (TLINHardwareStatus *) 0 ;
  BYTE arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINHardwareStatus, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINHardwareStatus_Mode_set" "', argument " "1"" of type '" "TLINHardwareStatus *""'"); 
  }
  arg1 = reinterpret_cast< TLINHardwareStatus * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "TLINHardwareStatus_Mode_set" "', argument " "2"" of type '" "BYTE""'");
  } 
  arg2 = static_cast< BYTE >(val2);if (arg1) (arg1)->Mode = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_TLINHardwareStatus_templ<SWIG_OBJ_WRAP>::_wrap_TLINHardwareStatus_Mode_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINHardwareStatus *arg1 = (TLINHardwareStatus *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  BYTE result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINHardwareStatus, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINHardwareStatus_Mode_get" "', argument " "1"" of type '" "TLINHardwareStatus *""'"); 
  }
  arg1 = reinterpret_cast< TLINHardwareStatus * >(argp1);result = (BYTE) ((arg1)->Mode);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_TLINHardwareStatus_templ<SWIG_OBJ_WRAP>::_wrap_TLINHardwareStatus_Status_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINHardwareStatus *arg1 = (TLINHardwareStatus *) 0 ;
  BYTE arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINHardwareStatus, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINHardwareStatus_Status_set" "', argument " "1"" of type '" "TLINHardwareStatus *""'"); 
  }
  arg1 = reinterpret_cast< TLINHardwareStatus * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "TLINHardwareStatus_Status_set" "', argument " "2"" of type '" "BYTE""'");
  } 
  arg2 = static_cast< BYTE >(val2);if (arg1) (arg1)->Status = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_TLINHardwareStatus_templ<SWIG_OBJ_WRAP>::_wrap_TLINHardwareStatus_Status_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINHardwareStatus *arg1 = (TLINHardwareStatus *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  BYTE result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINHardwareStatus, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINHardwareStatus_Status_get" "', argument " "1"" of type '" "TLINHardwareStatus *""'"); 
  }
  arg1 = reinterpret_cast< TLINHardwareStatus * >(argp1);result = (BYTE) ((arg1)->Status);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_TLINHardwareStatus_templ<SWIG_OBJ_WRAP>::_wrap_TLINHardwareStatus_FreeOnSendQueue_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINHardwareStatus *arg1 = (TLINHardwareStatus *) 0 ;
  BYTE arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINHardwareStatus, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINHardwareStatus_FreeOnSendQueue_set" "', argument " "1"" of type '" "TLINHardwareStatus *""'"); 
  }
  arg1 = reinterpret_cast< TLINHardwareStatus * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_char(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "TLINHardwareStatus_FreeOnSendQueue_set" "', argument " "2"" of type '" "BYTE""'");
  } 
  arg2 = static_cast< BYTE >(val2);if (arg1) (arg1)->FreeOnSendQueue = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_TLINHardwareStatus_templ<SWIG_OBJ_WRAP>::_wrap_TLINHardwareStatus_FreeOnSendQueue_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINHardwareStatus *arg1 = (TLINHardwareStatus *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  BYTE result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINHardwareStatus, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINHardwareStatus_FreeOnSendQueue_get" "', argument " "1"" of type '" "TLINHardwareStatus *""'"); 
  }
  arg1 = reinterpret_cast< TLINHardwareStatus * >(argp1);result = (BYTE) ((arg1)->FreeOnSendQueue);
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_TLINHardwareStatus_templ<SWIG_OBJ_WRAP>::_wrap_TLINHardwareStatus_FreeOnSchedulePool_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINHardwareStatus *arg1 = (TLINHardwareStatus *) 0 ;
  WORD arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned short val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINHardwareStatus, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINHardwareStatus_FreeOnSchedulePool_set" "', argument " "1"" of type '" "TLINHardwareStatus *""'"); 
  }
  arg1 = reinterpret_cast< TLINHardwareStatus * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_short(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "TLINHardwareStatus_FreeOnSchedulePool_set" "', argument " "2"" of type '" "WORD""'");
  } 
  arg2 = static_cast< WORD >(val2);if (arg1) (arg1)->FreeOnSchedulePool = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_TLINHardwareStatus_templ<SWIG_OBJ_WRAP>::_wrap_TLINHardwareStatus_FreeOnSchedulePool_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINHardwareStatus *arg1 = (TLINHardwareStatus *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  WORD result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINHardwareStatus, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINHardwareStatus_FreeOnSchedulePool_get" "', argument " "1"" of type '" "TLINHardwareStatus *""'"); 
  }
  arg1 = reinterpret_cast< TLINHardwareStatus * >(argp1);result = (WORD) ((arg1)->FreeOnSchedulePool);
  jsresult = SWIG_From_unsigned_SS_short  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned short >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_setter
template <typename SWIG_OBJ_WRAP>
void _exports_TLINHardwareStatus_templ<SWIG_OBJ_WRAP>::_wrap_TLINHardwareStatus_ReceiveBufferOverrun_set(const Napi::CallbackInfo &info, const Napi::Value &value) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINHardwareStatus *arg1 = (TLINHardwareStatus *) 0 ;
  WORD arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned short val2 ;
  int ecode2 = 0 ;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINHardwareStatus, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINHardwareStatus_ReceiveBufferOverrun_set" "', argument " "1"" of type '" "TLINHardwareStatus *""'"); 
  }
  arg1 = reinterpret_cast< TLINHardwareStatus * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_short(value, &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "TLINHardwareStatus_ReceiveBufferOverrun_set" "', argument " "2"" of type '" "WORD""'");
  } 
  arg2 = static_cast< WORD >(val2);if (arg1) (arg1)->ReceiveBufferOverrun = arg2;
  
  
  
  return;
  
  goto fail;
fail:
  return;
}


// js_getter
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_TLINHardwareStatus_templ<SWIG_OBJ_WRAP>::_wrap_TLINHardwareStatus_ReceiveBufferOverrun_get(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINHardwareStatus *arg1 = (TLINHardwareStatus *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  WORD result;
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_TLINHardwareStatus, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "TLINHardwareStatus_ReceiveBufferOverrun_get" "', argument " "1"" of type '" "TLINHardwareStatus *""'"); 
  }
  arg1 = reinterpret_cast< TLINHardwareStatus * >(argp1);result = (WORD) ((arg1)->ReceiveBufferOverrun);
  jsresult = SWIG_From_unsigned_SS_short  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned short >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


template <typename SWIG_OBJ_WRAP>
// js_ctor
// This is the main constructor
_exports_TLINHardwareStatus_templ<SWIG_OBJ_WRAP>::_exports_TLINHardwareStatus_templ(const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  Napi::Env env = info.Env();
  
  this->info = SWIGTYPE_p_TLINHardwareStatus;
  if (info.Length() == 1 && info[0].IsExternal()) {
    // This constructor has been called internally from C++/SWIG
    // to wrap an already existing C++ object in JS
    this->self = info[0].As<Napi::External<void>>().Data();
    this->owned = false;
    return;
  }
  this->owned = true;
  
  TLINHardwareStatus *result;
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_new_TLINHardwareStatus.");
  }
  result = (TLINHardwareStatus *)new TLINHardwareStatus();
  
  
  this->self = result;
  return;
  goto fail;
fail:
  return;
}

// This is the bypass constructor to be used from child classes
template <typename SWIG_OBJ_WRAP>
_exports_TLINHardwareStatus_templ<SWIG_OBJ_WRAP>::_exports_TLINHardwareStatus_templ(bool, const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  
}


// js_dtoroverride
template <typename SWIG_OBJ_WRAP>
_exports_TLINHardwareStatus_templ<SWIG_OBJ_WRAP>::~_exports_TLINHardwareStatus_templ() {
  auto arg1 = reinterpret_cast<TLINHardwareStatus *>(this->self);
  if (this->owned && arg1) {
    delete arg1;
    this->self = nullptr;
  }
}


// js_global_function
Napi::Value _wrap_LIN_RegisterClient(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  LPSTR arg1 = (LPSTR) 0 ;
  DWORD arg2 ;
  HLINCLIENT *arg3 = (HLINCLIENT *) 0 ;
  int res1 ;
  char *buf1 = 0 ;
  int alloc1 = 0 ;
  unsigned long val2 ;
  int ecode2 = 0 ;
  void *argp3 = 0 ;
  int res3 = 0 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 3 || static_cast<int>(info.Length()) > 3) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_RegisterClient.");
  }
  
  res1 = SWIG_AsCharPtrAndSize(info[0], &buf1, NULL, &alloc1);
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LIN_RegisterClient" "', argument " "1"" of type '" "LPSTR""'");
  }
  arg1 = reinterpret_cast< LPSTR >(buf1);ecode2 = SWIG_AsVal_unsigned_SS_long(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_RegisterClient" "', argument " "2"" of type '" "DWORD""'");
  } 
  arg2 = static_cast< DWORD >(val2);res3 = SWIG_ConvertPtr(info[2], &argp3,SWIGTYPE_p_unsigned_char, 0 |  0 );
  if (!SWIG_IsOK(res3)) {
    SWIG_exception_fail(SWIG_ArgError(res3), "in method '" "LIN_RegisterClient" "', argument " "3"" of type '" "HLINCLIENT *""'"); 
  }
  arg3 = reinterpret_cast< HLINCLIENT * >(argp3);result = (DWORD)LIN_RegisterClient(arg1,arg2,arg3);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  if (alloc1 == SWIG_NEWOBJ) delete[] buf1;
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_RemoveClient(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINCLIENT arg1 ;
  unsigned char val1 ;
  int ecode1 = 0 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_RemoveClient.");
  }
  
  ecode1 = SWIG_AsVal_unsigned_SS_char(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_RemoveClient" "', argument " "1"" of type '" "HLINCLIENT""'");
  } 
  arg1 = static_cast< HLINCLIENT >(val1);result = (DWORD)LIN_RemoveClient(arg1);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_ConnectClient(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINCLIENT arg1 ;
  HLINHW arg2 ;
  unsigned char val1 ;
  int ecode1 = 0 ;
  unsigned short val2 ;
  int ecode2 = 0 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 2 || static_cast<int>(info.Length()) > 2) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_ConnectClient.");
  }
  
  ecode1 = SWIG_AsVal_unsigned_SS_char(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_ConnectClient" "', argument " "1"" of type '" "HLINCLIENT""'");
  } 
  arg1 = static_cast< HLINCLIENT >(val1);ecode2 = SWIG_AsVal_unsigned_SS_short(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_ConnectClient" "', argument " "2"" of type '" "HLINHW""'");
  } 
  arg2 = static_cast< HLINHW >(val2);result = (DWORD)LIN_ConnectClient(arg1,arg2);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_DisconnectClient(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINCLIENT arg1 ;
  HLINHW arg2 ;
  unsigned char val1 ;
  int ecode1 = 0 ;
  unsigned short val2 ;
  int ecode2 = 0 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 2 || static_cast<int>(info.Length()) > 2) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_DisconnectClient.");
  }
  
  ecode1 = SWIG_AsVal_unsigned_SS_char(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_DisconnectClient" "', argument " "1"" of type '" "HLINCLIENT""'");
  } 
  arg1 = static_cast< HLINCLIENT >(val1);ecode2 = SWIG_AsVal_unsigned_SS_short(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_DisconnectClient" "', argument " "2"" of type '" "HLINHW""'");
  } 
  arg2 = static_cast< HLINHW >(val2);result = (DWORD)LIN_DisconnectClient(arg1,arg2);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_ResetClient(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINCLIENT arg1 ;
  unsigned char val1 ;
  int ecode1 = 0 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_ResetClient.");
  }
  
  ecode1 = SWIG_AsVal_unsigned_SS_char(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_ResetClient" "', argument " "1"" of type '" "HLINCLIENT""'");
  } 
  arg1 = static_cast< HLINCLIENT >(val1);result = (DWORD)LIN_ResetClient(arg1);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_SetClientParam(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINCLIENT arg1 ;
  WORD arg2 ;
  DWORD arg3 ;
  unsigned char val1 ;
  int ecode1 = 0 ;
  unsigned short val2 ;
  int ecode2 = 0 ;
  unsigned long val3 ;
  int ecode3 = 0 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 3 || static_cast<int>(info.Length()) > 3) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_SetClientParam.");
  }
  
  ecode1 = SWIG_AsVal_unsigned_SS_char(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_SetClientParam" "', argument " "1"" of type '" "HLINCLIENT""'");
  } 
  arg1 = static_cast< HLINCLIENT >(val1);ecode2 = SWIG_AsVal_unsigned_SS_short(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_SetClientParam" "', argument " "2"" of type '" "WORD""'");
  } 
  arg2 = static_cast< WORD >(val2);ecode3 = SWIG_AsVal_unsigned_SS_long(info[2], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "LIN_SetClientParam" "', argument " "3"" of type '" "DWORD""'");
  } 
  arg3 = static_cast< DWORD >(val3);result = (DWORD)LIN_SetClientParam(arg1,arg2,arg3);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_GetClientParam(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINCLIENT arg1 ;
  WORD arg2 ;
  void *arg3 = (void *) 0 ;
  WORD arg4 ;
  unsigned char val1 ;
  int ecode1 = 0 ;
  unsigned short val2 ;
  int ecode2 = 0 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 3 || static_cast<int>(info.Length()) > 3) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_GetClientParam.");
  }
  
  ecode1 = SWIG_AsVal_unsigned_SS_char(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_GetClientParam" "', argument " "1"" of type '" "HLINCLIENT""'");
  } 
  arg1 = static_cast< HLINCLIENT >(val1);ecode2 = SWIG_AsVal_unsigned_SS_short(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_GetClientParam" "', argument " "2"" of type '" "WORD""'");
  } 
  arg2 = static_cast< WORD >(val2);{
    {
      if (info[2].IsBuffer()) {
        Napi::Buffer<char> buf = info[2].As<Napi::Buffer<char>>();
        arg3 = reinterpret_cast<char *>(buf.Data());
        arg4 = buf.ByteLength();
        
      } else {
        SWIG_exception_fail(SWIG_TypeError, "in method 'LIN_GetClientParam', argument is not a Buffer");
      }
    }
  }
  result = (DWORD)LIN_GetClientParam(arg1,arg2,arg3,arg4);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_SetClientFilter(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINCLIENT arg1 ;
  HLINHW arg2 ;
  unsigned __int64 arg3 ;
  unsigned char val1 ;
  int ecode1 = 0 ;
  unsigned short val2 ;
  int ecode2 = 0 ;
  unsigned long long val3 ;
  int ecode3 = 0 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 3 || static_cast<int>(info.Length()) > 3) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_SetClientFilter.");
  }
  
  ecode1 = SWIG_AsVal_unsigned_SS_char(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_SetClientFilter" "', argument " "1"" of type '" "HLINCLIENT""'");
  } 
  arg1 = static_cast< HLINCLIENT >(val1);ecode2 = SWIG_AsVal_unsigned_SS_short(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_SetClientFilter" "', argument " "2"" of type '" "HLINHW""'");
  } 
  arg2 = static_cast< HLINHW >(val2);ecode3 = SWIG_AsVal_unsigned_SS_long_SS_long(info[2], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "LIN_SetClientFilter" "', argument " "3"" of type '" "unsigned __int64""'");
  } 
  arg3 = static_cast< unsigned __int64 >(val3);result = (DWORD)LIN_SetClientFilter(arg1,arg2,SWIG_STD_MOVE(arg3));
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_GetClientFilter(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINCLIENT arg1 ;
  HLINHW arg2 ;
  unsigned __int64 *arg3 = (unsigned __int64 *) 0 ;
  unsigned char val1 ;
  int ecode1 = 0 ;
  unsigned short val2 ;
  int ecode2 = 0 ;
  void *argp3 = 0 ;
  int res3 = 0 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 3 || static_cast<int>(info.Length()) > 3) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_GetClientFilter.");
  }
  
  ecode1 = SWIG_AsVal_unsigned_SS_char(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_GetClientFilter" "', argument " "1"" of type '" "HLINCLIENT""'");
  } 
  arg1 = static_cast< HLINCLIENT >(val1);ecode2 = SWIG_AsVal_unsigned_SS_short(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_GetClientFilter" "', argument " "2"" of type '" "HLINHW""'");
  } 
  arg2 = static_cast< HLINHW >(val2);res3 = SWIG_ConvertPtr(info[2], &argp3,SWIGTYPE_p_unsigned___int64, 0 |  0 );
  if (!SWIG_IsOK(res3)) {
    SWIG_exception_fail(SWIG_ArgError(res3), "in method '" "LIN_GetClientFilter" "', argument " "3"" of type '" "unsigned __int64 *""'"); 
  }
  arg3 = reinterpret_cast< unsigned __int64 * >(argp3);result = (DWORD)LIN_GetClientFilter(arg1,arg2,arg3);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_Read(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINCLIENT arg1 ;
  TLINRcvMsg *arg2 = (TLINRcvMsg *) 0 ;
  unsigned char val1 ;
  int ecode1 = 0 ;
  void *argp2 = 0 ;
  int res2 = 0 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 2 || static_cast<int>(info.Length()) > 2) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_Read.");
  }
  
  ecode1 = SWIG_AsVal_unsigned_SS_char(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_Read" "', argument " "1"" of type '" "HLINCLIENT""'");
  } 
  arg1 = static_cast< HLINCLIENT >(val1);res2 = SWIG_ConvertPtr(info[1], &argp2,SWIGTYPE_p_TLINRcvMsg, 0 |  0 );
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "LIN_Read" "', argument " "2"" of type '" "TLINRcvMsg *""'"); 
  }
  arg2 = reinterpret_cast< TLINRcvMsg * >(argp2);result = (DWORD)LIN_Read(arg1,arg2);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_ReadMulti(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINCLIENT arg1 ;
  TLINRcvMsg *arg2 = (TLINRcvMsg *) 0 ;
  int arg3 ;
  int *arg4 = (int *) 0 ;
  unsigned char val1 ;
  int ecode1 = 0 ;
  void *argp2 = 0 ;
  int res2 = 0 ;
  int val3 ;
  int ecode3 = 0 ;
  void *argp4 = 0 ;
  int res4 = 0 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 4 || static_cast<int>(info.Length()) > 4) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_ReadMulti.");
  }
  
  ecode1 = SWIG_AsVal_unsigned_SS_char(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_ReadMulti" "', argument " "1"" of type '" "HLINCLIENT""'");
  } 
  arg1 = static_cast< HLINCLIENT >(val1);res2 = SWIG_ConvertPtr(info[1], &argp2,SWIGTYPE_p_TLINRcvMsg, 0 |  0 );
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "LIN_ReadMulti" "', argument " "2"" of type '" "TLINRcvMsg *""'"); 
  }
  arg2 = reinterpret_cast< TLINRcvMsg * >(argp2);ecode3 = SWIG_AsVal_int(info[2], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "LIN_ReadMulti" "', argument " "3"" of type '" "int""'");
  } 
  arg3 = static_cast< int >(val3);res4 = SWIG_ConvertPtr(info[3], &argp4,SWIGTYPE_p_int, 0 |  0 );
  if (!SWIG_IsOK(res4)) {
    SWIG_exception_fail(SWIG_ArgError(res4), "in method '" "LIN_ReadMulti" "', argument " "4"" of type '" "int *""'"); 
  }
  arg4 = reinterpret_cast< int * >(argp4);result = (DWORD)LIN_ReadMulti(arg1,arg2,arg3,arg4);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_Write(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINCLIENT arg1 ;
  HLINHW arg2 ;
  TLINMsg *arg3 = (TLINMsg *) 0 ;
  unsigned char val1 ;
  int ecode1 = 0 ;
  unsigned short val2 ;
  int ecode2 = 0 ;
  void *argp3 = 0 ;
  int res3 = 0 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 3 || static_cast<int>(info.Length()) > 3) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_Write.");
  }
  
  ecode1 = SWIG_AsVal_unsigned_SS_char(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_Write" "', argument " "1"" of type '" "HLINCLIENT""'");
  } 
  arg1 = static_cast< HLINCLIENT >(val1);ecode2 = SWIG_AsVal_unsigned_SS_short(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_Write" "', argument " "2"" of type '" "HLINHW""'");
  } 
  arg2 = static_cast< HLINHW >(val2);res3 = SWIG_ConvertPtr(info[2], &argp3,SWIGTYPE_p_TLINMsg, 0 |  0 );
  if (!SWIG_IsOK(res3)) {
    SWIG_exception_fail(SWIG_ArgError(res3), "in method '" "LIN_Write" "', argument " "3"" of type '" "TLINMsg *""'"); 
  }
  arg3 = reinterpret_cast< TLINMsg * >(argp3);result = (DWORD)LIN_Write(arg1,arg2,arg3);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_InitializeHardware(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINCLIENT arg1 ;
  HLINHW arg2 ;
  BYTE arg3 ;
  WORD arg4 ;
  unsigned char val1 ;
  int ecode1 = 0 ;
  unsigned short val2 ;
  int ecode2 = 0 ;
  unsigned char val3 ;
  int ecode3 = 0 ;
  unsigned short val4 ;
  int ecode4 = 0 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 4 || static_cast<int>(info.Length()) > 4) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_InitializeHardware.");
  }
  
  ecode1 = SWIG_AsVal_unsigned_SS_char(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_InitializeHardware" "', argument " "1"" of type '" "HLINCLIENT""'");
  } 
  arg1 = static_cast< HLINCLIENT >(val1);ecode2 = SWIG_AsVal_unsigned_SS_short(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_InitializeHardware" "', argument " "2"" of type '" "HLINHW""'");
  } 
  arg2 = static_cast< HLINHW >(val2);ecode3 = SWIG_AsVal_unsigned_SS_char(info[2], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "LIN_InitializeHardware" "', argument " "3"" of type '" "BYTE""'");
  } 
  arg3 = static_cast< BYTE >(val3);ecode4 = SWIG_AsVal_unsigned_SS_short(info[3], &val4);
  if (!SWIG_IsOK(ecode4)) {
    SWIG_exception_fail(SWIG_ArgError(ecode4), "in method '" "LIN_InitializeHardware" "', argument " "4"" of type '" "WORD""'");
  } 
  arg4 = static_cast< WORD >(val4);result = (DWORD)LIN_InitializeHardware(arg1,arg2,arg3,arg4);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_GetAvailableHardware(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINHW *arg1 = (HLINHW *) 0 ;
  WORD arg2 ;
  int *arg3 = (int *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned short val2 ;
  int ecode2 = 0 ;
  void *argp3 = 0 ;
  int res3 = 0 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 3 || static_cast<int>(info.Length()) > 3) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_GetAvailableHardware.");
  }
  
  res1 = SWIG_ConvertPtr(info[0], &argp1,SWIGTYPE_p_unsigned_short, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LIN_GetAvailableHardware" "', argument " "1"" of type '" "HLINHW *""'"); 
  }
  arg1 = reinterpret_cast< HLINHW * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_short(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_GetAvailableHardware" "', argument " "2"" of type '" "WORD""'");
  } 
  arg2 = static_cast< WORD >(val2);res3 = SWIG_ConvertPtr(info[2], &argp3,SWIGTYPE_p_int, 0 |  0 );
  if (!SWIG_IsOK(res3)) {
    SWIG_exception_fail(SWIG_ArgError(res3), "in method '" "LIN_GetAvailableHardware" "', argument " "3"" of type '" "int *""'"); 
  }
  arg3 = reinterpret_cast< int * >(argp3);result = (DWORD)LIN_GetAvailableHardware(arg1,arg2,arg3);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_SetHardwareParam(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINCLIENT arg1 ;
  HLINHW arg2 ;
  WORD arg3 ;
  void *arg4 = (void *) 0 ;
  WORD arg5 ;
  unsigned char val1 ;
  int ecode1 = 0 ;
  unsigned short val2 ;
  int ecode2 = 0 ;
  unsigned short val3 ;
  int ecode3 = 0 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 4 || static_cast<int>(info.Length()) > 4) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_SetHardwareParam.");
  }
  
  ecode1 = SWIG_AsVal_unsigned_SS_char(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_SetHardwareParam" "', argument " "1"" of type '" "HLINCLIENT""'");
  } 
  arg1 = static_cast< HLINCLIENT >(val1);ecode2 = SWIG_AsVal_unsigned_SS_short(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_SetHardwareParam" "', argument " "2"" of type '" "HLINHW""'");
  } 
  arg2 = static_cast< HLINHW >(val2);ecode3 = SWIG_AsVal_unsigned_SS_short(info[2], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "LIN_SetHardwareParam" "', argument " "3"" of type '" "WORD""'");
  } 
  arg3 = static_cast< WORD >(val3);{
    {
      if (info[3].IsBuffer()) {
        Napi::Buffer<char> buf = info[3].As<Napi::Buffer<char>>();
        arg4 = reinterpret_cast<char *>(buf.Data());
        arg5 = buf.ByteLength();
        
      } else {
        SWIG_exception_fail(SWIG_TypeError, "in method 'LIN_SetHardwareParam', argument is not a Buffer");
      }
    }
  }
  result = (DWORD)LIN_SetHardwareParam(arg1,arg2,arg3,arg4,arg5);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_GetHardwareParam(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINHW arg1 ;
  WORD arg2 ;
  void *arg3 = (void *) 0 ;
  WORD arg4 ;
  unsigned short val1 ;
  int ecode1 = 0 ;
  unsigned short val2 ;
  int ecode2 = 0 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 3 || static_cast<int>(info.Length()) > 3) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_GetHardwareParam.");
  }
  
  ecode1 = SWIG_AsVal_unsigned_SS_short(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_GetHardwareParam" "', argument " "1"" of type '" "HLINHW""'");
  } 
  arg1 = static_cast< HLINHW >(val1);ecode2 = SWIG_AsVal_unsigned_SS_short(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_GetHardwareParam" "', argument " "2"" of type '" "WORD""'");
  } 
  arg2 = static_cast< WORD >(val2);{
    {
      if (info[2].IsBuffer()) {
        Napi::Buffer<char> buf = info[2].As<Napi::Buffer<char>>();
        arg3 = reinterpret_cast<char *>(buf.Data());
        arg4 = buf.ByteLength();
        
      } else {
        SWIG_exception_fail(SWIG_TypeError, "in method 'LIN_GetHardwareParam', argument is not a Buffer");
      }
    }
  }
  result = (DWORD)LIN_GetHardwareParam(arg1,arg2,arg3,arg4);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_ResetHardware(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINCLIENT arg1 ;
  HLINHW arg2 ;
  unsigned char val1 ;
  int ecode1 = 0 ;
  unsigned short val2 ;
  int ecode2 = 0 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 2 || static_cast<int>(info.Length()) > 2) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_ResetHardware.");
  }
  
  ecode1 = SWIG_AsVal_unsigned_SS_char(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_ResetHardware" "', argument " "1"" of type '" "HLINCLIENT""'");
  } 
  arg1 = static_cast< HLINCLIENT >(val1);ecode2 = SWIG_AsVal_unsigned_SS_short(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_ResetHardware" "', argument " "2"" of type '" "HLINHW""'");
  } 
  arg2 = static_cast< HLINHW >(val2);result = (DWORD)LIN_ResetHardware(arg1,arg2);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_ResetHardwareConfig(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINCLIENT arg1 ;
  HLINHW arg2 ;
  unsigned char val1 ;
  int ecode1 = 0 ;
  unsigned short val2 ;
  int ecode2 = 0 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 2 || static_cast<int>(info.Length()) > 2) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_ResetHardwareConfig.");
  }
  
  ecode1 = SWIG_AsVal_unsigned_SS_char(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_ResetHardwareConfig" "', argument " "1"" of type '" "HLINCLIENT""'");
  } 
  arg1 = static_cast< HLINCLIENT >(val1);ecode2 = SWIG_AsVal_unsigned_SS_short(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_ResetHardwareConfig" "', argument " "2"" of type '" "HLINHW""'");
  } 
  arg2 = static_cast< HLINHW >(val2);result = (DWORD)LIN_ResetHardwareConfig(arg1,arg2);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_IdentifyHardware(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINHW arg1 ;
  unsigned short val1 ;
  int ecode1 = 0 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_IdentifyHardware.");
  }
  
  ecode1 = SWIG_AsVal_unsigned_SS_short(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_IdentifyHardware" "', argument " "1"" of type '" "HLINHW""'");
  } 
  arg1 = static_cast< HLINHW >(val1);result = (DWORD)LIN_IdentifyHardware(arg1);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_RegisterFrameId(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINCLIENT arg1 ;
  HLINHW arg2 ;
  BYTE arg3 ;
  BYTE arg4 ;
  unsigned char val1 ;
  int ecode1 = 0 ;
  unsigned short val2 ;
  int ecode2 = 0 ;
  unsigned char val3 ;
  int ecode3 = 0 ;
  unsigned char val4 ;
  int ecode4 = 0 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 4 || static_cast<int>(info.Length()) > 4) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_RegisterFrameId.");
  }
  
  ecode1 = SWIG_AsVal_unsigned_SS_char(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_RegisterFrameId" "', argument " "1"" of type '" "HLINCLIENT""'");
  } 
  arg1 = static_cast< HLINCLIENT >(val1);ecode2 = SWIG_AsVal_unsigned_SS_short(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_RegisterFrameId" "', argument " "2"" of type '" "HLINHW""'");
  } 
  arg2 = static_cast< HLINHW >(val2);ecode3 = SWIG_AsVal_unsigned_SS_char(info[2], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "LIN_RegisterFrameId" "', argument " "3"" of type '" "BYTE""'");
  } 
  arg3 = static_cast< BYTE >(val3);ecode4 = SWIG_AsVal_unsigned_SS_char(info[3], &val4);
  if (!SWIG_IsOK(ecode4)) {
    SWIG_exception_fail(SWIG_ArgError(ecode4), "in method '" "LIN_RegisterFrameId" "', argument " "4"" of type '" "BYTE""'");
  } 
  arg4 = static_cast< BYTE >(val4);result = (DWORD)LIN_RegisterFrameId(arg1,arg2,arg3,arg4);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_SetFrameEntry(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINCLIENT arg1 ;
  HLINHW arg2 ;
  TLINFrameEntry *arg3 = (TLINFrameEntry *) 0 ;
  unsigned char val1 ;
  int ecode1 = 0 ;
  unsigned short val2 ;
  int ecode2 = 0 ;
  void *argp3 = 0 ;
  int res3 = 0 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 3 || static_cast<int>(info.Length()) > 3) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_SetFrameEntry.");
  }
  
  ecode1 = SWIG_AsVal_unsigned_SS_char(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_SetFrameEntry" "', argument " "1"" of type '" "HLINCLIENT""'");
  } 
  arg1 = static_cast< HLINCLIENT >(val1);ecode2 = SWIG_AsVal_unsigned_SS_short(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_SetFrameEntry" "', argument " "2"" of type '" "HLINHW""'");
  } 
  arg2 = static_cast< HLINHW >(val2);res3 = SWIG_ConvertPtr(info[2], &argp3,SWIGTYPE_p_TLINFrameEntry, 0 |  0 );
  if (!SWIG_IsOK(res3)) {
    SWIG_exception_fail(SWIG_ArgError(res3), "in method '" "LIN_SetFrameEntry" "', argument " "3"" of type '" "TLINFrameEntry *""'"); 
  }
  arg3 = reinterpret_cast< TLINFrameEntry * >(argp3);result = (DWORD)LIN_SetFrameEntry(arg1,arg2,arg3);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_GetFrameEntry(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINHW arg1 ;
  TLINFrameEntry *arg2 = (TLINFrameEntry *) 0 ;
  unsigned short val1 ;
  int ecode1 = 0 ;
  void *argp2 = 0 ;
  int res2 = 0 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 2 || static_cast<int>(info.Length()) > 2) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_GetFrameEntry.");
  }
  
  ecode1 = SWIG_AsVal_unsigned_SS_short(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_GetFrameEntry" "', argument " "1"" of type '" "HLINHW""'");
  } 
  arg1 = static_cast< HLINHW >(val1);res2 = SWIG_ConvertPtr(info[1], &argp2,SWIGTYPE_p_TLINFrameEntry, 0 |  0 );
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "LIN_GetFrameEntry" "', argument " "2"" of type '" "TLINFrameEntry *""'"); 
  }
  arg2 = reinterpret_cast< TLINFrameEntry * >(argp2);result = (DWORD)LIN_GetFrameEntry(arg1,arg2);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_UpdateByteArray(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINCLIENT arg1 ;
  HLINHW arg2 ;
  BYTE arg3 ;
  BYTE arg4 ;
  BYTE arg5 ;
  BYTE *arg6 = (BYTE *) 0 ;
  unsigned char val1 ;
  int ecode1 = 0 ;
  unsigned short val2 ;
  int ecode2 = 0 ;
  unsigned char val3 ;
  int ecode3 = 0 ;
  unsigned char val4 ;
  int ecode4 = 0 ;
  unsigned char val5 ;
  int ecode5 = 0 ;
  void *argp6 = 0 ;
  int res6 = 0 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 6 || static_cast<int>(info.Length()) > 6) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_UpdateByteArray.");
  }
  
  ecode1 = SWIG_AsVal_unsigned_SS_char(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_UpdateByteArray" "', argument " "1"" of type '" "HLINCLIENT""'");
  } 
  arg1 = static_cast< HLINCLIENT >(val1);ecode2 = SWIG_AsVal_unsigned_SS_short(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_UpdateByteArray" "', argument " "2"" of type '" "HLINHW""'");
  } 
  arg2 = static_cast< HLINHW >(val2);ecode3 = SWIG_AsVal_unsigned_SS_char(info[2], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "LIN_UpdateByteArray" "', argument " "3"" of type '" "BYTE""'");
  } 
  arg3 = static_cast< BYTE >(val3);ecode4 = SWIG_AsVal_unsigned_SS_char(info[3], &val4);
  if (!SWIG_IsOK(ecode4)) {
    SWIG_exception_fail(SWIG_ArgError(ecode4), "in method '" "LIN_UpdateByteArray" "', argument " "4"" of type '" "BYTE""'");
  } 
  arg4 = static_cast< BYTE >(val4);ecode5 = SWIG_AsVal_unsigned_SS_char(info[4], &val5);
  if (!SWIG_IsOK(ecode5)) {
    SWIG_exception_fail(SWIG_ArgError(ecode5), "in method '" "LIN_UpdateByteArray" "', argument " "5"" of type '" "BYTE""'");
  } 
  arg5 = static_cast< BYTE >(val5);res6 = SWIG_ConvertPtr(info[5], &argp6,SWIGTYPE_p_unsigned_char, 0 |  0 );
  if (!SWIG_IsOK(res6)) {
    SWIG_exception_fail(SWIG_ArgError(res6), "in method '" "LIN_UpdateByteArray" "', argument " "6"" of type '" "BYTE *""'"); 
  }
  arg6 = reinterpret_cast< BYTE * >(argp6);result = (DWORD)LIN_UpdateByteArray(arg1,arg2,arg3,arg4,arg5,arg6);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_StartKeepAlive(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINCLIENT arg1 ;
  HLINHW arg2 ;
  BYTE arg3 ;
  WORD arg4 ;
  unsigned char val1 ;
  int ecode1 = 0 ;
  unsigned short val2 ;
  int ecode2 = 0 ;
  unsigned char val3 ;
  int ecode3 = 0 ;
  unsigned short val4 ;
  int ecode4 = 0 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 4 || static_cast<int>(info.Length()) > 4) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_StartKeepAlive.");
  }
  
  ecode1 = SWIG_AsVal_unsigned_SS_char(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_StartKeepAlive" "', argument " "1"" of type '" "HLINCLIENT""'");
  } 
  arg1 = static_cast< HLINCLIENT >(val1);ecode2 = SWIG_AsVal_unsigned_SS_short(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_StartKeepAlive" "', argument " "2"" of type '" "HLINHW""'");
  } 
  arg2 = static_cast< HLINHW >(val2);ecode3 = SWIG_AsVal_unsigned_SS_char(info[2], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "LIN_StartKeepAlive" "', argument " "3"" of type '" "BYTE""'");
  } 
  arg3 = static_cast< BYTE >(val3);ecode4 = SWIG_AsVal_unsigned_SS_short(info[3], &val4);
  if (!SWIG_IsOK(ecode4)) {
    SWIG_exception_fail(SWIG_ArgError(ecode4), "in method '" "LIN_StartKeepAlive" "', argument " "4"" of type '" "WORD""'");
  } 
  arg4 = static_cast< WORD >(val4);result = (DWORD)LIN_StartKeepAlive(arg1,arg2,arg3,arg4);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_SuspendKeepAlive(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINCLIENT arg1 ;
  HLINHW arg2 ;
  unsigned char val1 ;
  int ecode1 = 0 ;
  unsigned short val2 ;
  int ecode2 = 0 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 2 || static_cast<int>(info.Length()) > 2) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_SuspendKeepAlive.");
  }
  
  ecode1 = SWIG_AsVal_unsigned_SS_char(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_SuspendKeepAlive" "', argument " "1"" of type '" "HLINCLIENT""'");
  } 
  arg1 = static_cast< HLINCLIENT >(val1);ecode2 = SWIG_AsVal_unsigned_SS_short(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_SuspendKeepAlive" "', argument " "2"" of type '" "HLINHW""'");
  } 
  arg2 = static_cast< HLINHW >(val2);result = (DWORD)LIN_SuspendKeepAlive(arg1,arg2);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_ResumeKeepAlive(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINCLIENT arg1 ;
  HLINHW arg2 ;
  unsigned char val1 ;
  int ecode1 = 0 ;
  unsigned short val2 ;
  int ecode2 = 0 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 2 || static_cast<int>(info.Length()) > 2) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_ResumeKeepAlive.");
  }
  
  ecode1 = SWIG_AsVal_unsigned_SS_char(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_ResumeKeepAlive" "', argument " "1"" of type '" "HLINCLIENT""'");
  } 
  arg1 = static_cast< HLINCLIENT >(val1);ecode2 = SWIG_AsVal_unsigned_SS_short(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_ResumeKeepAlive" "', argument " "2"" of type '" "HLINHW""'");
  } 
  arg2 = static_cast< HLINHW >(val2);result = (DWORD)LIN_ResumeKeepAlive(arg1,arg2);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_SetSchedule(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINCLIENT arg1 ;
  HLINHW arg2 ;
  int arg3 ;
  TLINScheduleSlot *arg4 = (TLINScheduleSlot *) 0 ;
  int arg5 ;
  unsigned char val1 ;
  int ecode1 = 0 ;
  unsigned short val2 ;
  int ecode2 = 0 ;
  int val3 ;
  int ecode3 = 0 ;
  void *argp4 = 0 ;
  int res4 = 0 ;
  int val5 ;
  int ecode5 = 0 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 5 || static_cast<int>(info.Length()) > 5) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_SetSchedule.");
  }
  
  ecode1 = SWIG_AsVal_unsigned_SS_char(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_SetSchedule" "', argument " "1"" of type '" "HLINCLIENT""'");
  } 
  arg1 = static_cast< HLINCLIENT >(val1);ecode2 = SWIG_AsVal_unsigned_SS_short(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_SetSchedule" "', argument " "2"" of type '" "HLINHW""'");
  } 
  arg2 = static_cast< HLINHW >(val2);ecode3 = SWIG_AsVal_int(info[2], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "LIN_SetSchedule" "', argument " "3"" of type '" "int""'");
  } 
  arg3 = static_cast< int >(val3);res4 = SWIG_ConvertPtr(info[3], &argp4,SWIGTYPE_p_TLINScheduleSlot, 0 |  0 );
  if (!SWIG_IsOK(res4)) {
    SWIG_exception_fail(SWIG_ArgError(res4), "in method '" "LIN_SetSchedule" "', argument " "4"" of type '" "TLINScheduleSlot *""'"); 
  }
  arg4 = reinterpret_cast< TLINScheduleSlot * >(argp4);ecode5 = SWIG_AsVal_int(info[4], &val5);
  if (!SWIG_IsOK(ecode5)) {
    SWIG_exception_fail(SWIG_ArgError(ecode5), "in method '" "LIN_SetSchedule" "', argument " "5"" of type '" "int""'");
  } 
  arg5 = static_cast< int >(val5);result = (DWORD)LIN_SetSchedule(arg1,arg2,arg3,arg4,arg5);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_GetSchedule(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINHW arg1 ;
  int arg2 ;
  TLINScheduleSlot *arg3 = (TLINScheduleSlot *) 0 ;
  int arg4 ;
  int *arg5 = (int *) 0 ;
  unsigned short val1 ;
  int ecode1 = 0 ;
  int val2 ;
  int ecode2 = 0 ;
  void *argp3 = 0 ;
  int res3 = 0 ;
  int val4 ;
  int ecode4 = 0 ;
  void *argp5 = 0 ;
  int res5 = 0 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 5 || static_cast<int>(info.Length()) > 5) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_GetSchedule.");
  }
  
  ecode1 = SWIG_AsVal_unsigned_SS_short(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_GetSchedule" "', argument " "1"" of type '" "HLINHW""'");
  } 
  arg1 = static_cast< HLINHW >(val1);ecode2 = SWIG_AsVal_int(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_GetSchedule" "', argument " "2"" of type '" "int""'");
  } 
  arg2 = static_cast< int >(val2);res3 = SWIG_ConvertPtr(info[2], &argp3,SWIGTYPE_p_TLINScheduleSlot, 0 |  0 );
  if (!SWIG_IsOK(res3)) {
    SWIG_exception_fail(SWIG_ArgError(res3), "in method '" "LIN_GetSchedule" "', argument " "3"" of type '" "TLINScheduleSlot *""'"); 
  }
  arg3 = reinterpret_cast< TLINScheduleSlot * >(argp3);ecode4 = SWIG_AsVal_int(info[3], &val4);
  if (!SWIG_IsOK(ecode4)) {
    SWIG_exception_fail(SWIG_ArgError(ecode4), "in method '" "LIN_GetSchedule" "', argument " "4"" of type '" "int""'");
  } 
  arg4 = static_cast< int >(val4);res5 = SWIG_ConvertPtr(info[4], &argp5,SWIGTYPE_p_int, 0 |  0 );
  if (!SWIG_IsOK(res5)) {
    SWIG_exception_fail(SWIG_ArgError(res5), "in method '" "LIN_GetSchedule" "', argument " "5"" of type '" "int *""'"); 
  }
  arg5 = reinterpret_cast< int * >(argp5);result = (DWORD)LIN_GetSchedule(arg1,arg2,arg3,arg4,arg5);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_DeleteSchedule(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINCLIENT arg1 ;
  HLINHW arg2 ;
  int arg3 ;
  unsigned char val1 ;
  int ecode1 = 0 ;
  unsigned short val2 ;
  int ecode2 = 0 ;
  int val3 ;
  int ecode3 = 0 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 3 || static_cast<int>(info.Length()) > 3) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_DeleteSchedule.");
  }
  
  ecode1 = SWIG_AsVal_unsigned_SS_char(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_DeleteSchedule" "', argument " "1"" of type '" "HLINCLIENT""'");
  } 
  arg1 = static_cast< HLINCLIENT >(val1);ecode2 = SWIG_AsVal_unsigned_SS_short(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_DeleteSchedule" "', argument " "2"" of type '" "HLINHW""'");
  } 
  arg2 = static_cast< HLINHW >(val2);ecode3 = SWIG_AsVal_int(info[2], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "LIN_DeleteSchedule" "', argument " "3"" of type '" "int""'");
  } 
  arg3 = static_cast< int >(val3);result = (DWORD)LIN_DeleteSchedule(arg1,arg2,arg3);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_SetScheduleBreakPoint(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINCLIENT arg1 ;
  HLINHW arg2 ;
  int arg3 ;
  DWORD arg4 ;
  unsigned char val1 ;
  int ecode1 = 0 ;
  unsigned short val2 ;
  int ecode2 = 0 ;
  int val3 ;
  int ecode3 = 0 ;
  unsigned long val4 ;
  int ecode4 = 0 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 4 || static_cast<int>(info.Length()) > 4) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_SetScheduleBreakPoint.");
  }
  
  ecode1 = SWIG_AsVal_unsigned_SS_char(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_SetScheduleBreakPoint" "', argument " "1"" of type '" "HLINCLIENT""'");
  } 
  arg1 = static_cast< HLINCLIENT >(val1);ecode2 = SWIG_AsVal_unsigned_SS_short(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_SetScheduleBreakPoint" "', argument " "2"" of type '" "HLINHW""'");
  } 
  arg2 = static_cast< HLINHW >(val2);ecode3 = SWIG_AsVal_int(info[2], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "LIN_SetScheduleBreakPoint" "', argument " "3"" of type '" "int""'");
  } 
  arg3 = static_cast< int >(val3);ecode4 = SWIG_AsVal_unsigned_SS_long(info[3], &val4);
  if (!SWIG_IsOK(ecode4)) {
    SWIG_exception_fail(SWIG_ArgError(ecode4), "in method '" "LIN_SetScheduleBreakPoint" "', argument " "4"" of type '" "DWORD""'");
  } 
  arg4 = static_cast< DWORD >(val4);result = (DWORD)LIN_SetScheduleBreakPoint(arg1,arg2,arg3,arg4);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_StartSchedule(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINCLIENT arg1 ;
  HLINHW arg2 ;
  int arg3 ;
  unsigned char val1 ;
  int ecode1 = 0 ;
  unsigned short val2 ;
  int ecode2 = 0 ;
  int val3 ;
  int ecode3 = 0 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 3 || static_cast<int>(info.Length()) > 3) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_StartSchedule.");
  }
  
  ecode1 = SWIG_AsVal_unsigned_SS_char(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_StartSchedule" "', argument " "1"" of type '" "HLINCLIENT""'");
  } 
  arg1 = static_cast< HLINCLIENT >(val1);ecode2 = SWIG_AsVal_unsigned_SS_short(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_StartSchedule" "', argument " "2"" of type '" "HLINHW""'");
  } 
  arg2 = static_cast< HLINHW >(val2);ecode3 = SWIG_AsVal_int(info[2], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "LIN_StartSchedule" "', argument " "3"" of type '" "int""'");
  } 
  arg3 = static_cast< int >(val3);result = (DWORD)LIN_StartSchedule(arg1,arg2,arg3);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_SuspendSchedule(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINCLIENT arg1 ;
  HLINHW arg2 ;
  unsigned char val1 ;
  int ecode1 = 0 ;
  unsigned short val2 ;
  int ecode2 = 0 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 2 || static_cast<int>(info.Length()) > 2) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_SuspendSchedule.");
  }
  
  ecode1 = SWIG_AsVal_unsigned_SS_char(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_SuspendSchedule" "', argument " "1"" of type '" "HLINCLIENT""'");
  } 
  arg1 = static_cast< HLINCLIENT >(val1);ecode2 = SWIG_AsVal_unsigned_SS_short(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_SuspendSchedule" "', argument " "2"" of type '" "HLINHW""'");
  } 
  arg2 = static_cast< HLINHW >(val2);result = (DWORD)LIN_SuspendSchedule(arg1,arg2);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_ResumeSchedule(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINCLIENT arg1 ;
  HLINHW arg2 ;
  unsigned char val1 ;
  int ecode1 = 0 ;
  unsigned short val2 ;
  int ecode2 = 0 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 2 || static_cast<int>(info.Length()) > 2) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_ResumeSchedule.");
  }
  
  ecode1 = SWIG_AsVal_unsigned_SS_char(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_ResumeSchedule" "', argument " "1"" of type '" "HLINCLIENT""'");
  } 
  arg1 = static_cast< HLINCLIENT >(val1);ecode2 = SWIG_AsVal_unsigned_SS_short(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_ResumeSchedule" "', argument " "2"" of type '" "HLINHW""'");
  } 
  arg2 = static_cast< HLINHW >(val2);result = (DWORD)LIN_ResumeSchedule(arg1,arg2);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_XmtWakeUp(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINCLIENT arg1 ;
  HLINHW arg2 ;
  unsigned char val1 ;
  int ecode1 = 0 ;
  unsigned short val2 ;
  int ecode2 = 0 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 2 || static_cast<int>(info.Length()) > 2) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_XmtWakeUp.");
  }
  
  ecode1 = SWIG_AsVal_unsigned_SS_char(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_XmtWakeUp" "', argument " "1"" of type '" "HLINCLIENT""'");
  } 
  arg1 = static_cast< HLINCLIENT >(val1);ecode2 = SWIG_AsVal_unsigned_SS_short(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_XmtWakeUp" "', argument " "2"" of type '" "HLINHW""'");
  } 
  arg2 = static_cast< HLINHW >(val2);result = (DWORD)LIN_XmtWakeUp(arg1,arg2);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_XmtDynamicWakeUp(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINCLIENT arg1 ;
  HLINHW arg2 ;
  WORD arg3 ;
  unsigned char val1 ;
  int ecode1 = 0 ;
  unsigned short val2 ;
  int ecode2 = 0 ;
  unsigned short val3 ;
  int ecode3 = 0 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 3 || static_cast<int>(info.Length()) > 3) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_XmtDynamicWakeUp.");
  }
  
  ecode1 = SWIG_AsVal_unsigned_SS_char(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_XmtDynamicWakeUp" "', argument " "1"" of type '" "HLINCLIENT""'");
  } 
  arg1 = static_cast< HLINCLIENT >(val1);ecode2 = SWIG_AsVal_unsigned_SS_short(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_XmtDynamicWakeUp" "', argument " "2"" of type '" "HLINHW""'");
  } 
  arg2 = static_cast< HLINHW >(val2);ecode3 = SWIG_AsVal_unsigned_SS_short(info[2], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "LIN_XmtDynamicWakeUp" "', argument " "3"" of type '" "WORD""'");
  } 
  arg3 = static_cast< WORD >(val3);result = (DWORD)LIN_XmtDynamicWakeUp(arg1,arg2,arg3);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_StartAutoBaud(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINCLIENT arg1 ;
  HLINHW arg2 ;
  WORD arg3 ;
  unsigned char val1 ;
  int ecode1 = 0 ;
  unsigned short val2 ;
  int ecode2 = 0 ;
  unsigned short val3 ;
  int ecode3 = 0 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 3 || static_cast<int>(info.Length()) > 3) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_StartAutoBaud.");
  }
  
  ecode1 = SWIG_AsVal_unsigned_SS_char(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_StartAutoBaud" "', argument " "1"" of type '" "HLINCLIENT""'");
  } 
  arg1 = static_cast< HLINCLIENT >(val1);ecode2 = SWIG_AsVal_unsigned_SS_short(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_StartAutoBaud" "', argument " "2"" of type '" "HLINHW""'");
  } 
  arg2 = static_cast< HLINHW >(val2);ecode3 = SWIG_AsVal_unsigned_SS_short(info[2], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "LIN_StartAutoBaud" "', argument " "3"" of type '" "WORD""'");
  } 
  arg3 = static_cast< WORD >(val3);result = (DWORD)LIN_StartAutoBaud(arg1,arg2,arg3);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_GetStatus(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINHW arg1 ;
  TLINHardwareStatus *arg2 = (TLINHardwareStatus *) 0 ;
  unsigned short val1 ;
  int ecode1 = 0 ;
  void *argp2 = 0 ;
  int res2 = 0 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 2 || static_cast<int>(info.Length()) > 2) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_GetStatus.");
  }
  
  ecode1 = SWIG_AsVal_unsigned_SS_short(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_GetStatus" "', argument " "1"" of type '" "HLINHW""'");
  } 
  arg1 = static_cast< HLINHW >(val1);res2 = SWIG_ConvertPtr(info[1], &argp2,SWIGTYPE_p_TLINHardwareStatus, 0 |  0 );
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "LIN_GetStatus" "', argument " "2"" of type '" "TLINHardwareStatus *""'"); 
  }
  arg2 = reinterpret_cast< TLINHardwareStatus * >(argp2);result = (DWORD)LIN_GetStatus(arg1,arg2);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_CalculateChecksum(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINMsg *arg1 = (TLINMsg *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_CalculateChecksum.");
  }
  
  res1 = SWIG_ConvertPtr(info[0], &argp1,SWIGTYPE_p_TLINMsg, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LIN_CalculateChecksum" "', argument " "1"" of type '" "TLINMsg *""'"); 
  }
  arg1 = reinterpret_cast< TLINMsg * >(argp1);result = (DWORD)LIN_CalculateChecksum(arg1);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_GetVersion(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  TLINVersion *arg1 = (TLINVersion *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_GetVersion.");
  }
  
  res1 = SWIG_ConvertPtr(info[0], &argp1,SWIGTYPE_p_TLINVersion, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LIN_GetVersion" "', argument " "1"" of type '" "TLINVersion *""'"); 
  }
  arg1 = reinterpret_cast< TLINVersion * >(argp1);result = (DWORD)LIN_GetVersion(arg1);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_GetVersionInfo(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  LPSTR arg1 = (LPSTR) 0 ;
  WORD arg2 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_GetVersionInfo.");
  }
  
  {
    {
      if (info[0].IsBuffer()) {
        Napi::Buffer<char> buf = info[0].As<Napi::Buffer<char>>();
        arg1 = reinterpret_cast<char *>(buf.Data());
        arg2 = buf.ByteLength();
        
      } else {
        SWIG_exception_fail(SWIG_TypeError, "in method 'LIN_GetVersionInfo', argument is not a Buffer");
      }
    }
  }
  result = (DWORD)LIN_GetVersionInfo(arg1,arg2);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_GetErrorText(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  DWORD arg1 ;
  BYTE arg2 ;
  LPSTR arg3 = (LPSTR) 0 ;
  WORD arg4 ;
  unsigned long val1 ;
  int ecode1 = 0 ;
  unsigned char val2 ;
  int ecode2 = 0 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 3 || static_cast<int>(info.Length()) > 3) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_GetErrorText.");
  }
  
  ecode1 = SWIG_AsVal_unsigned_SS_long(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_GetErrorText" "', argument " "1"" of type '" "DWORD""'");
  } 
  arg1 = static_cast< DWORD >(val1);ecode2 = SWIG_AsVal_unsigned_SS_char(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_GetErrorText" "', argument " "2"" of type '" "BYTE""'");
  } 
  arg2 = static_cast< BYTE >(val2);{
    {
      if (info[2].IsBuffer()) {
        Napi::Buffer<char> buf = info[2].As<Napi::Buffer<char>>();
        arg3 = reinterpret_cast<char *>(buf.Data());
        arg4 = buf.ByteLength();
        
      } else {
        SWIG_exception_fail(SWIG_TypeError, "in method 'LIN_GetErrorText', argument is not a Buffer");
      }
    }
  }
  result = (DWORD)LIN_GetErrorText(arg1,arg2,arg3,arg4);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_GetPID(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  BYTE *arg1 = (BYTE *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_GetPID.");
  }
  
  res1 = SWIG_ConvertPtr(info[0], &argp1,SWIGTYPE_p_unsigned_char, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LIN_GetPID" "', argument " "1"" of type '" "BYTE *""'"); 
  }
  arg1 = reinterpret_cast< BYTE * >(argp1);result = (DWORD)LIN_GetPID(arg1);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_GetTargetTime(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINHW arg1 ;
  unsigned __int64 *arg2 = (unsigned __int64 *) 0 ;
  unsigned short val1 ;
  int ecode1 = 0 ;
  void *argp2 = 0 ;
  int res2 = 0 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 2 || static_cast<int>(info.Length()) > 2) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_GetTargetTime.");
  }
  
  ecode1 = SWIG_AsVal_unsigned_SS_short(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_GetTargetTime" "', argument " "1"" of type '" "HLINHW""'");
  } 
  arg1 = static_cast< HLINHW >(val1);res2 = SWIG_ConvertPtr(info[1], &argp2,SWIGTYPE_p_unsigned___int64, 0 |  0 );
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "LIN_GetTargetTime" "', argument " "2"" of type '" "unsigned __int64 *""'"); 
  }
  arg2 = reinterpret_cast< unsigned __int64 * >(argp2);result = (DWORD)LIN_GetTargetTime(arg1,arg2);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_SetResponseRemap(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINCLIENT arg1 ;
  HLINHW arg2 ;
  BYTE *arg3 = (BYTE *) 0 ;
  unsigned char val1 ;
  int ecode1 = 0 ;
  unsigned short val2 ;
  int ecode2 = 0 ;
  void *argp3 = 0 ;
  int res3 = 0 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 3 || static_cast<int>(info.Length()) > 3) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_SetResponseRemap.");
  }
  
  ecode1 = SWIG_AsVal_unsigned_SS_char(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_SetResponseRemap" "', argument " "1"" of type '" "HLINCLIENT""'");
  } 
  arg1 = static_cast< HLINCLIENT >(val1);ecode2 = SWIG_AsVal_unsigned_SS_short(info[1], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "LIN_SetResponseRemap" "', argument " "2"" of type '" "HLINHW""'");
  } 
  arg2 = static_cast< HLINHW >(val2);res3 = SWIG_ConvertPtr(info[2], &argp3,SWIGTYPE_p_unsigned_char, 0 |  0 );
  if (!SWIG_IsOK(res3)) {
    SWIG_exception_fail(SWIG_ArgError(res3), "in method '" "LIN_SetResponseRemap" "', argument " "3"" of type '" "BYTE *""'"); 
  }
  arg3 = reinterpret_cast< BYTE * >(argp3);result = (DWORD)LIN_SetResponseRemap(arg1,arg2,arg3);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_GetResponseRemap(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  HLINHW arg1 ;
  BYTE *arg2 = (BYTE *) 0 ;
  unsigned short val1 ;
  int ecode1 = 0 ;
  void *argp2 = 0 ;
  int res2 = 0 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 2 || static_cast<int>(info.Length()) > 2) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_GetResponseRemap.");
  }
  
  ecode1 = SWIG_AsVal_unsigned_SS_short(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "LIN_GetResponseRemap" "', argument " "1"" of type '" "HLINHW""'");
  } 
  arg1 = static_cast< HLINHW >(val1);res2 = SWIG_ConvertPtr(info[1], &argp2,SWIGTYPE_p_unsigned_char, 0 |  0 );
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "LIN_GetResponseRemap" "', argument " "2"" of type '" "BYTE *""'"); 
  }
  arg2 = reinterpret_cast< BYTE * >(argp2);result = (DWORD)LIN_GetResponseRemap(arg1,arg2);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LIN_GetSystemTime(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  unsigned __int64 *arg1 = (unsigned __int64 *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  DWORD result;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LIN_GetSystemTime.");
  }
  
  res1 = SWIG_ConvertPtr(info[0], &argp1,SWIGTYPE_p_unsigned___int64, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LIN_GetSystemTime" "', argument " "1"" of type '" "unsigned __int64 *""'"); 
  }
  arg1 = reinterpret_cast< unsigned __int64 * >(argp1);result = (DWORD)LIN_GetSystemTime(arg1);
  jsresult = SWIG_From_unsigned_SS_long  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned long >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_global_function
Napi::Value _wrap_LoadDll(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  char *arg1 = (char *) 0 ;
  int res1 ;
  char *buf1 = 0 ;
  int alloc1 = 0 ;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_LoadDll.");
  }
  
  res1 = SWIG_AsCharPtrAndSize(info[0], &buf1, NULL, &alloc1);
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "LoadDll" "', argument " "1"" of type '" "char const *""'");
  }
  arg1 = reinterpret_cast< char * >(buf1);LoadDll((char const *)arg1);
  jsresult = env.Undefined();
  if (alloc1 == SWIG_NEWOBJ) delete[] buf1;
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


/* -------- TYPE CONVERSION AND EQUIVALENCE RULES (BEGIN) -------- */

static void *_p_INT64_JSTo_p___int64(void *x, int *SWIGUNUSEDPARM(newmemory)) {
    return (void *)((__int64 *)  ((INT64_JS *) x));
}
static void *_p_INT_JSTo_p_int(void *x, int *SWIGUNUSEDPARM(newmemory)) {
    return (void *)((int *)  ((INT_JS *) x));
}
static swig_type_info _swigt__p_ByteArray = {"_p_ByteArray", "p_ByteArray|ByteArray *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_HLINCLIENT_JS = {"_p_HLINCLIENT_JS", "HLINCLIENT_JS *|p_HLINCLIENT_JS", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_HLINHW_JS = {"_p_HLINHW_JS", "HLINHW_JS *|p_HLINHW_JS", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_INT64_JS = {"_p_INT64_JS", "p_INT64_JS|INT64_JS *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_INT_JS = {"_p_INT_JS", "p_INT_JS|INT_JS *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_TLINFrameEntry = {"_p_TLINFrameEntry", "p_TLINFrameEntry|TLINFrameEntry *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_TLINHardwareStatus = {"_p_TLINHardwareStatus", "TLINHardwareStatus *|p_TLINHardwareStatus", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_TLINMsg = {"_p_TLINMsg", "p_TLINMsg|TLINMsg *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_TLINRcvMsg = {"_p_TLINRcvMsg", "p_TLINRcvMsg|TLINRcvMsg *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_TLINScheduleSlot = {"_p_TLINScheduleSlot", "p_TLINScheduleSlot|TLINScheduleSlot *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_TLINVersion = {"_p_TLINVersion", "TLINVersion *|p_TLINVersion", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p___int64 = {"_p___int64", "LONG64 *|LONGLONG *|__int64 *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_char = {"_p_char", "CCHAR *|CHAR *|TCHAR *|char *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_float = {"_p_float", "FLOAT *|float *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_int = {"_p_int", "BOOL *|INT *|INT32 *|INT_PTR *|LONG32 *|int32_t *|int_fast16_t *|int_fast32_t *|int_least32_t *|intptr_t *|int *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_long = {"_p_long", "HRESULT *|LONG *|LONG_PTR *|SHANDLE_PTR *|SSIZE_T *|long *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_long_long = {"_p_long_long", "int64_t *|int_fast64_t *|int_least64_t *|intmax_t *|long long *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_p_char = {"_p_p_char", "LPCTSTR *|LPCUTSTR *|LPTCH *|LPTSTR *|LPUTSTR *|PCTSTR *|PCUTSTR *|PTCH *|PTSTR *|PUTSTR *|char **", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_p_unsigned_long = {"_p_p_unsigned_long", "PLCID *|unsigned long **", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_short = {"_p_short", "HALF_PTR *|INT16 *|SHORT *|int16_t *|int_least16_t *|short *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_signed___int64 = {"_p_signed___int64", "INT64 *|signed __int64 *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_signed_char = {"_p_signed_char", "INT8 *|int8_t *|int_fast8_t *|int_least8_t *|signed char *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_unsigned___int64 = {"_p_unsigned___int64", "DWORD64 *|DWORDLONG *|UINT64 *|ULONG64 *|ULONGLONG *|unsigned __int64 *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_unsigned_char = {"_p_unsigned_char", "BOOLEAN *|BYTE *|FCHAR *|HLINCLIENT *|TBYTE *|UCHAR *|UINT8 *|uint8_t *|uint_fast8_t *|uint_least8_t *|unsigned char *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_unsigned_int = {"_p_unsigned_int", "DWORD32 *|UINT *|UINT32 *|UINT_PTR *|ULONG32 *|uint32_t *|uint_fast16_t *|uint_fast32_t *|uint_least32_t *|uintptr_t *|unsigned int *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_unsigned_long = {"_p_unsigned_long", "DWORD *|DWORD_PTR *|FLONG *|HANDLE_PTR *|LCID *|SIZE_T *|ULONG *|ULONG_PTR *|unsigned long *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_unsigned_long_long = {"_p_unsigned_long_long", "uint64_t *|uint_fast64_t *|uint_least64_t *|uintmax_t *|unsigned long long *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_unsigned_short = {"_p_unsigned_short", "FSHORT *|HLINHW *|LANGID *|UHALF_PTR *|UINT16 *|USHORT *|WORD *|uint16_t *|uint_least16_t *|unsigned short *", 0, 0, (void*)0, 0};

static swig_type_info *swig_type_initial[] = {
  &_swigt__p_ByteArray,
  &_swigt__p_HLINCLIENT_JS,
  &_swigt__p_HLINHW_JS,
  &_swigt__p_INT64_JS,
  &_swigt__p_INT_JS,
  &_swigt__p_TLINFrameEntry,
  &_swigt__p_TLINHardwareStatus,
  &_swigt__p_TLINMsg,
  &_swigt__p_TLINRcvMsg,
  &_swigt__p_TLINScheduleSlot,
  &_swigt__p_TLINVersion,
  &_swigt__p___int64,
  &_swigt__p_char,
  &_swigt__p_float,
  &_swigt__p_int,
  &_swigt__p_long,
  &_swigt__p_long_long,
  &_swigt__p_p_char,
  &_swigt__p_p_unsigned_long,
  &_swigt__p_short,
  &_swigt__p_signed___int64,
  &_swigt__p_signed_char,
  &_swigt__p_unsigned___int64,
  &_swigt__p_unsigned_char,
  &_swigt__p_unsigned_int,
  &_swigt__p_unsigned_long,
  &_swigt__p_unsigned_long_long,
  &_swigt__p_unsigned_short,
};

static swig_cast_info _swigc__p_ByteArray[] = {  {&_swigt__p_ByteArray, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_HLINCLIENT_JS[] = {  {&_swigt__p_HLINCLIENT_JS, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_HLINHW_JS[] = {  {&_swigt__p_HLINHW_JS, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_INT64_JS[] = {  {&_swigt__p_INT64_JS, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_INT_JS[] = {  {&_swigt__p_INT_JS, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_TLINFrameEntry[] = {  {&_swigt__p_TLINFrameEntry, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_TLINHardwareStatus[] = {  {&_swigt__p_TLINHardwareStatus, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_TLINMsg[] = {  {&_swigt__p_TLINMsg, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_TLINRcvMsg[] = {  {&_swigt__p_TLINRcvMsg, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_TLINScheduleSlot[] = {  {&_swigt__p_TLINScheduleSlot, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_TLINVersion[] = {  {&_swigt__p_TLINVersion, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p___int64[] = {  {&_swigt__p___int64, 0, 0, 0},  {&_swigt__p_INT64_JS, _p_INT64_JSTo_p___int64, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_char[] = {  {&_swigt__p_char, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_float[] = {  {&_swigt__p_float, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_int[] = {  {&_swigt__p_int, 0, 0, 0},  {&_swigt__p_INT_JS, _p_INT_JSTo_p_int, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_long[] = {  {&_swigt__p_long, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_long_long[] = {  {&_swigt__p_long_long, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_p_char[] = {  {&_swigt__p_p_char, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_p_unsigned_long[] = {  {&_swigt__p_p_unsigned_long, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_short[] = {  {&_swigt__p_short, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_signed___int64[] = {  {&_swigt__p_signed___int64, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_signed_char[] = {  {&_swigt__p_signed_char, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_unsigned___int64[] = {  {&_swigt__p_unsigned___int64, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_unsigned_char[] = {  {&_swigt__p_unsigned_char, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_unsigned_int[] = {  {&_swigt__p_unsigned_int, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_unsigned_long[] = {  {&_swigt__p_unsigned_long, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_unsigned_long_long[] = {  {&_swigt__p_unsigned_long_long, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_unsigned_short[] = {  {&_swigt__p_unsigned_short, 0, 0, 0},{0, 0, 0, 0}};

static swig_cast_info *swig_cast_initial[] = {
  _swigc__p_ByteArray,
  _swigc__p_HLINCLIENT_JS,
  _swigc__p_HLINHW_JS,
  _swigc__p_INT64_JS,
  _swigc__p_INT_JS,
  _swigc__p_TLINFrameEntry,
  _swigc__p_TLINHardwareStatus,
  _swigc__p_TLINMsg,
  _swigc__p_TLINRcvMsg,
  _swigc__p_TLINScheduleSlot,
  _swigc__p_TLINVersion,
  _swigc__p___int64,
  _swigc__p_char,
  _swigc__p_float,
  _swigc__p_int,
  _swigc__p_long,
  _swigc__p_long_long,
  _swigc__p_p_char,
  _swigc__p_p_unsigned_long,
  _swigc__p_short,
  _swigc__p_signed___int64,
  _swigc__p_signed_char,
  _swigc__p_unsigned___int64,
  _swigc__p_unsigned_char,
  _swigc__p_unsigned_int,
  _swigc__p_unsigned_long,
  _swigc__p_unsigned_long_long,
  _swigc__p_unsigned_short,
};


/* -------- TYPE CONVERSION AND EQUIVALENCE RULES (END) -------- */




EnvInstanceData::EnvInstanceData(Napi::Env env, swig_module_info *swig_module) :
env(env), SWIG_NAPI_ObjectWrapCtor(nullptr), ctor(nullptr), swig_module(swig_module) {
  ctor = new Napi::FunctionReference*[swig_module->size + 1];
  for (size_t i = 0; i <= swig_module->size; i++) {
    ctor[i] = nullptr;
  }
}

EnvInstanceData::~EnvInstanceData() {
  for (size_t i = 0; i <= swig_module->size; i++) {
    if (ctor[i] != nullptr)
      delete ctor[i];
    ctor[i] = nullptr;
  }
  delete [] ctor;
  delete SWIG_NAPI_ObjectWrapCtor;
}

SWIGRUNTIME void
SWIG_NAPI_SetModule(Napi::Env env, swig_module_info *swig_module) {
  auto data = new EnvInstanceData(env, swig_module);
  env.SetInstanceData(data);
}

SWIGRUNTIME swig_module_info *
SWIG_NAPI_GetModule(Napi::Env env) {
  auto data = env.GetInstanceData<EnvInstanceData>();
  if (data == nullptr) return nullptr;
  return data->swig_module;
}

#define SWIG_GetModule(clientdata)                SWIG_NAPI_GetModule(clientdata)
#define SWIG_SetModule(clientdata, pointer)       SWIG_NAPI_SetModule(clientdata, pointer)
#define SWIG_INIT_CLIENT_DATA_TYPE                Napi::Env


/* -----------------------------------------------------------------------------
 * Type initialization:
 * This problem is tough by the requirement that no dynamic
 * memory is used. Also, since swig_type_info structures store pointers to
 * swig_cast_info structures and swig_cast_info structures store pointers back
 * to swig_type_info structures, we need some lookup code at initialization.
 * The idea is that swig generates all the structures that are needed.
 * The runtime then collects these partially filled structures.
 * The SWIG_InitializeModule function takes these initial arrays out of
 * swig_module, and does all the lookup, filling in the swig_module.types
 * array with the correct data and linking the correct swig_cast_info
 * structures together.
 *
 * The generated swig_type_info structures are assigned statically to an initial
 * array. We just loop through that array, and handle each type individually.
 * First we lookup if this type has been already loaded, and if so, use the
 * loaded structure instead of the generated one. Then we have to fill in the
 * cast linked list. The cast data is initially stored in something like a
 * two-dimensional array. Each row corresponds to a type (there are the same
 * number of rows as there are in the swig_type_initial array). Each entry in
 * a column is one of the swig_cast_info structures for that type.
 * The cast_initial array is actually an array of arrays, because each row has
 * a variable number of columns. So to actually build the cast linked list,
 * we find the array of casts associated with the type, and loop through it
 * adding the casts to the list. The one last trick we need to do is making
 * sure the type pointer in the swig_cast_info struct is correct.
 *
 * First off, we lookup the cast->type name to see if it is already loaded.
 * There are three cases to handle:
 *  1) If the cast->type has already been loaded AND the type we are adding
 *     casting info to has not been loaded (it is in this module), THEN we
 *     replace the cast->type pointer with the type pointer that has already
 *     been loaded.
 *  2) If BOTH types (the one we are adding casting info to, and the
 *     cast->type) are loaded, THEN the cast info has already been loaded by
 *     the previous module so we just ignore it.
 *  3) Finally, if cast->type has not already been loaded, then we add that
 *     swig_cast_info to the linked list (because the cast->type) pointer will
 *     be correct.
 * ----------------------------------------------------------------------------- */

#ifdef __cplusplus
extern "C" {
#if 0
} /* c-mode */
#endif
#endif

#if 0
#define SWIGRUNTIME_DEBUG
#endif

#ifndef SWIG_INIT_CLIENT_DATA_TYPE
#define SWIG_INIT_CLIENT_DATA_TYPE void *
#endif

SWIGRUNTIME void
SWIG_InitializeModule(SWIG_INIT_CLIENT_DATA_TYPE clientdata) {
  size_t i;
  swig_module_info *module_head, *iter;
  int init;

  /* check to see if the circular list has been setup, if not, set it up */
  if (swig_module.next==0) {
    /* Initialize the swig_module */
    swig_module.type_initial = swig_type_initial;
    swig_module.cast_initial = swig_cast_initial;
    swig_module.next = &swig_module;
    init = 1;
  } else {
    init = 0;
  }

  /* Try and load any already created modules */
  module_head = SWIG_GetModule(clientdata);
  if (!module_head) {
    /* This is the first module loaded for this interpreter */
    /* so set the swig module into the interpreter */
    SWIG_SetModule(clientdata, &swig_module);
  } else {
    /* the interpreter has loaded a SWIG module, but has it loaded this one? */
    iter=module_head;
    do {
      if (iter==&swig_module) {
        /* Our module is already in the list, so there's nothing more to do. */
        return;
      }
      iter=iter->next;
    } while (iter!= module_head);

    /* otherwise we must add our module into the list */
    swig_module.next = module_head->next;
    module_head->next = &swig_module;
  }

  /* When multiple interpreters are used, a module could have already been initialized in
     a different interpreter, but not yet have a pointer in this interpreter.
     In this case, we do not want to continue adding types... everything should be
     set up already */
  if (init == 0) return;

  /* Now work on filling in swig_module.types */
#ifdef SWIGRUNTIME_DEBUG
  printf("SWIG_InitializeModule: size %lu\n", (unsigned long)swig_module.size);
#endif
  for (i = 0; i < swig_module.size; ++i) {
    swig_type_info *type = 0;
    swig_type_info *ret;
    swig_cast_info *cast;

#ifdef SWIGRUNTIME_DEBUG
    printf("SWIG_InitializeModule: type %lu %s\n", (unsigned long)i, swig_module.type_initial[i]->name);
#endif

    /* if there is another module already loaded */
    if (swig_module.next != &swig_module) {
      type = SWIG_MangledTypeQueryModule(swig_module.next, &swig_module, swig_module.type_initial[i]->name);
    }
    if (type) {
      /* Overwrite clientdata field */
#ifdef SWIGRUNTIME_DEBUG
      printf("SWIG_InitializeModule: found type %s\n", type->name);
#endif
      if (swig_module.type_initial[i]->clientdata) {
	type->clientdata = swig_module.type_initial[i]->clientdata;
#ifdef SWIGRUNTIME_DEBUG
      printf("SWIG_InitializeModule: found and overwrite type %s \n", type->name);
#endif
      }
    } else {
      type = swig_module.type_initial[i];
    }

    /* Insert casting types */
    cast = swig_module.cast_initial[i];
    while (cast->type) {

      /* Don't need to add information already in the list */
      ret = 0;
#ifdef SWIGRUNTIME_DEBUG
      printf("SWIG_InitializeModule: look cast %s\n", cast->type->name);
#endif
      if (swig_module.next != &swig_module) {
        ret = SWIG_MangledTypeQueryModule(swig_module.next, &swig_module, cast->type->name);
#ifdef SWIGRUNTIME_DEBUG
	if (ret) printf("SWIG_InitializeModule: found cast %s\n", ret->name);
#endif
      }
      if (ret) {
	if (type == swig_module.type_initial[i]) {
#ifdef SWIGRUNTIME_DEBUG
	  printf("SWIG_InitializeModule: skip old type %s\n", ret->name);
#endif
	  cast->type = ret;
	  ret = 0;
	} else {
	  /* Check for casting already in the list */
	  swig_cast_info *ocast = SWIG_TypeCheck(ret->name, type);
#ifdef SWIGRUNTIME_DEBUG
	  if (ocast) printf("SWIG_InitializeModule: skip old cast %s\n", ret->name);
#endif
	  if (!ocast) ret = 0;
	}
      }

      if (!ret) {
#ifdef SWIGRUNTIME_DEBUG
	printf("SWIG_InitializeModule: adding cast %s\n", cast->type->name);
#endif
        if (type->cast) {
          type->cast->prev = cast;
          cast->next = type->cast;
        }
        type->cast = cast;
      }
      cast++;
    }
    /* Set entry in modules->types array equal to the type */
    swig_module.types[i] = type;
  }
  swig_module.types[i] = 0;

#ifdef SWIGRUNTIME_DEBUG
  printf("**** SWIG_InitializeModule: Cast List ******\n");
  for (i = 0; i < swig_module.size; ++i) {
    int j = 0;
    swig_cast_info *cast = swig_module.cast_initial[i];
    printf("SWIG_InitializeModule: type %lu %s\n", (unsigned long)i, swig_module.type_initial[i]->name);
    while (cast->type) {
      printf("SWIG_InitializeModule: cast type %s\n", cast->type->name);
      cast++;
      ++j;
    }
  printf("---- Total casts: %d\n",j);
  }
  printf("**** SWIG_InitializeModule: Cast List ******\n");
#endif
}

/* This function will propagate the clientdata field of type to
* any new swig_type_info structures that have been added into the list
* of equivalent types.  It is like calling
* SWIG_TypeClientData(type, clientdata) a second time.
*/
SWIGRUNTIME void
SWIG_PropagateClientData(void) {
  size_t i;
  swig_cast_info *equiv;
  static int init_run = 0;

  if (init_run) return;
  init_run = 1;

  for (i = 0; i < swig_module.size; i++) {
    if (swig_module.types[i]->clientdata) {
      equiv = swig_module.types[i]->cast;
      while (equiv) {
        if (!equiv->converter) {
          if (equiv->type && !equiv->type->clientdata)
            SWIG_TypeClientData(equiv->type, swig_module.types[i]->clientdata);
        }
        equiv = equiv->next;
      }
    }
  }
}

#ifdef __cplusplus
#if 0
{ /* c-mode */
#endif
}
#endif


Napi::Object Init(Napi::Env env, Napi::Object exports) {
  SWIG_InitializeModule(env);



extern void CreateTSFN(const Napi::CallbackInfo &info);
extern void FreeTSFN(const Napi::CallbackInfo &info);


do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("CreateTSFN", CreateTSFN);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);

do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("FreeTSFN", FreeTSFN);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
	pd
  }));
} while (0);


  Napi::Function SWIG_NAPI_ObjectWrap_ctor = SWIG_NAPI_ObjectWrap_inst::GetClass(env);
  Napi::FunctionReference *SWIG_NAPI_ObjectWrap_ctor_ref = new Napi::FunctionReference();
  *SWIG_NAPI_ObjectWrap_ctor_ref = Napi::Persistent(SWIG_NAPI_ObjectWrap_ctor);
  env.GetInstanceData<EnvInstanceData>()->SWIG_NAPI_ObjectWrapCtor = SWIG_NAPI_ObjectWrap_ctor_ref;

  /* create objects for namespaces */
  

  /* register classes */
  /* Class: INT64_JS (_exports_INT64_JS) */
// jsnapi_registerclass
Napi::Function _exports_INT64_JS_ctor = _exports_INT64_JS_inst::GetClass(env);
exports.Set("INT64_JS", _exports_INT64_JS_ctor);
if (SWIGTYPE_p_INT64_JS->clientdata == nullptr) {
  SWIGTYPE_p_INT64_JS->clientdata = new size_t(0);
}
Napi::FunctionReference *_exports_INT64_JS_ctor_ref = new Napi::FunctionReference();
*_exports_INT64_JS_ctor_ref = Napi::Persistent(_exports_INT64_JS_ctor);
env.GetInstanceData<EnvInstanceData>()->ctor[0] = _exports_INT64_JS_ctor_ref;
/* Class: INT_JS (_exports_INT_JS) */
// jsnapi_registerclass
Napi::Function _exports_INT_JS_ctor = _exports_INT_JS_inst::GetClass(env);
exports.Set("INT_JS", _exports_INT_JS_ctor);
if (SWIGTYPE_p_INT_JS->clientdata == nullptr) {
  SWIGTYPE_p_INT_JS->clientdata = new size_t(1);
}
Napi::FunctionReference *_exports_INT_JS_ctor_ref = new Napi::FunctionReference();
*_exports_INT_JS_ctor_ref = Napi::Persistent(_exports_INT_JS_ctor);
env.GetInstanceData<EnvInstanceData>()->ctor[1] = _exports_INT_JS_ctor_ref;
/* Class: HLINCLIENT_JS (_exports_HLINCLIENT_JS) */
// jsnapi_registerclass
Napi::Function _exports_HLINCLIENT_JS_ctor = _exports_HLINCLIENT_JS_inst::GetClass(env);
exports.Set("HLINCLIENT_JS", _exports_HLINCLIENT_JS_ctor);
if (SWIGTYPE_p_HLINCLIENT_JS->clientdata == nullptr) {
  SWIGTYPE_p_HLINCLIENT_JS->clientdata = new size_t(2);
}
Napi::FunctionReference *_exports_HLINCLIENT_JS_ctor_ref = new Napi::FunctionReference();
*_exports_HLINCLIENT_JS_ctor_ref = Napi::Persistent(_exports_HLINCLIENT_JS_ctor);
env.GetInstanceData<EnvInstanceData>()->ctor[2] = _exports_HLINCLIENT_JS_ctor_ref;
/* Class: ByteArray (_exports_ByteArray) */
// jsnapi_registerclass
Napi::Function _exports_ByteArray_ctor = _exports_ByteArray_inst::GetClass(env);
exports.Set("ByteArray", _exports_ByteArray_ctor);
if (SWIGTYPE_p_ByteArray->clientdata == nullptr) {
  SWIGTYPE_p_ByteArray->clientdata = new size_t(3);
}
Napi::FunctionReference *_exports_ByteArray_ctor_ref = new Napi::FunctionReference();
*_exports_ByteArray_ctor_ref = Napi::Persistent(_exports_ByteArray_ctor);
env.GetInstanceData<EnvInstanceData>()->ctor[3] = _exports_ByteArray_ctor_ref;
/* Class: HLINHW_JS (_exports_HLINHW_JS) */
// jsnapi_registerclass
Napi::Function _exports_HLINHW_JS_ctor = _exports_HLINHW_JS_inst::GetClass(env);
exports.Set("HLINHW_JS", _exports_HLINHW_JS_ctor);
if (SWIGTYPE_p_HLINHW_JS->clientdata == nullptr) {
  SWIGTYPE_p_HLINHW_JS->clientdata = new size_t(4);
}
Napi::FunctionReference *_exports_HLINHW_JS_ctor_ref = new Napi::FunctionReference();
*_exports_HLINHW_JS_ctor_ref = Napi::Persistent(_exports_HLINHW_JS_ctor);
env.GetInstanceData<EnvInstanceData>()->ctor[4] = _exports_HLINHW_JS_ctor_ref;
/* Class: TLINVersion (_exports_TLINVersion) */
// jsnapi_registerclass
Napi::Function _exports_TLINVersion_ctor = _exports_TLINVersion_inst::GetClass(env);
exports.Set("TLINVersion", _exports_TLINVersion_ctor);
if (SWIGTYPE_p_TLINVersion->clientdata == nullptr) {
  SWIGTYPE_p_TLINVersion->clientdata = new size_t(5);
}
Napi::FunctionReference *_exports_TLINVersion_ctor_ref = new Napi::FunctionReference();
*_exports_TLINVersion_ctor_ref = Napi::Persistent(_exports_TLINVersion_ctor);
env.GetInstanceData<EnvInstanceData>()->ctor[5] = _exports_TLINVersion_ctor_ref;
/* Class: TLINMsg (_exports_TLINMsg) */
// jsnapi_registerclass
Napi::Function _exports_TLINMsg_ctor = _exports_TLINMsg_inst::GetClass(env);
exports.Set("TLINMsg", _exports_TLINMsg_ctor);
if (SWIGTYPE_p_TLINMsg->clientdata == nullptr) {
  SWIGTYPE_p_TLINMsg->clientdata = new size_t(6);
}
Napi::FunctionReference *_exports_TLINMsg_ctor_ref = new Napi::FunctionReference();
*_exports_TLINMsg_ctor_ref = Napi::Persistent(_exports_TLINMsg_ctor);
env.GetInstanceData<EnvInstanceData>()->ctor[6] = _exports_TLINMsg_ctor_ref;
/* Class: TLINRcvMsg (_exports_TLINRcvMsg) */
// jsnapi_registerclass
Napi::Function _exports_TLINRcvMsg_ctor = _exports_TLINRcvMsg_inst::GetClass(env);
exports.Set("TLINRcvMsg", _exports_TLINRcvMsg_ctor);
if (SWIGTYPE_p_TLINRcvMsg->clientdata == nullptr) {
  SWIGTYPE_p_TLINRcvMsg->clientdata = new size_t(7);
}
Napi::FunctionReference *_exports_TLINRcvMsg_ctor_ref = new Napi::FunctionReference();
*_exports_TLINRcvMsg_ctor_ref = Napi::Persistent(_exports_TLINRcvMsg_ctor);
env.GetInstanceData<EnvInstanceData>()->ctor[7] = _exports_TLINRcvMsg_ctor_ref;
/* Class: TLINFrameEntry (_exports_TLINFrameEntry) */
// jsnapi_registerclass
Napi::Function _exports_TLINFrameEntry_ctor = _exports_TLINFrameEntry_inst::GetClass(env);
exports.Set("TLINFrameEntry", _exports_TLINFrameEntry_ctor);
if (SWIGTYPE_p_TLINFrameEntry->clientdata == nullptr) {
  SWIGTYPE_p_TLINFrameEntry->clientdata = new size_t(8);
}
Napi::FunctionReference *_exports_TLINFrameEntry_ctor_ref = new Napi::FunctionReference();
*_exports_TLINFrameEntry_ctor_ref = Napi::Persistent(_exports_TLINFrameEntry_ctor);
env.GetInstanceData<EnvInstanceData>()->ctor[8] = _exports_TLINFrameEntry_ctor_ref;
/* Class: TLINScheduleSlot (_exports_TLINScheduleSlot) */
// jsnapi_registerclass
Napi::Function _exports_TLINScheduleSlot_ctor = _exports_TLINScheduleSlot_inst::GetClass(env);
exports.Set("TLINScheduleSlot", _exports_TLINScheduleSlot_ctor);
if (SWIGTYPE_p_TLINScheduleSlot->clientdata == nullptr) {
  SWIGTYPE_p_TLINScheduleSlot->clientdata = new size_t(9);
}
Napi::FunctionReference *_exports_TLINScheduleSlot_ctor_ref = new Napi::FunctionReference();
*_exports_TLINScheduleSlot_ctor_ref = Napi::Persistent(_exports_TLINScheduleSlot_ctor);
env.GetInstanceData<EnvInstanceData>()->ctor[9] = _exports_TLINScheduleSlot_ctor_ref;
/* Class: TLINHardwareStatus (_exports_TLINHardwareStatus) */
// jsnapi_registerclass
Napi::Function _exports_TLINHardwareStatus_ctor = _exports_TLINHardwareStatus_inst::GetClass(env);
exports.Set("TLINHardwareStatus", _exports_TLINHardwareStatus_ctor);
if (SWIGTYPE_p_TLINHardwareStatus->clientdata == nullptr) {
  SWIGTYPE_p_TLINHardwareStatus->clientdata = new size_t(10);
}
Napi::FunctionReference *_exports_TLINHardwareStatus_ctor_ref = new Napi::FunctionReference();
*_exports_TLINHardwareStatus_ctor_ref = Napi::Persistent(_exports_TLINHardwareStatus_ctor);
env.GetInstanceData<EnvInstanceData>()->ctor[10] = _exports_TLINHardwareStatus_ctor_ref;


  /* enable inheritance */
  
Napi::Value jsObjectValue, jsSetProtoValue;
Napi::Object jsObject;
Napi::Function setProto;
NAPI_CHECK_RESULT(env.Global().Get("Object"), jsObjectValue);
NAPI_CHECK_RESULT(jsObjectValue.ToObject(), jsObject);
NAPI_CHECK_RESULT(jsObject.Get("setPrototypeOf"), jsSetProtoValue);
setProto = jsSetProtoValue.As<Napi::Function>();



  /* setup inheritances */
  
// Inheritance for _exports_INT64_JS (INT64_JS) <- SWIG_NAPI_ObjectWrap
// jsnapi_setup_inheritance
do {
  Napi::Value protoBase, protoSub;
  NAPI_CHECK_RESULT(_exports_INT64_JS_ctor.Get("prototype"), protoSub);
  NAPI_CHECK_RESULT(SWIG_NAPI_ObjectWrap_ctor.Get("prototype"), protoBase);
  NAPI_CHECK_MAYBE(setProto.Call({
    _exports_INT64_JS_ctor, SWIG_NAPI_ObjectWrap_ctor
  }));
  NAPI_CHECK_MAYBE(setProto.Call({
    protoSub, protoBase
  }));
} while (0);


// Inheritance for _exports_INT_JS (INT_JS) <- SWIG_NAPI_ObjectWrap
// jsnapi_setup_inheritance
do {
  Napi::Value protoBase, protoSub;
  NAPI_CHECK_RESULT(_exports_INT_JS_ctor.Get("prototype"), protoSub);
  NAPI_CHECK_RESULT(SWIG_NAPI_ObjectWrap_ctor.Get("prototype"), protoBase);
  NAPI_CHECK_MAYBE(setProto.Call({
    _exports_INT_JS_ctor, SWIG_NAPI_ObjectWrap_ctor
  }));
  NAPI_CHECK_MAYBE(setProto.Call({
    protoSub, protoBase
  }));
} while (0);


// Inheritance for _exports_HLINCLIENT_JS (HLINCLIENT_JS) <- SWIG_NAPI_ObjectWrap
// jsnapi_setup_inheritance
do {
  Napi::Value protoBase, protoSub;
  NAPI_CHECK_RESULT(_exports_HLINCLIENT_JS_ctor.Get("prototype"), protoSub);
  NAPI_CHECK_RESULT(SWIG_NAPI_ObjectWrap_ctor.Get("prototype"), protoBase);
  NAPI_CHECK_MAYBE(setProto.Call({
    _exports_HLINCLIENT_JS_ctor, SWIG_NAPI_ObjectWrap_ctor
  }));
  NAPI_CHECK_MAYBE(setProto.Call({
    protoSub, protoBase
  }));
} while (0);


// Inheritance for _exports_ByteArray (ByteArray) <- SWIG_NAPI_ObjectWrap
// jsnapi_setup_inheritance
do {
  Napi::Value protoBase, protoSub;
  NAPI_CHECK_RESULT(_exports_ByteArray_ctor.Get("prototype"), protoSub);
  NAPI_CHECK_RESULT(SWIG_NAPI_ObjectWrap_ctor.Get("prototype"), protoBase);
  NAPI_CHECK_MAYBE(setProto.Call({
    _exports_ByteArray_ctor, SWIG_NAPI_ObjectWrap_ctor
  }));
  NAPI_CHECK_MAYBE(setProto.Call({
    protoSub, protoBase
  }));
} while (0);


// Inheritance for _exports_HLINHW_JS (HLINHW_JS) <- SWIG_NAPI_ObjectWrap
// jsnapi_setup_inheritance
do {
  Napi::Value protoBase, protoSub;
  NAPI_CHECK_RESULT(_exports_HLINHW_JS_ctor.Get("prototype"), protoSub);
  NAPI_CHECK_RESULT(SWIG_NAPI_ObjectWrap_ctor.Get("prototype"), protoBase);
  NAPI_CHECK_MAYBE(setProto.Call({
    _exports_HLINHW_JS_ctor, SWIG_NAPI_ObjectWrap_ctor
  }));
  NAPI_CHECK_MAYBE(setProto.Call({
    protoSub, protoBase
  }));
} while (0);


// Inheritance for _exports_TLINVersion (TLINVersion) <- SWIG_NAPI_ObjectWrap
// jsnapi_setup_inheritance
do {
  Napi::Value protoBase, protoSub;
  NAPI_CHECK_RESULT(_exports_TLINVersion_ctor.Get("prototype"), protoSub);
  NAPI_CHECK_RESULT(SWIG_NAPI_ObjectWrap_ctor.Get("prototype"), protoBase);
  NAPI_CHECK_MAYBE(setProto.Call({
    _exports_TLINVersion_ctor, SWIG_NAPI_ObjectWrap_ctor
  }));
  NAPI_CHECK_MAYBE(setProto.Call({
    protoSub, protoBase
  }));
} while (0);


// Inheritance for _exports_TLINMsg (TLINMsg) <- SWIG_NAPI_ObjectWrap
// jsnapi_setup_inheritance
do {
  Napi::Value protoBase, protoSub;
  NAPI_CHECK_RESULT(_exports_TLINMsg_ctor.Get("prototype"), protoSub);
  NAPI_CHECK_RESULT(SWIG_NAPI_ObjectWrap_ctor.Get("prototype"), protoBase);
  NAPI_CHECK_MAYBE(setProto.Call({
    _exports_TLINMsg_ctor, SWIG_NAPI_ObjectWrap_ctor
  }));
  NAPI_CHECK_MAYBE(setProto.Call({
    protoSub, protoBase
  }));
} while (0);


// Inheritance for _exports_TLINRcvMsg (TLINRcvMsg) <- SWIG_NAPI_ObjectWrap
// jsnapi_setup_inheritance
do {
  Napi::Value protoBase, protoSub;
  NAPI_CHECK_RESULT(_exports_TLINRcvMsg_ctor.Get("prototype"), protoSub);
  NAPI_CHECK_RESULT(SWIG_NAPI_ObjectWrap_ctor.Get("prototype"), protoBase);
  NAPI_CHECK_MAYBE(setProto.Call({
    _exports_TLINRcvMsg_ctor, SWIG_NAPI_ObjectWrap_ctor
  }));
  NAPI_CHECK_MAYBE(setProto.Call({
    protoSub, protoBase
  }));
} while (0);


// Inheritance for _exports_TLINFrameEntry (TLINFrameEntry) <- SWIG_NAPI_ObjectWrap
// jsnapi_setup_inheritance
do {
  Napi::Value protoBase, protoSub;
  NAPI_CHECK_RESULT(_exports_TLINFrameEntry_ctor.Get("prototype"), protoSub);
  NAPI_CHECK_RESULT(SWIG_NAPI_ObjectWrap_ctor.Get("prototype"), protoBase);
  NAPI_CHECK_MAYBE(setProto.Call({
    _exports_TLINFrameEntry_ctor, SWIG_NAPI_ObjectWrap_ctor
  }));
  NAPI_CHECK_MAYBE(setProto.Call({
    protoSub, protoBase
  }));
} while (0);


// Inheritance for _exports_TLINScheduleSlot (TLINScheduleSlot) <- SWIG_NAPI_ObjectWrap
// jsnapi_setup_inheritance
do {
  Napi::Value protoBase, protoSub;
  NAPI_CHECK_RESULT(_exports_TLINScheduleSlot_ctor.Get("prototype"), protoSub);
  NAPI_CHECK_RESULT(SWIG_NAPI_ObjectWrap_ctor.Get("prototype"), protoBase);
  NAPI_CHECK_MAYBE(setProto.Call({
    _exports_TLINScheduleSlot_ctor, SWIG_NAPI_ObjectWrap_ctor
  }));
  NAPI_CHECK_MAYBE(setProto.Call({
    protoSub, protoBase
  }));
} while (0);


// Inheritance for _exports_TLINHardwareStatus (TLINHardwareStatus) <- SWIG_NAPI_ObjectWrap
// jsnapi_setup_inheritance
do {
  Napi::Value protoBase, protoSub;
  NAPI_CHECK_RESULT(_exports_TLINHardwareStatus_ctor.Get("prototype"), protoSub);
  NAPI_CHECK_RESULT(SWIG_NAPI_ObjectWrap_ctor.Get("prototype"), protoBase);
  NAPI_CHECK_MAYBE(setProto.Call({
    _exports_TLINHardwareStatus_ctor, SWIG_NAPI_ObjectWrap_ctor
  }));
  NAPI_CHECK_MAYBE(setProto.Call({
    protoSub, protoBase
  }));
} while (0);



  /* create and register namespace objects */
  // jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_INVALID_LIN_HANDLE_get, JS_veto_set_variable>("INVALID_LIN_HANDLE");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_HW_TYPE_USB_get, JS_veto_set_variable>("LIN_HW_TYPE_USB");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_HW_TYPE_USB_PRO_get, JS_veto_set_variable>("LIN_HW_TYPE_USB_PRO");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_HW_TYPE_USB_PRO_FD_get, JS_veto_set_variable>("LIN_HW_TYPE_USB_PRO_FD");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_HW_TYPE_PLIN_USB_get, JS_veto_set_variable>("LIN_HW_TYPE_PLIN_USB");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_MAX_FRAME_ID_get, JS_veto_set_variable>("LIN_MAX_FRAME_ID");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_MAX_SCHEDULES_get, JS_veto_set_variable>("LIN_MAX_SCHEDULES");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_MIN_SCHEDULE_NUMBER_get, JS_veto_set_variable>("LIN_MIN_SCHEDULE_NUMBER");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_MAX_SCHEDULE_NUMBER_get, JS_veto_set_variable>("LIN_MAX_SCHEDULE_NUMBER");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_MAX_SCHEDULE_SLOTS_get, JS_veto_set_variable>("LIN_MAX_SCHEDULE_SLOTS");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_MIN_BAUDRATE_get, JS_veto_set_variable>("LIN_MIN_BAUDRATE");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_MAX_BAUDRATE_get, JS_veto_set_variable>("LIN_MAX_BAUDRATE");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_MAX_NAME_LENGTH_get, JS_veto_set_variable>("LIN_MAX_NAME_LENGTH");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_MAX_USER_DATA_get, JS_veto_set_variable>("LIN_MAX_USER_DATA");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_MIN_BREAK_LENGTH_get, JS_veto_set_variable>("LIN_MIN_BREAK_LENGTH");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_MAX_BREAK_LENGTH_get, JS_veto_set_variable>("LIN_MAX_BREAK_LENGTH");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LIN_MAX_RCV_QUEUE_COUNT_get, JS_veto_set_variable>("LIN_MAX_RCV_QUEUE_COUNT");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_FRAME_FLAG_RESPONSE_ENABLE_get, JS_veto_set_variable>("FRAME_FLAG_RESPONSE_ENABLE");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_FRAME_FLAG_SINGLE_SHOT_get, JS_veto_set_variable>("FRAME_FLAG_SINGLE_SHOT");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_FRAME_FLAG_IGNORE_INIT_DATA_get, JS_veto_set_variable>("FRAME_FLAG_IGNORE_INIT_DATA");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LOG_FLAG_DEFAULT_get, JS_veto_set_variable>("LOG_FLAG_DEFAULT");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LOG_FLAG_ENTRY_get, JS_veto_set_variable>("LOG_FLAG_ENTRY");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LOG_FLAG_PARAMETERS_get, JS_veto_set_variable>("LOG_FLAG_PARAMETERS");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LOG_FLAG_LEAVE_get, JS_veto_set_variable>("LOG_FLAG_LEAVE");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LOG_FLAG_WRITE_get, JS_veto_set_variable>("LOG_FLAG_WRITE");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LOG_FLAG_READ_get, JS_veto_set_variable>("LOG_FLAG_READ");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_LOG_FLAG_ALL_get, JS_veto_set_variable>("LOG_FLAG_ALL");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_MSG_ERR_INCONSISTENT_SYNC_get, JS_veto_set_variable>("MSG_ERR_INCONSISTENT_SYNC");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_MSG_ERR_ID_PARITY_BIT0_get, JS_veto_set_variable>("MSG_ERR_ID_PARITY_BIT0");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_MSG_ERR_ID_PARITY_BIT1_get, JS_veto_set_variable>("MSG_ERR_ID_PARITY_BIT1");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_MSG_ERR_SLAVE_NOT_RESPONDING_get, JS_veto_set_variable>("MSG_ERR_SLAVE_NOT_RESPONDING");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_MSG_ERR_TIMEOUT_get, JS_veto_set_variable>("MSG_ERR_TIMEOUT");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_MSG_ERR_CHECKSUM_get, JS_veto_set_variable>("MSG_ERR_CHECKSUM");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_MSG_ERR_GND_SHORT_get, JS_veto_set_variable>("MSG_ERR_GND_SHORT");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_MSG_ERR_VBAT_SHORT_get, JS_veto_set_variable>("MSG_ERR_VBAT_SHORT");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_MSG_ERR_SLOT_DELAY_get, JS_veto_set_variable>("MSG_ERR_SLOT_DELAY");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_MSG_ERR_OTHER_RESPONSE_get, JS_veto_set_variable>("MSG_ERR_OTHER_RESPONSE");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_clpName_get, JS_veto_set_variable>("clpName");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_clpMessagesOnQueue_get, JS_veto_set_variable>("clpMessagesOnQueue");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_clpWindowHandle_get, JS_veto_set_variable>("clpWindowHandle");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_clpConnectedHardware_get, JS_veto_set_variable>("clpConnectedHardware");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_clpTransmittedMessages_get, JS_veto_set_variable>("clpTransmittedMessages");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_clpReceivedMessages_get, JS_veto_set_variable>("clpReceivedMessages");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_clpReceiveStatusFrames_get, JS_veto_set_variable>("clpReceiveStatusFrames");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_clpOnReceiveEventHandle_get, JS_veto_set_variable>("clpOnReceiveEventHandle");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_clpOnPluginEventHandle_get, JS_veto_set_variable>("clpOnPluginEventHandle");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_clpLogStatus_get, JS_veto_set_variable>("clpLogStatus");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_clpLogConfiguration_get, JS_veto_set_variable>("clpLogConfiguration");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_hwpName_get, JS_veto_set_variable>("hwpName");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_hwpDeviceNumber_get, JS_veto_set_variable>("hwpDeviceNumber");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_hwpChannelNumber_get, JS_veto_set_variable>("hwpChannelNumber");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_hwpConnectedClients_get, JS_veto_set_variable>("hwpConnectedClients");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_hwpMessageFilter_get, JS_veto_set_variable>("hwpMessageFilter");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_hwpBaudrate_get, JS_veto_set_variable>("hwpBaudrate");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_hwpMode_get, JS_veto_set_variable>("hwpMode");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_hwpFirmwareVersion_get, JS_veto_set_variable>("hwpFirmwareVersion");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_hwpBufferOverrunCount_get, JS_veto_set_variable>("hwpBufferOverrunCount");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_hwpBossClient_get, JS_veto_set_variable>("hwpBossClient");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_hwpSerialNumber_get, JS_veto_set_variable>("hwpSerialNumber");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_hwpVersion_get, JS_veto_set_variable>("hwpVersion");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_hwpType_get, JS_veto_set_variable>("hwpType");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_hwpQueueOverrunCount_get, JS_veto_set_variable>("hwpQueueOverrunCount");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_hwpIdNumber_get, JS_veto_set_variable>("hwpIdNumber");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_hwpUserData_get, JS_veto_set_variable>("hwpUserData");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_hwpBreakLength_get, JS_veto_set_variable>("hwpBreakLength");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_hwpLinTermination_get, JS_veto_set_variable>("hwpLinTermination");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_hwpFlashMode_get, JS_veto_set_variable>("hwpFlashMode");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_hwpScheduleActive_get, JS_veto_set_variable>("hwpScheduleActive");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_hwpScheduleState_get, JS_veto_set_variable>("hwpScheduleState");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_hwpScheduleSuspendedSlot_get, JS_veto_set_variable>("hwpScheduleSuspendedSlot");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_mstStandard_get, JS_veto_set_variable>("mstStandard");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_mstBusSleep_get, JS_veto_set_variable>("mstBusSleep");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_mstBusWakeUp_get, JS_veto_set_variable>("mstBusWakeUp");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_mstAutobaudrateTimeOut_get, JS_veto_set_variable>("mstAutobaudrateTimeOut");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_mstAutobaudrateReply_get, JS_veto_set_variable>("mstAutobaudrateReply");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_mstOverrun_get, JS_veto_set_variable>("mstOverrun");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_mstQueueOverrun_get, JS_veto_set_variable>("mstQueueOverrun");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_mstClientQueueOverrun_get, JS_veto_set_variable>("mstClientQueueOverrun");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_sltUnconditional_get, JS_veto_set_variable>("sltUnconditional");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_sltEvent_get, JS_veto_set_variable>("sltEvent");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_sltSporadic_get, JS_veto_set_variable>("sltSporadic");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_sltMasterRequest_get, JS_veto_set_variable>("sltMasterRequest");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_sltSlaveResponse_get, JS_veto_set_variable>("sltSlaveResponse");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_dirDisabled_get, JS_veto_set_variable>("dirDisabled");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_dirPublisher_get, JS_veto_set_variable>("dirPublisher");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_dirSubscriber_get, JS_veto_set_variable>("dirSubscriber");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_dirSubscriberAutoLength_get, JS_veto_set_variable>("dirSubscriberAutoLength");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_cstCustom_get, JS_veto_set_variable>("cstCustom");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_cstClassic_get, JS_veto_set_variable>("cstClassic");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_cstEnhanced_get, JS_veto_set_variable>("cstEnhanced");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_cstAuto_get, JS_veto_set_variable>("cstAuto");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_modNone_get, JS_veto_set_variable>("modNone");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_modSlave_get, JS_veto_set_variable>("modSlave");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_modMaster_get, JS_veto_set_variable>("modMaster");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_hwsNotInitialized_get, JS_veto_set_variable>("hwsNotInitialized");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_hwsAutobaudrate_get, JS_veto_set_variable>("hwsAutobaudrate");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_hwsActive_get, JS_veto_set_variable>("hwsActive");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_hwsSleep_get, JS_veto_set_variable>("hwsSleep");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_hwsShortGround_get, JS_veto_set_variable>("hwsShortGround");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_hwsVBatMissing_get, JS_veto_set_variable>("hwsVBatMissing");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_schNotRunning_get, JS_veto_set_variable>("schNotRunning");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_schSuspended_get, JS_veto_set_variable>("schSuspended");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_schRunning_get, JS_veto_set_variable>("schRunning");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_errOK_get, JS_veto_set_variable>("errOK");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_errXmtQueueFull_get, JS_veto_set_variable>("errXmtQueueFull");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_errIllegalPeriod_get, JS_veto_set_variable>("errIllegalPeriod");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_errRcvQueueEmpty_get, JS_veto_set_variable>("errRcvQueueEmpty");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_errIllegalChecksumType_get, JS_veto_set_variable>("errIllegalChecksumType");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_errIllegalHardware_get, JS_veto_set_variable>("errIllegalHardware");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_errIllegalClient_get, JS_veto_set_variable>("errIllegalClient");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_errWrongParameterType_get, JS_veto_set_variable>("errWrongParameterType");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_errWrongParameterValue_get, JS_veto_set_variable>("errWrongParameterValue");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_errIllegalDirection_get, JS_veto_set_variable>("errIllegalDirection");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_errIllegalLength_get, JS_veto_set_variable>("errIllegalLength");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_errIllegalBaudrate_get, JS_veto_set_variable>("errIllegalBaudrate");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_errIllegalFrameID_get, JS_veto_set_variable>("errIllegalFrameID");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_errBufferInsufficient_get, JS_veto_set_variable>("errBufferInsufficient");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_errIllegalScheduleNo_get, JS_veto_set_variable>("errIllegalScheduleNo");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_errIllegalSlotCount_get, JS_veto_set_variable>("errIllegalSlotCount");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_errIllegalIndex_get, JS_veto_set_variable>("errIllegalIndex");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_errIllegalRange_get, JS_veto_set_variable>("errIllegalRange");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_errIllegalHardwareState_get, JS_veto_set_variable>("errIllegalHardwareState");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_errIllegalSchedulerState_get, JS_veto_set_variable>("errIllegalSchedulerState");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_errIllegalFrameConfiguration_get, JS_veto_set_variable>("errIllegalFrameConfiguration");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_errScheduleSlotPoolFull_get, JS_veto_set_variable>("errScheduleSlotPoolFull");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_errIllegalSchedule_get, JS_veto_set_variable>("errIllegalSchedule");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_errIllegalHardwareMode_get, JS_veto_set_variable>("errIllegalHardwareMode");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_errOutOfResource_get, JS_veto_set_variable>("errOutOfResource");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_errManagerNotLoaded_get, JS_veto_set_variable>("errManagerNotLoaded");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_errManagerNotResponding_get, JS_veto_set_variable>("errManagerNotResponding");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_errMemoryAccess_get, JS_veto_set_variable>("errMemoryAccess");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_errNotImplemented_get, JS_veto_set_variable>("errNotImplemented");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_variable
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Accessor<exports_errUnknown_get, JS_veto_set_variable>("errUnknown");
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_RegisterClient", _wrap_LIN_RegisterClient);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_RemoveClient", _wrap_LIN_RemoveClient);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_ConnectClient", _wrap_LIN_ConnectClient);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_DisconnectClient", _wrap_LIN_DisconnectClient);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_ResetClient", _wrap_LIN_ResetClient);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_SetClientParam", _wrap_LIN_SetClientParam);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_GetClientParam", _wrap_LIN_GetClientParam);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_SetClientFilter", _wrap_LIN_SetClientFilter);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_GetClientFilter", _wrap_LIN_GetClientFilter);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_Read", _wrap_LIN_Read);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_ReadMulti", _wrap_LIN_ReadMulti);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_Write", _wrap_LIN_Write);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_InitializeHardware", _wrap_LIN_InitializeHardware);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_GetAvailableHardware", _wrap_LIN_GetAvailableHardware);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_SetHardwareParam", _wrap_LIN_SetHardwareParam);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_GetHardwareParam", _wrap_LIN_GetHardwareParam);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_ResetHardware", _wrap_LIN_ResetHardware);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_ResetHardwareConfig", _wrap_LIN_ResetHardwareConfig);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_IdentifyHardware", _wrap_LIN_IdentifyHardware);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_RegisterFrameId", _wrap_LIN_RegisterFrameId);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_SetFrameEntry", _wrap_LIN_SetFrameEntry);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_GetFrameEntry", _wrap_LIN_GetFrameEntry);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_UpdateByteArray", _wrap_LIN_UpdateByteArray);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_StartKeepAlive", _wrap_LIN_StartKeepAlive);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_SuspendKeepAlive", _wrap_LIN_SuspendKeepAlive);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_ResumeKeepAlive", _wrap_LIN_ResumeKeepAlive);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_SetSchedule", _wrap_LIN_SetSchedule);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_GetSchedule", _wrap_LIN_GetSchedule);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_DeleteSchedule", _wrap_LIN_DeleteSchedule);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_SetScheduleBreakPoint", _wrap_LIN_SetScheduleBreakPoint);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_StartSchedule", _wrap_LIN_StartSchedule);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_SuspendSchedule", _wrap_LIN_SuspendSchedule);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_ResumeSchedule", _wrap_LIN_ResumeSchedule);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_XmtWakeUp", _wrap_LIN_XmtWakeUp);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_XmtDynamicWakeUp", _wrap_LIN_XmtDynamicWakeUp);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_StartAutoBaud", _wrap_LIN_StartAutoBaud);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_GetStatus", _wrap_LIN_GetStatus);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_CalculateChecksum", _wrap_LIN_CalculateChecksum);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_GetVersion", _wrap_LIN_GetVersion);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_GetVersionInfo", _wrap_LIN_GetVersionInfo);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_GetErrorText", _wrap_LIN_GetErrorText);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_GetPID", _wrap_LIN_GetPID);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_GetTargetTime", _wrap_LIN_GetTargetTime);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_SetResponseRemap", _wrap_LIN_SetResponseRemap);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_GetResponseRemap", _wrap_LIN_GetResponseRemap);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LIN_GetSystemTime", _wrap_LIN_GetSystemTime);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);
// jsnapi_register_global_function
do {
  Napi::PropertyDescriptor pd = Napi::PropertyDescriptor::Function("LoadDll", _wrap_LoadDll);
  NAPI_CHECK_MAYBE(exports.DefineProperties({
    pd
  }));
} while (0);


  return exports;
  goto fail;
fail:
  return Napi::Object();
}

NODE_API_MODULE(xmlpp, Init)
