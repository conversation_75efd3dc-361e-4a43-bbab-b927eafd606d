/* ----------------------------------------------------------------------------
 * This file was automatically generated by SWIG (https://www.swig.org).
 * Version 4.2.1
 *
 * Do not make changes to this file unless you know what you are doing - modify
 * the SWIG interface file instead.
 * ----------------------------------------------------------------------------- */


#define SWIG_VERSION 0x040201
#define SWIGJAVASCRIPT
/* -----------------------------------------------------------------------------
 *  This section contains generic SWIG labels for method/variable
 *  declarations/attributes, and other compiler dependent labels.
 * ----------------------------------------------------------------------------- */

/* template workaround for compilers that cannot correctly implement the C++ standard */
#ifndef SWIGTEMPLATEDISAMBIGUATOR
# if defined(__SUNPRO_CC) && (__SUNPRO_CC <= 0x560)
#  define SWIGTEMPLATEDISAMBIGUATOR template
# elif defined(__HP_aCC)
/* Needed even with `aCC -AA' when `aCC -V' reports HP ANSI C++ B3910B A.03.55 */
/* If we find a maximum version that requires this, the test would be __HP_aCC <= 35500 for A.03.55 */
#  define SWIGTEMPLATEDISAMBIGUATOR template
# else
#  define SWIGTEMPLATEDISAMBIGUATOR
# endif
#endif

/* inline attribute */
#ifndef SWIGINLINE
# if defined(__cplusplus) || (defined(__GNUC__) && !defined(__STRICT_ANSI__))
#   define SWIGINLINE inline
# else
#   define SWIGINLINE
# endif
#endif

/* attribute recognised by some compilers to avoid 'unused' warnings */
#ifndef SWIGUNUSED
# if defined(__GNUC__)
#   if !(defined(__cplusplus)) || (__GNUC__ > 3 || (__GNUC__ == 3 && __GNUC_MINOR__ >= 4))
#     define SWIGUNUSED __attribute__ ((__unused__))
#   else
#     define SWIGUNUSED
#   endif
# elif defined(__ICC)
#   define SWIGUNUSED __attribute__ ((__unused__))
# else
#   define SWIGUNUSED
# endif
#endif

#ifndef SWIG_MSC_UNSUPPRESS_4505
# if defined(_MSC_VER)
#   pragma warning(disable : 4505) /* unreferenced local function has been removed */
# endif
#endif

#ifndef SWIGUNUSEDPARM
# ifdef __cplusplus
#   define SWIGUNUSEDPARM(p)
# else
#   define SWIGUNUSEDPARM(p) p SWIGUNUSED
# endif
#endif

/* internal SWIG method */
#ifndef SWIGINTERN
# define SWIGINTERN static SWIGUNUSED
#endif

/* internal inline SWIG method */
#ifndef SWIGINTERNINLINE
# define SWIGINTERNINLINE SWIGINTERN SWIGINLINE
#endif

/* exporting methods */
#if defined(__GNUC__)
#  if (__GNUC__ >= 4) || (__GNUC__ == 3 && __GNUC_MINOR__ >= 4)
#    ifndef GCC_HASCLASSVISIBILITY
#      define GCC_HASCLASSVISIBILITY
#    endif
#  endif
#endif

#ifndef SWIGEXPORT
# if defined(_WIN32) || defined(__WIN32__) || defined(__CYGWIN__)
#   if defined(STATIC_LINKED)
#     define SWIGEXPORT
#   else
#     define SWIGEXPORT __declspec(dllexport)
#   endif
# else
#   if defined(__GNUC__) && defined(GCC_HASCLASSVISIBILITY)
#     define SWIGEXPORT __attribute__ ((visibility("default")))
#   else
#     define SWIGEXPORT
#   endif
# endif
#endif

/* calling conventions for Windows */
#ifndef SWIGSTDCALL
# if defined(_WIN32) || defined(__WIN32__) || defined(__CYGWIN__)
#   define SWIGSTDCALL __stdcall
# else
#   define SWIGSTDCALL
# endif
#endif

/* Deal with Microsoft's attempt at deprecating C standard runtime functions */
#if !defined(SWIG_NO_CRT_SECURE_NO_DEPRECATE) && defined(_MSC_VER) && !defined(_CRT_SECURE_NO_DEPRECATE)
# define _CRT_SECURE_NO_DEPRECATE
#endif

/* Deal with Microsoft's attempt at deprecating methods in the standard C++ library */
#if !defined(SWIG_NO_SCL_SECURE_NO_DEPRECATE) && defined(_MSC_VER) && !defined(_SCL_SECURE_NO_DEPRECATE)
# define _SCL_SECURE_NO_DEPRECATE
#endif

/* Deal with Apple's deprecated 'AssertMacros.h' from Carbon-framework */
#if defined(__APPLE__) && !defined(__ASSERT_MACROS_DEFINE_VERSIONS_WITHOUT_UNDERSCORES)
# define __ASSERT_MACROS_DEFINE_VERSIONS_WITHOUT_UNDERSCORES 0
#endif

/* Intel's compiler complains if a variable which was never initialised is
 * cast to void, which is a common idiom which we use to indicate that we
 * are aware a variable isn't used.  So we just silence that warning.
 * See: https://github.com/swig/swig/issues/192 for more discussion.
 */
#ifdef __INTEL_COMPILER
# pragma warning disable 592
#endif

#if defined(__cplusplus) && __cplusplus >=201103L
# define SWIG_NULLPTR nullptr
#else
# define SWIG_NULLPTR NULL
#endif 

/* -----------------------------------------------------------------------------
 * swigcompat.swg
 *
 * Macros to provide support compatibility with older C and C++ standards.
 * ----------------------------------------------------------------------------- */

/* C99 and C++11 should provide snprintf, but define SWIG_NO_SNPRINTF
 * if you're missing it.
 */
#if ((defined __STDC_VERSION__ && __STDC_VERSION__ >= 199901L) || \
     (defined __cplusplus && __cplusplus >= 201103L) || \
     defined SWIG_HAVE_SNPRINTF) && \
    !defined SWIG_NO_SNPRINTF
# define SWIG_snprintf(O,S,F,A) snprintf(O,S,F,A)
# define SWIG_snprintf2(O,S,F,A,B) snprintf(O,S,F,A,B)
#else
/* Fallback versions ignore the buffer size, but most of our uses either have a
 * fixed maximum possible size or dynamically allocate a buffer that's large
 * enough.
 */
# define SWIG_snprintf(O,S,F,A) sprintf(O,F,A)
# define SWIG_snprintf2(O,S,F,A,B) sprintf(O,F,A,B)
#endif


#define SWIG_FromCharPtrAndSize(cptr, size) SWIG_Env_FromCharPtrAndSize(env, cptr, size)
#define SWIG_FromCharPtr(cptr)              SWIG_Env_FromCharPtrAndSize(env, cptr, strlen(cptr))


#define SWIG_NAPI_FROM_DECL_ARGS(arg1)              (Napi::Env env, arg1)
#define SWIG_NAPI_FROM_CALL_ARGS(arg1)              (env, arg1)



#define SWIG_exception_fail(code, msg) do { SWIG_Error(code, msg); SWIG_fail; } while(0) 

#define SWIG_contract_assert(expr, msg) do { if (!(expr)) { SWIG_Error(SWIG_RuntimeError, msg); SWIG_fail; } } while (0) 



#if defined(_CPPUNWIND) || defined(__EXCEPTIONS)
#define NAPI_CPP_EXCEPTIONS
#else
#define NAPI_DISABLE_CPP_EXCEPTIONS
#define NODE_ADDON_API_ENABLE_MAYBE
#endif

// This gives us
// Branch Node.js v10.x - from v10.20.0
// Branch Node.js v12.x - from v12.17.0
// Everything from Node.js v14.0.0 on
// Our limiting feature is napi_set_instance_data
#ifndef NAPI_VERSION
#define NAPI_VERSION 6
#elif NAPI_VERSION < 6
#error NAPI_VERSION 6 is the minimum supported target (Node.js >=14, >=12.17, >=10.20)
#endif
#include <napi.h>

#include <errno.h>
#include <limits.h>
#include <stdlib.h>
#include <assert.h>
#include <map>

/* -----------------------------------------------------------------------------
 * swigrun.swg
 *
 * This file contains generic C API SWIG runtime support for pointer
 * type checking.
 * ----------------------------------------------------------------------------- */

/* This should only be incremented when either the layout of swig_type_info changes,
   or for whatever reason, the runtime changes incompatibly */
#define SWIG_RUNTIME_VERSION "4"

/* define SWIG_TYPE_TABLE_NAME as "SWIG_TYPE_TABLE" */
#ifdef SWIG_TYPE_TABLE
# define SWIG_QUOTE_STRING(x) #x
# define SWIG_EXPAND_AND_QUOTE_STRING(x) SWIG_QUOTE_STRING(x)
# define SWIG_TYPE_TABLE_NAME SWIG_EXPAND_AND_QUOTE_STRING(SWIG_TYPE_TABLE)
#else
# define SWIG_TYPE_TABLE_NAME
#endif

/*
  You can use the SWIGRUNTIME and SWIGRUNTIMEINLINE macros for
  creating a static or dynamic library from the SWIG runtime code.
  In 99.9% of the cases, SWIG just needs to declare them as 'static'.

  But only do this if strictly necessary, ie, if you have problems
  with your compiler or suchlike.
*/

#ifndef SWIGRUNTIME
# define SWIGRUNTIME SWIGINTERN
#endif

#ifndef SWIGRUNTIMEINLINE
# define SWIGRUNTIMEINLINE SWIGRUNTIME SWIGINLINE
#endif

/*  Generic buffer size */
#ifndef SWIG_BUFFER_SIZE
# define SWIG_BUFFER_SIZE 1024
#endif

/* Flags for pointer conversions */
#define SWIG_POINTER_DISOWN        0x1
#define SWIG_CAST_NEW_MEMORY       0x2
#define SWIG_POINTER_NO_NULL       0x4
#define SWIG_POINTER_CLEAR         0x8
#define SWIG_POINTER_RELEASE       (SWIG_POINTER_CLEAR | SWIG_POINTER_DISOWN)

/* Flags for new pointer objects */
#define SWIG_POINTER_OWN           0x1


/*
   Flags/methods for returning states.

   The SWIG conversion methods, as ConvertPtr, return an integer
   that tells if the conversion was successful or not. And if not,
   an error code can be returned (see swigerrors.swg for the codes).

   Use the following macros/flags to set or process the returning
   states.

   In old versions of SWIG, code such as the following was usually written:

     if (SWIG_ConvertPtr(obj,vptr,ty.flags) != -1) {
       // success code
     } else {
       //fail code
     }

   Now you can be more explicit:

    int res = SWIG_ConvertPtr(obj,vptr,ty.flags);
    if (SWIG_IsOK(res)) {
      // success code
    } else {
      // fail code
    }

   which is the same really, but now you can also do

    Type *ptr;
    int res = SWIG_ConvertPtr(obj,(void **)(&ptr),ty.flags);
    if (SWIG_IsOK(res)) {
      // success code
      if (SWIG_IsNewObj(res) {
        ...
	delete *ptr;
      } else {
        ...
      }
    } else {
      // fail code
    }

   I.e., now SWIG_ConvertPtr can return new objects and you can
   identify the case and take care of the deallocation. Of course that
   also requires SWIG_ConvertPtr to return new result values, such as

      int SWIG_ConvertPtr(obj, ptr,...) {
        if (<obj is ok>) {
          if (<need new object>) {
            *ptr = <ptr to new allocated object>;
            return SWIG_NEWOBJ;
          } else {
            *ptr = <ptr to old object>;
            return SWIG_OLDOBJ;
          }
        } else {
          return SWIG_BADOBJ;
        }
      }

   Of course, returning the plain '0(success)/-1(fail)' still works, but you can be
   more explicit by returning SWIG_BADOBJ, SWIG_ERROR or any of the
   SWIG errors code.

   Finally, if the SWIG_CASTRANK_MODE is enabled, the result code
   allows returning the 'cast rank', for example, if you have this

       int food(double)
       int fooi(int);

   and you call

      food(1)   // cast rank '1'  (1 -> 1.0)
      fooi(1)   // cast rank '0'

   just use the SWIG_AddCast()/SWIG_CheckState()
*/

#define SWIG_OK                    (0)
/* Runtime errors are < 0 */
#define SWIG_ERROR                 (-1)
/* Errors in range -1 to -99 are in swigerrors.swg (errors for all languages including those not using the runtime) */
/* Errors in range -100 to -199 are language specific errors defined in *errors.swg */
/* Errors < -200 are generic runtime specific errors */
#define SWIG_ERROR_RELEASE_NOT_OWNED (-200)

#define SWIG_IsOK(r)               (r >= 0)
#define SWIG_ArgError(r)           ((r != SWIG_ERROR) ? r : SWIG_TypeError)

/* The CastRankLimit says how many bits are used for the cast rank */
#define SWIG_CASTRANKLIMIT         (1 << 8)
/* The NewMask denotes the object was created (using new/malloc) */
#define SWIG_NEWOBJMASK            (SWIG_CASTRANKLIMIT  << 1)
/* The TmpMask is for in/out typemaps that use temporary objects */
#define SWIG_TMPOBJMASK            (SWIG_NEWOBJMASK << 1)
/* Simple returning values */
#define SWIG_BADOBJ                (SWIG_ERROR)
#define SWIG_OLDOBJ                (SWIG_OK)
#define SWIG_NEWOBJ                (SWIG_OK | SWIG_NEWOBJMASK)
#define SWIG_TMPOBJ                (SWIG_OK | SWIG_TMPOBJMASK)
/* Check, add and del object mask methods */
#define SWIG_AddNewMask(r)         (SWIG_IsOK(r) ? (r | SWIG_NEWOBJMASK) : r)
#define SWIG_DelNewMask(r)         (SWIG_IsOK(r) ? (r & ~SWIG_NEWOBJMASK) : r)
#define SWIG_IsNewObj(r)           (SWIG_IsOK(r) && (r & SWIG_NEWOBJMASK))
#define SWIG_AddTmpMask(r)         (SWIG_IsOK(r) ? (r | SWIG_TMPOBJMASK) : r)
#define SWIG_DelTmpMask(r)         (SWIG_IsOK(r) ? (r & ~SWIG_TMPOBJMASK) : r)
#define SWIG_IsTmpObj(r)           (SWIG_IsOK(r) && (r & SWIG_TMPOBJMASK))

/* Cast-Rank Mode */
#if defined(SWIG_CASTRANK_MODE)
#  ifndef SWIG_TypeRank
#    define SWIG_TypeRank             unsigned long
#  endif
#  ifndef SWIG_MAXCASTRANK            /* Default cast allowed */
#    define SWIG_MAXCASTRANK          (2)
#  endif
#  define SWIG_CASTRANKMASK          ((SWIG_CASTRANKLIMIT) -1)
#  define SWIG_CastRank(r)           (r & SWIG_CASTRANKMASK)
SWIGINTERNINLINE int SWIG_AddCast(int r) {
  return SWIG_IsOK(r) ? ((SWIG_CastRank(r) < SWIG_MAXCASTRANK) ? (r + 1) : SWIG_ERROR) : r;
}
SWIGINTERNINLINE int SWIG_CheckState(int r) {
  return SWIG_IsOK(r) ? SWIG_CastRank(r) + 1 : 0;
}
#else /* no cast-rank mode */
#  define SWIG_AddCast(r) (r)
#  define SWIG_CheckState(r) (SWIG_IsOK(r) ? 1 : 0)
#endif


#include <string.h>

#ifdef __cplusplus
extern "C" {
#endif

typedef void *(*swig_converter_func)(void *, int *);
typedef struct swig_type_info *(*swig_dycast_func)(void **);

/* Structure to store information on one type */
typedef struct swig_type_info {
  const char             *name;			/* mangled name of this type */
  const char             *str;			/* human readable name of this type */
  swig_dycast_func        dcast;		/* dynamic cast function down a hierarchy */
  struct swig_cast_info  *cast;			/* linked list of types that can cast into this type */
  void                   *clientdata;		/* language specific type data */
  int                    owndata;		/* flag if the structure owns the clientdata */
} swig_type_info;

/* Structure to store a type and conversion function used for casting */
typedef struct swig_cast_info {
  swig_type_info         *type;			/* pointer to type that is equivalent to this type */
  swig_converter_func     converter;		/* function to cast the void pointers */
  struct swig_cast_info  *next;			/* pointer to next cast in linked list */
  struct swig_cast_info  *prev;			/* pointer to the previous cast */
} swig_cast_info;

/* Structure used to store module information
 * Each module generates one structure like this, and the runtime collects
 * all of these structures and stores them in a circularly linked list.*/
typedef struct swig_module_info {
  swig_type_info         **types;		/* Array of pointers to swig_type_info structures that are in this module */
  size_t                 size;		        /* Number of types in this module */
  struct swig_module_info *next;		/* Pointer to next element in circularly linked list */
  swig_type_info         **type_initial;	/* Array of initially generated type structures */
  swig_cast_info         **cast_initial;	/* Array of initially generated casting structures */
  void                    *clientdata;		/* Language specific module data */
} swig_module_info;

/*
  Compare two type names skipping the space characters, therefore
  "char*" == "char *" and "Class<int>" == "Class<int >", etc.

  Return 0 when the two name types are equivalent, as in
  strncmp, but skipping ' '.
*/
SWIGRUNTIME int
SWIG_TypeNameComp(const char *f1, const char *l1,
		  const char *f2, const char *l2) {
  for (;(f1 != l1) && (f2 != l2); ++f1, ++f2) {
    while ((*f1 == ' ') && (f1 != l1)) ++f1;
    while ((*f2 == ' ') && (f2 != l2)) ++f2;
    if (*f1 != *f2) return (*f1 > *f2) ? 1 : -1;
  }
  return (int)((l1 - f1) - (l2 - f2));
}

/*
  Check type equivalence in a name list like <name1>|<name2>|...
  Return 0 if equal, -1 if nb < tb, 1 if nb > tb
*/
SWIGRUNTIME int
SWIG_TypeCmp(const char *nb, const char *tb) {
  int equiv = 1;
  const char* te = tb + strlen(tb);
  const char* ne = nb;
  while (equiv != 0 && *ne) {
    for (nb = ne; *ne; ++ne) {
      if (*ne == '|') break;
    }
    equiv = SWIG_TypeNameComp(nb, ne, tb, te);
    if (*ne) ++ne;
  }
  return equiv;
}

/*
  Check type equivalence in a name list like <name1>|<name2>|...
  Return 0 if not equal, 1 if equal
*/
SWIGRUNTIME int
SWIG_TypeEquiv(const char *nb, const char *tb) {
  return SWIG_TypeCmp(nb, tb) == 0 ? 1 : 0;
}

/*
  Check the typename
*/
SWIGRUNTIME swig_cast_info *
SWIG_TypeCheck(const char *c, swig_type_info *ty) {
  if (ty) {
    swig_cast_info *iter = ty->cast;
    while (iter) {
      if (strcmp(iter->type->name, c) == 0) {
        if (iter == ty->cast)
          return iter;
        /* Move iter to the top of the linked list */
        iter->prev->next = iter->next;
        if (iter->next)
          iter->next->prev = iter->prev;
        iter->next = ty->cast;
        iter->prev = 0;
        if (ty->cast) ty->cast->prev = iter;
        ty->cast = iter;
        return iter;
      }
      iter = iter->next;
    }
  }
  return 0;
}

/*
  Identical to SWIG_TypeCheck, except strcmp is replaced with a pointer comparison
*/
SWIGRUNTIME swig_cast_info *
SWIG_TypeCheckStruct(const swig_type_info *from, swig_type_info *ty) {
  if (ty) {
    swig_cast_info *iter = ty->cast;
    while (iter) {
      if (iter->type == from) {
        if (iter == ty->cast)
          return iter;
        /* Move iter to the top of the linked list */
        iter->prev->next = iter->next;
        if (iter->next)
          iter->next->prev = iter->prev;
        iter->next = ty->cast;
        iter->prev = 0;
        if (ty->cast) ty->cast->prev = iter;
        ty->cast = iter;
        return iter;
      }
      iter = iter->next;
    }
  }
  return 0;
}

/*
  Cast a pointer up an inheritance hierarchy
*/
SWIGRUNTIMEINLINE void *
SWIG_TypeCast(swig_cast_info *ty, void *ptr, int *newmemory) {
  return ((!ty) || (!ty->converter)) ? ptr : (*ty->converter)(ptr, newmemory);
}

/*
   Dynamic pointer casting. Down an inheritance hierarchy
*/
SWIGRUNTIME swig_type_info *
SWIG_TypeDynamicCast(swig_type_info *ty, void **ptr) {
  swig_type_info *lastty = ty;
  if (!ty || !ty->dcast) return ty;
  while (ty && (ty->dcast)) {
    ty = (*ty->dcast)(ptr);
    if (ty) lastty = ty;
  }
  return lastty;
}

/*
  Return the name associated with this type
*/
SWIGRUNTIMEINLINE const char *
SWIG_TypeName(const swig_type_info *ty) {
  return ty->name;
}

/*
  Return the pretty name associated with this type,
  that is an unmangled type name in a form presentable to the user.
*/
SWIGRUNTIME const char *
SWIG_TypePrettyName(const swig_type_info *type) {
  /* The "str" field contains the equivalent pretty names of the
     type, separated by vertical-bar characters.  Choose the last
     name. It should be the most specific; a fully resolved name
     but not necessarily with default template parameters expanded. */
  if (!type) return NULL;
  if (type->str != NULL) {
    const char *last_name = type->str;
    const char *s;
    for (s = type->str; *s; s++)
      if (*s == '|') last_name = s+1;
    return last_name;
  }
  else
    return type->name;
}

/*
   Set the clientdata field for a type
*/
SWIGRUNTIME void
SWIG_TypeClientData(swig_type_info *ti, void *clientdata) {
  swig_cast_info *cast = ti->cast;
  /* if (ti->clientdata == clientdata) return; */
  ti->clientdata = clientdata;

  while (cast) {
    if (!cast->converter) {
      swig_type_info *tc = cast->type;
      if (!tc->clientdata) {
	SWIG_TypeClientData(tc, clientdata);
      }
    }
    cast = cast->next;
  }
}
SWIGRUNTIME void
SWIG_TypeNewClientData(swig_type_info *ti, void *clientdata) {
  SWIG_TypeClientData(ti, clientdata);
  ti->owndata = 1;
}

/*
  Search for a swig_type_info structure only by mangled name
  Search is a O(log #types)

  We start searching at module start, and finish searching when start == end.
  Note: if start == end at the beginning of the function, we go all the way around
  the circular list.
*/
SWIGRUNTIME swig_type_info *
SWIG_MangledTypeQueryModule(swig_module_info *start,
                            swig_module_info *end,
		            const char *name) {
  swig_module_info *iter = start;
  do {
    if (iter->size) {
      size_t l = 0;
      size_t r = iter->size - 1;
      do {
	/* since l+r >= 0, we can (>> 1) instead (/ 2) */
	size_t i = (l + r) >> 1;
	const char *iname = iter->types[i]->name;
	if (iname) {
	  int compare = strcmp(name, iname);
	  if (compare == 0) {
	    return iter->types[i];
	  } else if (compare < 0) {
	    if (i) {
	      r = i - 1;
	    } else {
	      break;
	    }
	  } else if (compare > 0) {
	    l = i + 1;
	  }
	} else {
	  break; /* should never happen */
	}
      } while (l <= r);
    }
    iter = iter->next;
  } while (iter != end);
  return 0;
}

/*
  Search for a swig_type_info structure for either a mangled name or a human readable name.
  It first searches the mangled names of the types, which is a O(log #types)
  If a type is not found it then searches the human readable names, which is O(#types).

  We start searching at module start, and finish searching when start == end.
  Note: if start == end at the beginning of the function, we go all the way around
  the circular list.
*/
SWIGRUNTIME swig_type_info *
SWIG_TypeQueryModule(swig_module_info *start,
                     swig_module_info *end,
		     const char *name) {
  /* STEP 1: Search the name field using binary search */
  swig_type_info *ret = SWIG_MangledTypeQueryModule(start, end, name);
  if (ret) {
    return ret;
  } else {
    /* STEP 2: If the type hasn't been found, do a complete search
       of the str field (the human readable name) */
    swig_module_info *iter = start;
    do {
      size_t i = 0;
      for (; i < iter->size; ++i) {
	if (iter->types[i]->str && (SWIG_TypeEquiv(iter->types[i]->str, name)))
	  return iter->types[i];
      }
      iter = iter->next;
    } while (iter != end);
  }

  /* neither found a match */
  return 0;
}

/*
   Pack binary data into a string
*/
SWIGRUNTIME char *
SWIG_PackData(char *c, void *ptr, size_t sz) {
  static const char hex[17] = "0123456789abcdef";
  const unsigned char *u = (unsigned char *) ptr;
  const unsigned char *eu =  u + sz;
  for (; u != eu; ++u) {
    unsigned char uu = *u;
    *(c++) = hex[(uu & 0xf0) >> 4];
    *(c++) = hex[uu & 0xf];
  }
  return c;
}

/*
   Unpack binary data from a string
*/
SWIGRUNTIME const char *
SWIG_UnpackData(const char *c, void *ptr, size_t sz) {
  unsigned char *u = (unsigned char *) ptr;
  const unsigned char *eu = u + sz;
  for (; u != eu; ++u) {
    char d = *(c++);
    unsigned char uu;
    if ((d >= '0') && (d <= '9'))
      uu = (unsigned char)((d - '0') << 4);
    else if ((d >= 'a') && (d <= 'f'))
      uu = (unsigned char)((d - ('a'-10)) << 4);
    else
      return (char *) 0;
    d = *(c++);
    if ((d >= '0') && (d <= '9'))
      uu |= (unsigned char)(d - '0');
    else if ((d >= 'a') && (d <= 'f'))
      uu |= (unsigned char)(d - ('a'-10));
    else
      return (char *) 0;
    *u = uu;
  }
  return c;
}

/*
   Pack 'void *' into a string buffer.
*/
SWIGRUNTIME char *
SWIG_PackVoidPtr(char *buff, void *ptr, const char *name, size_t bsz) {
  char *r = buff;
  if ((2*sizeof(void *) + 2) > bsz) return 0;
  *(r++) = '_';
  r = SWIG_PackData(r,&ptr,sizeof(void *));
  if (strlen(name) + 1 > (bsz - (r - buff))) return 0;
  strcpy(r,name);
  return buff;
}

SWIGRUNTIME const char *
SWIG_UnpackVoidPtr(const char *c, void **ptr, const char *name) {
  if (*c != '_') {
    if (strcmp(c,"NULL") == 0) {
      *ptr = (void *) 0;
      return name;
    } else {
      return 0;
    }
  }
  return SWIG_UnpackData(++c,ptr,sizeof(void *));
}

SWIGRUNTIME char *
SWIG_PackDataName(char *buff, void *ptr, size_t sz, const char *name, size_t bsz) {
  char *r = buff;
  size_t lname = (name ? strlen(name) : 0);
  if ((2*sz + 2 + lname) > bsz) return 0;
  *(r++) = '_';
  r = SWIG_PackData(r,ptr,sz);
  if (lname) {
    strncpy(r,name,lname+1);
  } else {
    *r = 0;
  }
  return buff;
}

SWIGRUNTIME const char *
SWIG_UnpackDataName(const char *c, void *ptr, size_t sz, const char *name) {
  if (*c != '_') {
    if (strcmp(c,"NULL") == 0) {
      memset(ptr,0,sz);
      return name;
    } else {
      return 0;
    }
  }
  return SWIG_UnpackData(++c,ptr,sz);
}

#ifdef __cplusplus
}
#endif

/* SWIG Errors applicable to all language modules, values are reserved from -1 to -99 */
#define  SWIG_UnknownError    	   -1
#define  SWIG_IOError        	   -2
#define  SWIG_RuntimeError   	   -3
#define  SWIG_IndexError     	   -4
#define  SWIG_TypeError      	   -5
#define  SWIG_DivisionByZero 	   -6
#define  SWIG_OverflowError  	   -7
#define  SWIG_SyntaxError    	   -8
#define  SWIG_ValueError     	   -9
#define  SWIG_SystemError    	   -10
#define  SWIG_AttributeError 	   -11
#define  SWIG_MemoryError    	   -12
#define  SWIG_NullReferenceError   -13


/* ---------------------------------------------------------------------------
 * Error handling
 *
 * ---------------------------------------------------------------------------*/

/*
 * We support several forms:
 *
 * SWIG_Raise("Error message")
 * which creates an Error object with the error message
 *
 * SWIG_Raise(SWIG_TypeError, "Type error")
 * which creates the specified error type with the message
 *
 * SWIG_Raise(obj)
 * which throws the object itself
 *
 * SWIG_Raise(obj, "Exception const &", SWIGType_p_Exception)
 * which also throws the object itself and discards the unneeded extra type info
 *
 * These must be functions instead of macros to use the C++ overloading to
 * resolve the arguments
 */
#define SWIG_exception(code, msg)               SWIG_Error(code, msg)
#define SWIG_fail                               goto fail

#ifdef NAPI_CPP_EXCEPTIONS

#define SWIG_Error(code, msg)                   SWIG_NAPI_Raise(env, code, msg)
#define NAPI_CHECK_MAYBE(maybe)                 (maybe)
#define NAPI_CHECK_RESULT(maybe, result)        (result = maybe)

SWIGINTERN void SWIG_NAPI_Raise(Napi::Env env, const char *msg) {
  throw Napi::Error::New(env, msg);
}

SWIGINTERN void SWIG_NAPI_Raise(Napi::Env env, int type, const char *msg) {
  switch(type) {
    default:
    case SWIG_IOError:
    case SWIG_MemoryError:
    case SWIG_SystemError:
    case SWIG_RuntimeError:
    case SWIG_DivisionByZero:
    case SWIG_SyntaxError:
      throw Napi::Error::New(env, msg);
    case SWIG_OverflowError:
    case SWIG_IndexError:
      throw Napi::RangeError::New(env, msg);
    case SWIG_ValueError:
    case SWIG_TypeError:
      throw Napi::TypeError::New(env, msg);
  }
}

SWIGINTERN void SWIG_NAPI_Raise(Napi::Env env, Napi::Value obj,
        const char *msg = nullptr, swig_type_info *info = nullptr) {
  throw Napi::Error(env, obj);
}

#else

#define SWIG_Error(code, msg)     do { SWIG_NAPI_Raise(env, code, msg); SWIG_fail; } while (0)
#define NAPI_CHECK_MAYBE(maybe)   do { if (maybe.IsNothing()) SWIG_fail; } while (0)
#define NAPI_CHECK_RESULT(maybe, result)          \
        do {                                      \
                auto r = maybe;                   \
                if (r.IsNothing()) SWIG_fail;     \
                result = r.Unwrap();              \
        } while (0)

SWIGINTERN void SWIG_NAPI_Raise(Napi::Env env, const char *msg) {
  Napi::Error::New(env, msg).ThrowAsJavaScriptException();
}

SWIGINTERN void SWIG_NAPI_Raise(Napi::Env env, int type, const char *msg) {
  switch(type) {
    default:
    case SWIG_IOError:
    case SWIG_MemoryError:
    case SWIG_SystemError:
    case SWIG_RuntimeError:
    case SWIG_DivisionByZero:
    case SWIG_SyntaxError:
      Napi::Error::New(env, msg).ThrowAsJavaScriptException();
      return;
    case SWIG_OverflowError:
    case SWIG_IndexError:
      Napi::RangeError::New(env, msg).ThrowAsJavaScriptException();
      return;
    case SWIG_ValueError:
    case SWIG_TypeError:
      Napi::TypeError::New(env, msg).ThrowAsJavaScriptException();
      return;
  }
}

SWIGINTERN void SWIG_NAPI_Raise(Napi::Env env, Napi::Value obj,
        const char *msg = nullptr, swig_type_info *info = nullptr) {
  Napi::Error(env, obj).ThrowAsJavaScriptException();
}

#endif

void JS_veto_set_variable(const Napi::CallbackInfo &info) {
  SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}

struct EnvInstanceData {
  Napi::Env env;
  // Base class per-environment constructor, used to check
  // if a JS object is a SWIG wrapper
  Napi::FunctionReference *SWIG_NAPI_ObjectWrapCtor;
  // Per-environment wrapper constructors, indexed by the number in
  // swig_type->clientdata
  Napi::FunctionReference **ctor;
  swig_module_info *swig_module;
  EnvInstanceData(Napi::Env, swig_module_info *);
  ~EnvInstanceData();
};

typedef size_t SWIG_NAPI_ClientData;

// Base class for all wrapped objects,
// used mostly when unwrapping unknown objects
template <typename SWIG_OBJ_WRAP>
class SWIG_NAPI_ObjectWrap_templ : public Napi::ObjectWrap<SWIG_OBJ_WRAP> {
  public:
    void *self;
    bool owned;
    size_t size;
    swig_type_info *info;
    SWIG_NAPI_ObjectWrap_templ(const Napi::CallbackInfo &info);
    SWIG_NAPI_ObjectWrap_templ(bool, const Napi::CallbackInfo &info) :
        Napi::ObjectWrap<SWIG_OBJ_WRAP>(info),
        self(nullptr),
        owned(true),
        size(0),
        info(nullptr)
        {}
    virtual ~SWIG_NAPI_ObjectWrap_templ() {};

    Napi::Value ToString(const Napi::CallbackInfo &info);
};

template <typename SWIG_OBJ_WRAP>
SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>::SWIG_NAPI_ObjectWrap_templ(const Napi::CallbackInfo &info) :
        Napi::ObjectWrap<SWIG_OBJ_WRAP>(info), size(0), info(nullptr) { 
  Napi::Env env = info.Env();
  if (info.Length() == 1 && info[0].IsExternal()) {
    // This constructor has been called internally from C++/SWIG
    // to wrap an already existing C++ object of unknown type in JS
    this->self = info[0].As<Napi::External<void>>().Data();
    this->owned = false;
  } else {
    SWIG_Error(SWIG_ERROR, "This constructor is not accessible from JS");
  }
  return;
  goto fail;
fail:
  return;
}

template <typename SWIG_OBJ_WRAP>
Napi::Value SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>::ToString(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  static char repr[128];
  const char *name = SWIG_TypePrettyName(this->info);
  snprintf(repr, sizeof(repr), "{SwigObject %s (%s) at %p %s}",
    this->info ? this->info->name : "unknown",
    name ? name : "unknown",
    this->self,
    this->owned ? "[owned]" : "[copy]");
  return Napi::String::New(env, repr);
}

class SWIG_NAPI_ObjectWrap_inst : public SWIG_NAPI_ObjectWrap_templ<SWIG_NAPI_ObjectWrap_inst> {
public:
  using SWIG_NAPI_ObjectWrap_templ::SWIG_NAPI_ObjectWrap_templ;
  static Napi::Function GetClass(Napi::Env);
  static void GetMembers(
    Napi::Env,
    std::map<std::string, SWIG_NAPI_ObjectWrap_templ::PropertyDescriptor> &,
    std::map<std::string, SWIG_NAPI_ObjectWrap_templ::PropertyDescriptor> &
  );
};

void SWIG_NAPI_ObjectWrap_inst::GetMembers(
        Napi::Env env,
        std::map<std::string, SWIG_NAPI_ObjectWrap_templ::PropertyDescriptor> &members,
        std::map<std::string, SWIG_NAPI_ObjectWrap_templ::PropertyDescriptor> &
) {
  members.erase("toString");
  members.insert({"toString", SWIG_NAPI_ObjectWrap_templ::InstanceMethod("toString", &SWIG_NAPI_ObjectWrap_templ::ToString)});
}

Napi::Function SWIG_NAPI_ObjectWrap_inst::GetClass(Napi::Env env) {
  return Napi::ObjectWrap<SWIG_NAPI_ObjectWrap_inst>::DefineClass(env, "SwigObject", {});
}

SWIGRUNTIME int SWIG_NAPI_ConvertInstancePtr(Napi::Object objRef, void **ptr, swig_type_info *info, int flags) {
  SWIG_NAPI_ObjectWrap_inst *ow;
  Napi::Env env = objRef.Env();
  if(!objRef.IsObject()) return SWIG_ERROR;

  // Check if this is a SWIG wrapper
  Napi::FunctionReference *ctor = env.GetInstanceData<EnvInstanceData>()->SWIG_NAPI_ObjectWrapCtor;
  bool instanceOf;
  NAPI_CHECK_RESULT(objRef.InstanceOf(ctor->Value()), instanceOf);
  if (!instanceOf) {
    return SWIG_TypeError;
  }

  ow = Napi::ObjectWrap<SWIG_NAPI_ObjectWrap_inst>::Unwrap(objRef);

  // Now check if the SWIG type is compatible unless the types match exactly or the type is unknown
  if(info && ow->info != info && ow->info != nullptr) {
    swig_cast_info *tc = SWIG_TypeCheckStruct(ow->info, info);
    if (!tc && ow->info->name) {
      tc = SWIG_TypeCheck(ow->info->name, info);
    }
    bool type_valid = tc != 0;
    if(!type_valid) {
      return SWIG_TypeError;
    }
    int newmemory = 0;
    *ptr = SWIG_TypeCast(tc, ow->self, &newmemory);
    assert(!newmemory); /* newmemory handling not yet implemented */
  } else {
    *ptr = ow->self;
  }

  if (((flags & SWIG_POINTER_RELEASE) == SWIG_POINTER_RELEASE) && !ow->owned) {
    return SWIG_ERROR_RELEASE_NOT_OWNED;
  } else {
    if (flags & SWIG_POINTER_DISOWN) {
      ow->owned = false;
    }
    if (flags & SWIG_POINTER_CLEAR) {
      ow->self = nullptr;
    }
  }
  return SWIG_OK;
  goto fail;
fail:
  return SWIG_ERROR;
}


SWIGRUNTIME int SWIG_NAPI_GetInstancePtr(Napi::Value valRef, void **ptr) {
  SWIG_NAPI_ObjectWrap_inst *ow;
  if(!valRef.IsObject()) {
    return SWIG_TypeError;
  }
  Napi::Object objRef;
  NAPI_CHECK_RESULT(valRef.ToObject(), objRef);
  ow = Napi::ObjectWrap<SWIG_NAPI_ObjectWrap_inst>::Unwrap(objRef);

  if(ow->self == nullptr) {
    return SWIG_ERROR;
  }

  *ptr = ow->self;
  return SWIG_OK;
  goto fail;
fail:
  return SWIG_ERROR;
}


SWIGRUNTIME int SWIG_NAPI_ConvertPtr(Napi::Value valRef, void **ptr, swig_type_info *info, int flags) {
  // special case: JavaScript null => C NULL pointer
  if (valRef.IsNull()) {
    *ptr=0;
    return (flags & SWIG_POINTER_NO_NULL) ? SWIG_NullReferenceError : SWIG_OK;
  }

  if (!valRef.IsObject()) {
    return SWIG_TypeError;
  }

  Napi::Object objRef;
  NAPI_CHECK_RESULT(valRef.ToObject(), objRef);
  return SWIG_NAPI_ConvertInstancePtr(objRef, ptr, info, flags);
  goto fail;
fail:
  return SWIG_ERROR;
}

SWIGRUNTIME Napi::Value SWIG_NAPI_NewPointerObj(Napi::Env env, void *ptr, swig_type_info *info, int flags) {
  Napi::External<void> native;
  Napi::FunctionReference *ctor;

  if (ptr == nullptr) {
    return env.Null();
  }
  native = Napi::External<void>::New(env, ptr);

  size_t *idx = info != nullptr ?
        reinterpret_cast<SWIG_NAPI_ClientData *>(info->clientdata) :
        nullptr;
  if (idx == nullptr) {
    // This type does not have a dedicated wrapper
    ctor = env.GetInstanceData<EnvInstanceData>()->SWIG_NAPI_ObjectWrapCtor;
  } else {
    ctor = env.GetInstanceData<EnvInstanceData>()->ctor[*idx];
  }

  Napi::Value wrapped;
  NAPI_CHECK_RESULT(ctor->New({native}), wrapped);

  // Preserve the type even if using the generic wrapper
  if (idx == nullptr && info != nullptr) {
    Napi::Object obj;
    NAPI_CHECK_RESULT(wrapped.ToObject(), obj);
    Napi::ObjectWrap<SWIG_NAPI_ObjectWrap_inst>::Unwrap(obj)->info = info;
  }

  if ((flags & SWIG_POINTER_OWN) == SWIG_POINTER_OWN) {
    Napi::Object obj;
    NAPI_CHECK_RESULT(wrapped.ToObject(), obj);
    Napi::ObjectWrap<SWIG_NAPI_ObjectWrap_inst>::Unwrap(obj)->owned = true;
  }

  return wrapped;
  goto fail;
fail:
  return Napi::Value();
}

#define SWIG_ConvertPtr(obj, ptr, info, flags)          SWIG_NAPI_ConvertPtr(obj, ptr, info, flags)
#define SWIG_NewPointerObj(ptr, info, flags)            SWIG_NAPI_NewPointerObj(env, ptr, info, flags)

#define SWIG_ConvertInstance(obj, pptr, type, flags)    SWIG_NAPI_ConvertInstancePtr(obj, pptr, type, flags)
#define SWIG_NewInstanceObj(thisvalue, type, flags)     SWIG_NAPI_NewPointerObj(env, thisvalue, type, flags)

#define SWIG_ConvertFunctionPtr(obj, pptr, type)        SWIG_NAPI_ConvertPtr(obj, pptr, type, 0)
#define SWIG_NewFunctionPtrObj(ptr, type)               SWIG_NAPI_NewPointerObj(env, ptr, type, 0)

#define SWIG_GetInstancePtr(obj, ptr)                   SWIG_NAPI_GetInstancePtr(obj, ptr)

SWIGRUNTIME Napi::Value _SWIG_NAPI_wrap_equals(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  void *arg1 = (void *) 0 ;
  void *arg2 = (void *) 0 ;
  bool result;
  int res1;
  int res2;

  if(info.Length() != 1) SWIG_Error(SWIG_ERROR, "Illegal number of arguments for equals.");

  res1 = SWIG_GetInstancePtr(info.This(), &arg1);
  if (!SWIG_IsOK(res1)) {
    SWIG_Error(SWIG_ERROR, "Could not get pointer from 'this' object for equals.");
  }
  res2 = SWIG_GetInstancePtr(info[0], &arg2);
  if (!SWIG_IsOK(res2)) {
    SWIG_Error(SWIG_ArgError(res2), " in method '" "equals" "', argument " "1"" of type '" "void *""'");
  }

  result = (bool)(arg1 == arg2);
  jsresult = Napi::Boolean::New(env, result);

  return jsresult;
  goto fail;
fail:
  return Napi::Value();
}

SWIGRUNTIME Napi::Value _wrap_getCPtr(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  void *arg1 = (void *) 0 ;
  long result;
  int res1;

  res1 = SWIG_GetInstancePtr(info.This(), &arg1);
  if (!SWIG_IsOK(res1)) {
    SWIG_Error(SWIG_ArgError(res1), " in method '" "getCPtr" "', argument " "1"" of type '" "void *""'");
  }

  result = (long)arg1;
  jsresult = Napi::Number::New(env, result);

  return jsresult;
  goto fail;
fail:
  return Napi::Value();
}


/* ---------------------------------------------------------------------------
 * PackedData object
 * (objects visible to JS that do not have a dedicated wrapper but must preserve type)
 * ---------------------------------------------------------------------------*/

SWIGRUNTIME
Napi::Value SWIG_NAPI_NewPackedObj(Napi::Env env, void *data, size_t size, swig_type_info *type) {
  void *data_copy = new uint8_t[size];
  memcpy(data_copy, data, size);
  Napi::Value val = SWIG_NAPI_NewPointerObj(env, data_copy, type, SWIG_POINTER_OWN);
  Napi::Object obj;
  if (val.IsEmpty()) goto fail;

  NAPI_CHECK_RESULT(val.ToObject(), obj);
  Napi::ObjectWrap<SWIG_NAPI_ObjectWrap_inst>::Unwrap(obj)->size = size;

fail:
  return val;
}

SWIGRUNTIME
int SWIG_NAPI_ConvertPacked(Napi::Value valRef, void *ptr, size_t size, swig_type_info *type) {
  void *tmp;
  if (!SWIG_IsOK(SWIG_NAPI_ConvertPtr(valRef, &tmp, type, 0))) {
    return SWIG_ERROR;
  }
  memcpy(ptr, tmp, size);
  return SWIG_OK;
}

#define SWIG_ConvertMember(obj, ptr, sz, ty)            SWIG_NAPI_ConvertPacked(obj, ptr, sz, ty)
#define SWIG_NewMemberObj(ptr, sz, type)                SWIG_NAPI_NewPackedObj(env, ptr, sz, type)


/* ---------------------------------------------------------------------------
 * Support for IN/OUTPUT typemaps (see Lib/typemaps/inoutlist.swg)
 *
 * ---------------------------------------------------------------------------*/

SWIGRUNTIME

Napi::Value SWIG_NAPI_AppendOutput(Napi::Env env, Napi::Value result, Napi::Value obj) {
  if (result.IsUndefined()) {
    result = Napi::Array::New(env);
  } else if (!result.IsArray()) {
    Napi::Array tmparr = Napi::Array::New(env);
    tmparr.Set(static_cast<uint32_t>(0), result);
    result = tmparr;
  }

  Napi::Array arr = result.As<Napi::Array>();
  arr.Set(arr.Length(), obj);
  return arr;
}


/* -------- TYPES TABLE (BEGIN) -------- */

#define SWIGTYPE_p_INT8_ARRAY swig_types[0]
#define SWIGTYPE_p_SeedKey swig_types[1]
#define SWIGTYPE_p_UINT32_PTR swig_types[2]
#define SWIGTYPE_p_UINT8_ARRAY swig_types[3]
#define SWIGTYPE_p_char swig_types[4]
#define SWIGTYPE_p_unsigned_char swig_types[5]
#define SWIGTYPE_p_unsigned_int swig_types[6]
static swig_type_info *swig_types[8];
static swig_module_info swig_module = {swig_types, 7, 0, 0, 0, 0};
#define SWIG_TypeQuery(name) SWIG_TypeQueryModule(&swig_module, &swig_module, name)
#define SWIG_MangledTypeQuery(name) SWIG_MangledTypeQueryModule(&swig_module, &swig_module, name)

/* -------- TYPES TABLE (END) -------- */



#ifdef __cplusplus
#include <utility>
/* SwigValueWrapper is described in swig.swg */
template<typename T> class SwigValueWrapper {
  struct SwigSmartPointer {
    T *ptr;
    SwigSmartPointer(T *p) : ptr(p) { }
    ~SwigSmartPointer() { delete ptr; }
    SwigSmartPointer& operator=(SwigSmartPointer& rhs) { T* oldptr = ptr; ptr = 0; delete oldptr; ptr = rhs.ptr; rhs.ptr = 0; return *this; }
    void reset(T *p) { T* oldptr = ptr; ptr = 0; delete oldptr; ptr = p; }
  } pointer;
  SwigValueWrapper& operator=(const SwigValueWrapper<T>& rhs);
  SwigValueWrapper(const SwigValueWrapper<T>& rhs);
public:
  SwigValueWrapper() : pointer(0) { }
  SwigValueWrapper& operator=(const T& t) { SwigSmartPointer tmp(new T(t)); pointer = tmp; return *this; }
#if __cplusplus >=201103L
  SwigValueWrapper& operator=(T&& t) { SwigSmartPointer tmp(new T(std::move(t))); pointer = tmp; return *this; }
  operator T&&() const { return std::move(*pointer.ptr); }
#else
  operator T&() const { return *pointer.ptr; }
#endif
  T *operator&() const { return pointer.ptr; }
  static void reset(SwigValueWrapper& t, T *p) { t.pointer.reset(p); }
};

/*
 * SwigValueInit() is a generic initialisation solution as the following approach:
 * 
 *       T c_result = T();
 * 
 * doesn't compile for all types for example:
 * 
 *       unsigned int c_result = unsigned int();
 */
template <typename T> T SwigValueInit() {
  return T();
}

#if __cplusplus >=201103L
# define SWIG_STD_MOVE(OBJ) std::move(OBJ)
#else
# define SWIG_STD_MOVE(OBJ) OBJ
#endif

#endif


#define SWIG_as_voidptr(a) const_cast< void * >(static_cast< const void * >(a)) 
#define SWIG_as_voidptrptr(a) ((void)SWIG_as_voidptr(*a),reinterpret_cast< void** >(a)) 


#include <stdexcept>


#include <assert.h>


#include <windows.h>
#include "sa.h"


typedef unsigned int UINT32_PTR;

SWIGINTERN UINT32_PTR *new_UINT32_PTR(){
  return new unsigned int();
}

SWIGINTERN
int SWIG_AsVal_unsigned_SS_int (Napi::Value valRef, unsigned int* val)
{
  if (!valRef.IsNumber()) {
    return SWIG_TypeError;
  }
  if (val) {
    Napi::Number num;
    NAPI_CHECK_RESULT(valRef.ToNumber(), num);
    if (num.Int64Value() < 0) {
      return SWIG_TypeError;
    }
    *val = static_cast<unsigned int>(num.Uint32Value());
  }

  return SWIG_OK;
  goto fail;
fail:
  return SWIG_ERROR;
}

SWIGINTERN void UINT32_PTR_assign(UINT32_PTR *self,unsigned int value){
  *self = value;
}
SWIGINTERN unsigned int UINT32_PTR_value(UINT32_PTR *self){
  return *self;
}

SWIGINTERN
Napi::Value SWIG_From_unsigned_SS_int(Napi::Env env, unsigned int val)
{
  return Napi::Number::New(env, val);
}

SWIGINTERN unsigned int *UINT32_PTR_cast(UINT32_PTR *self){
  return self;
}
SWIGINTERN UINT32_PTR *UINT32_PTR_frompointer(unsigned int *t){
  return (UINT32_PTR *) t;
}

typedef unsigned char UINT8_ARRAY;


SWIGINTERN
int SWIG_AsVal_double (Napi::Value obj, double *val)
{
  if(!obj.IsNumber()) {
    return SWIG_TypeError;
  }

  if(val) {
    Napi::Number num;
    NAPI_CHECK_RESULT(obj.ToNumber(), num);
    *val = static_cast<double>(num.DoubleValue());
  }

  return SWIG_OK;
  goto fail;
fail:
  return SWIG_ERROR;
}


#include <float.h>


#include <math.h>


SWIGINTERNINLINE int
SWIG_CanCastAsInteger(double *d, double min, double max) {
  double x = *d;
  if ((min <= x && x <= max)) {
   double fx, cx, rd;
   errno = 0;
   fx = floor(x);
   cx = ceil(x);
   rd =  ((x - fx) < 0.5) ? fx : cx; /* simple rint */
   if ((errno == EDOM) || (errno == ERANGE)) {
     errno = 0;
   } else {
     double summ, reps, diff;
     if (rd < x) {
       diff = x - rd;
     } else if (rd > x) {
       diff = rd - x;
     } else {
       return 1;
     }
     summ = rd + x;
     reps = diff/summ;
     if (reps < 8*DBL_EPSILON) {
       *d = rd;
       return 1;
     }
   }
  }
  return 0;
}


SWIGINTERN
int SWIG_AsVal_unsigned_SS_long (Napi::Value obj, unsigned long *val)
{
  if(!obj.IsNumber()) {
    return SWIG_TypeError;
  }
  if (val) {
    Napi::Number num;
    NAPI_CHECK_RESULT(obj.ToNumber(), num);
    if (num.Int64Value() < 0) {
      return SWIG_TypeError;
    }
    *val = static_cast<unsigned long>(num.Int64Value());
  }
  return SWIG_OK;
  goto fail;
fail:
  return SWIG_ERROR;
}


#include <limits.h>
#if !defined(SWIG_NO_LLONG_MAX)
# if !defined(LLONG_MAX) && defined(__GNUC__) && defined (__LONG_LONG_MAX__)
#   define LLONG_MAX __LONG_LONG_MAX__
#   define LLONG_MIN (-LLONG_MAX - 1LL)
#   define ULLONG_MAX (LLONG_MAX * 2ULL + 1ULL)
# endif
#endif


#if defined(LLONG_MAX) && !defined(SWIG_LONG_LONG_AVAILABLE)
#  define SWIG_LONG_LONG_AVAILABLE
#endif


#ifdef SWIG_LONG_LONG_AVAILABLE
SWIGINTERN
int SWIG_AsVal_unsigned_SS_long_SS_long (Napi::Value obj, unsigned long long *val)
{
  if(!obj.IsNumber()) {
    return SWIG_TypeError;
  }
  if (obj.ToNumber().Int64Value() < 0) {
    return SWIG_TypeError;
  }
  if (val) {
    Napi::Number num;
    NAPI_CHECK_RESULT(obj.ToNumber(), num);
    *val = static_cast<unsigned long long>(num.Int64Value());
  }
  return SWIG_OK;
  goto fail;
fail:
  return SWIG_ERROR;
}
#endif


SWIGINTERNINLINE int
SWIG_AsVal_size_t (Napi::Value obj, size_t *val)
{
  int res = SWIG_TypeError;
#ifdef SWIG_LONG_LONG_AVAILABLE
  if (sizeof(size_t) <= sizeof(unsigned long)) {
#endif
    unsigned long v;
    res = SWIG_AsVal_unsigned_SS_long (obj, val ? &v : 0);
    if (SWIG_IsOK(res) && val) *val = static_cast< size_t >(v);
#ifdef SWIG_LONG_LONG_AVAILABLE
  } else if (sizeof(size_t) <= sizeof(unsigned long long)) {
    unsigned long long v;
    res = SWIG_AsVal_unsigned_SS_long_SS_long (obj, val ? &v : 0);
    if (SWIG_IsOK(res) && val) *val = static_cast< size_t >(v);
  }
#endif
  return res;
}

SWIGINTERN UINT8_ARRAY *new_UINT8_ARRAY(size_t nelements){
  return new unsigned char[nelements]();
}
SWIGINTERN unsigned char UINT8_ARRAY_getitem(UINT8_ARRAY *self,size_t index){
  return self[index];
}

SWIGINTERNINLINE Napi::Value
SWIG_From_unsigned_SS_char(Napi::Env env, unsigned char c)
{
  return Napi::Number::New(env, static_cast<double>(c));
}


SWIGINTERN int
SWIG_AsVal_unsigned_SS_char (Napi::Value obj, unsigned char *val)
{
  unsigned long v;
  int res = SWIG_AsVal_unsigned_SS_long (obj, &v);
  if (SWIG_IsOK(res)) {
    if ((v > UCHAR_MAX)) {
      return SWIG_OverflowError;
    } else {
      if (val) *val = static_cast< unsigned char >(v);
    }
  }  
  return res;
}

SWIGINTERN void UINT8_ARRAY_setitem(UINT8_ARRAY *self,size_t index,unsigned char value){
  self[index] = value;
}
SWIGINTERN unsigned char *UINT8_ARRAY_cast(UINT8_ARRAY *self){
  return self;
}
SWIGINTERN UINT8_ARRAY *UINT8_ARRAY_frompointer(unsigned char *t){
  return (UINT8_ARRAY *) t;
}

typedef char INT8_ARRAY;

SWIGINTERN INT8_ARRAY *new_INT8_ARRAY(size_t nelements){
  return new char[nelements]();
}
SWIGINTERN char INT8_ARRAY_getitem(INT8_ARRAY *self,size_t index){
  return self[index];
}

SWIGINTERNINLINE Napi::Value
SWIG_From_char(Napi::Env env, char c)
{
  Napi::String js_str = Napi::String::New(env, &c, 1);
  return js_str;
}


SWIGINTERN swig_type_info*
SWIG_pchar_descriptor(void)
{
  static int init = 0;
  static swig_type_info* info = 0;
  if (!init) {
    info = SWIG_TypeQuery("_p_char");
    init = 1;
  }
  return info;
}


SWIGINTERN int
SWIG_AsCharPtrAndSize(Napi::Value valRef, char** cptr, size_t* psize, int *alloc)
{
  if(valRef.IsString()) {
    Napi::String js_str;
    NAPI_CHECK_RESULT(valRef.ToString(), js_str);

    std::string str = js_str.Utf8Value();
    size_t len = str.size() + 1;
    char* cstr = (char*) (new char[len]());
    memcpy(cstr, str.data(), len);
    
    if(alloc) *alloc = SWIG_NEWOBJ;
    if(psize) *psize = len;
    if(cptr) *cptr = cstr;
    
    return SWIG_OK;
  } else {
    if(valRef.IsObject()) {
      swig_type_info* pchar_descriptor = SWIG_pchar_descriptor();
      Napi::Object obj;
      NAPI_CHECK_RESULT(valRef.ToObject(), obj);
      // try if the object is a wrapped char[]
      if (pchar_descriptor) {
        void* vptr = 0;
        if (SWIG_ConvertPtr(obj, &vptr, pchar_descriptor, 0) == SWIG_OK) {
          if (cptr) *cptr = (char *) vptr;
          if (psize) *psize = vptr ? (strlen((char *)vptr) + 1) : 0;
          if (alloc) *alloc = SWIG_OLDOBJ;
          return SWIG_OK;
        }
      }
    }
  }
  goto fail;
fail:
  return SWIG_TypeError;
}


SWIGINTERN int
SWIG_AsCharArray(Napi::Value obj, char *val, size_t size)
{ 
  char* cptr = 0; size_t csize = 0; int alloc = SWIG_OLDOBJ;
  int res = SWIG_AsCharPtrAndSize(obj, &cptr, &csize, &alloc);
  if (SWIG_IsOK(res)) {
    /* special case of single char conversion when we don't need space for NUL */
    if (size == 1 && csize == 2 && cptr && !cptr[1]) --csize;
    if (csize <= size) {
      if (val) {
	if (csize) memcpy(val, cptr, csize*sizeof(char));
	if (csize < size) memset(val + csize, 0, (size - csize)*sizeof(char));
      }
      if (alloc == SWIG_NEWOBJ) {
	delete[] cptr;
	res = SWIG_DelNewMask(res);
      }      
      return res;
    }
    if (alloc == SWIG_NEWOBJ) delete[] cptr;
  }
  return SWIG_TypeError;
}


SWIGINTERN
int SWIG_AsVal_long (Napi::Value obj, long* val)
{
  if (!obj.IsNumber()) {
    return SWIG_TypeError;
  }
  if (val) {
    Napi::Number num;
    NAPI_CHECK_RESULT(obj.ToNumber(), num);
    *val = static_cast<long>(num.Int64Value());
  }

  return SWIG_OK;
  goto fail;
fail:
  return SWIG_ERROR;
}


SWIGINTERN int
SWIG_AsVal_char (Napi::Value obj, char *val)
{    
  int res = SWIG_AsCharArray(obj, val, 1);
  if (!SWIG_IsOK(res)) {
    long v;
    res = SWIG_AddCast(SWIG_AsVal_long (obj, &v));
    if (SWIG_IsOK(res)) {
      if ((CHAR_MIN <= v) && (v <= CHAR_MAX)) {
	if (val) *val = static_cast< char >(v);
      } else {
	res = SWIG_OverflowError;
      }
    }
  }
  return res;
}

SWIGINTERN void INT8_ARRAY_setitem(INT8_ARRAY *self,size_t index,char value){
  self[index] = value;
}
SWIGINTERN char *INT8_ARRAY_cast(INT8_ARRAY *self){
  return self;
}

SWIGINTERNINLINE Napi::Value
SWIG_Env_FromCharPtrAndSize(Napi::Env env, const char* carray, size_t size)
{
  if (carray) {
    Napi::String js_str = Napi::String::New(env, carray, size);
    return js_str;
  } else {
    return env.Undefined();
  }
}


// Override the default one with an empty one




SWIGINTERN INT8_ARRAY *INT8_ARRAY_frompointer(char *t){
  return (INT8_ARRAY *) t;
}

SWIGINTERN
Napi::Value SWIG_From_int(Napi::Env env, int val)
{
  return Napi::Number::New(env, val);
}


SWIGINTERN
Napi::Value SWIG_From_bool(Napi::Env env, bool val)
{
  return Napi::Boolean::New(env, val);
}


#define SWIG_NAPI_INIT xmlpp_initialize


// jsnapi_class_prologue_template
template <typename SWIG_OBJ_WRAP>
class _exports_UINT32_PTR_templ : public SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP> {
public:
  _exports_UINT32_PTR_templ(const Napi::CallbackInfo &);
_exports_UINT32_PTR_templ(bool, const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_new_UINT32_PTR(const Napi::CallbackInfo &);
virtual ~_exports_UINT32_PTR_templ();
// jsnapi_class_method_declaration
Napi::Value _wrap_UINT32_PTR_assign(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_UINT32_PTR_value(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_UINT32_PTR_cast(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
static Napi::Value _wrap_UINT32_PTR_frompointer(const Napi::CallbackInfo &);
// jsnapi_class_epilogue_template
static void JS_veto_set_static_variable(const Napi::CallbackInfo &, const Napi::Value &);
void JS_veto_set_variable(const Napi::CallbackInfo &, const Napi::Value &);
};

template <typename SWIG_OBJ_WRAP>
void _exports_UINT32_PTR_templ<SWIG_OBJ_WRAP>::JS_veto_set_static_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}

template <typename SWIG_OBJ_WRAP>
void _exports_UINT32_PTR_templ<SWIG_OBJ_WRAP>::JS_veto_set_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}
// jsnapi_class_instance
class _exports_UINT32_PTR_inst : public _exports_UINT32_PTR_templ<_exports_UINT32_PTR_inst> {
public:
  using _exports_UINT32_PTR_templ::_exports_UINT32_PTR_templ;
  virtual ~_exports_UINT32_PTR_inst() {
    
  };
  static void GetMembers(
    Napi::Env,
    std::map<std::string, _exports_UINT32_PTR_templ::PropertyDescriptor> &,
    std::map<std::string, _exports_UINT32_PTR_templ::PropertyDescriptor> &
    );
  static Napi::Function GetClass(Napi::Env);
};
/* Class: UINT32_PTR (_exports_UINT32_PTR) */
// jsnapi_getclass
Napi::Function _exports_UINT32_PTR_inst::GetClass(Napi::Env env) {
  std::map<std::string, _exports_UINT32_PTR_templ::PropertyDescriptor> members, staticMembers;
  GetMembers(env, members, staticMembers);
  
  std::vector<_exports_UINT32_PTR_inst::PropertyDescriptor> symbolTable;
  for (auto it = members.begin(); it != members.end(); it++)
  symbolTable.push_back(it->second);
  for (auto it = staticMembers.begin(); it != staticMembers.end(); it++)
  symbolTable.push_back(it->second);
  
  return Napi::ObjectWrap<_exports_UINT32_PTR_inst>::DefineClass(env, "UINT32_PTR", symbolTable);
}

void _exports_UINT32_PTR_inst::GetMembers(
  Napi::Env env,
  std::map<std::string, _exports_UINT32_PTR_templ::PropertyDescriptor> &members,
  std::map<std::string, _exports_UINT32_PTR_templ::PropertyDescriptor> &staticMembers
  ) {
  std::map<std::string, SWIG_NAPI_ObjectWrap_templ<SWIG_NAPI_ObjectWrap_inst>::PropertyDescriptor> baseMembers, baseStaticMembers;
  SWIG_NAPI_ObjectWrap_inst::GetMembers(env, baseMembers, baseStaticMembers);
  members.insert(baseMembers.begin(), baseMembers.end());
  staticMembers.insert(staticMembers.begin(), staticMembers.end());
  
  /* register wrapper functions */
  // jsnapi_member_function_descriptor
  members.erase("assign");
  members.insert({
    "assign",
      _exports_UINT32_PTR_templ::InstanceMethod("assign",
        &_exports_UINT32_PTR_templ::_wrap_UINT32_PTR_assign,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  // jsnapi_member_function_descriptor
  members.erase("value");
  members.insert({
    "value",
      _exports_UINT32_PTR_templ::InstanceMethod("value",
        &_exports_UINT32_PTR_templ::_wrap_UINT32_PTR_value,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  // jsnapi_member_function_descriptor
  members.erase("cast");
  members.insert({
    "cast",
      _exports_UINT32_PTR_templ::InstanceMethod("cast",
        &_exports_UINT32_PTR_templ::_wrap_UINT32_PTR_cast,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  
  /* add static class functions and variables */
  // jsnapi_register_static_function
  staticMembers.erase("frompointer");
  staticMembers.insert({
    "frompointer",
      StaticMethod("frompointer",
        &_exports_UINT32_PTR_templ::_wrap_UINT32_PTR_frompointer,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  
}
// jsnapi_class_prologue_template
template <typename SWIG_OBJ_WRAP>
class _exports_UINT8_ARRAY_templ : public SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP> {
public:
  _exports_UINT8_ARRAY_templ(const Napi::CallbackInfo &);
_exports_UINT8_ARRAY_templ(bool, const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_new_UINT8_ARRAY(const Napi::CallbackInfo &);
virtual ~_exports_UINT8_ARRAY_templ();
// jsnapi_class_method_declaration
Napi::Value _wrap_UINT8_ARRAY_getitem(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_UINT8_ARRAY_setitem(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_UINT8_ARRAY_cast(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
static Napi::Value _wrap_UINT8_ARRAY_frompointer(const Napi::CallbackInfo &);
// jsnapi_class_epilogue_template
static void JS_veto_set_static_variable(const Napi::CallbackInfo &, const Napi::Value &);
void JS_veto_set_variable(const Napi::CallbackInfo &, const Napi::Value &);
};

template <typename SWIG_OBJ_WRAP>
void _exports_UINT8_ARRAY_templ<SWIG_OBJ_WRAP>::JS_veto_set_static_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}

template <typename SWIG_OBJ_WRAP>
void _exports_UINT8_ARRAY_templ<SWIG_OBJ_WRAP>::JS_veto_set_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}
// jsnapi_class_instance
class _exports_UINT8_ARRAY_inst : public _exports_UINT8_ARRAY_templ<_exports_UINT8_ARRAY_inst> {
public:
  using _exports_UINT8_ARRAY_templ::_exports_UINT8_ARRAY_templ;
  virtual ~_exports_UINT8_ARRAY_inst() {
    
  };
  static void GetMembers(
    Napi::Env,
    std::map<std::string, _exports_UINT8_ARRAY_templ::PropertyDescriptor> &,
    std::map<std::string, _exports_UINT8_ARRAY_templ::PropertyDescriptor> &
    );
  static Napi::Function GetClass(Napi::Env);
};
/* Class: UINT8_ARRAY (_exports_UINT8_ARRAY) */
// jsnapi_getclass
Napi::Function _exports_UINT8_ARRAY_inst::GetClass(Napi::Env env) {
  std::map<std::string, _exports_UINT8_ARRAY_templ::PropertyDescriptor> members, staticMembers;
  GetMembers(env, members, staticMembers);
  
  std::vector<_exports_UINT8_ARRAY_inst::PropertyDescriptor> symbolTable;
  for (auto it = members.begin(); it != members.end(); it++)
  symbolTable.push_back(it->second);
  for (auto it = staticMembers.begin(); it != staticMembers.end(); it++)
  symbolTable.push_back(it->second);
  
  return Napi::ObjectWrap<_exports_UINT8_ARRAY_inst>::DefineClass(env, "UINT8_ARRAY", symbolTable);
}

void _exports_UINT8_ARRAY_inst::GetMembers(
  Napi::Env env,
  std::map<std::string, _exports_UINT8_ARRAY_templ::PropertyDescriptor> &members,
  std::map<std::string, _exports_UINT8_ARRAY_templ::PropertyDescriptor> &staticMembers
  ) {
  std::map<std::string, SWIG_NAPI_ObjectWrap_templ<SWIG_NAPI_ObjectWrap_inst>::PropertyDescriptor> baseMembers, baseStaticMembers;
  SWIG_NAPI_ObjectWrap_inst::GetMembers(env, baseMembers, baseStaticMembers);
  members.insert(baseMembers.begin(), baseMembers.end());
  staticMembers.insert(staticMembers.begin(), staticMembers.end());
  
  /* register wrapper functions */
  // jsnapi_member_function_descriptor
  members.erase("getitem");
  members.insert({
    "getitem",
      _exports_UINT8_ARRAY_templ::InstanceMethod("getitem",
        &_exports_UINT8_ARRAY_templ::_wrap_UINT8_ARRAY_getitem,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  // jsnapi_member_function_descriptor
  members.erase("setitem");
  members.insert({
    "setitem",
      _exports_UINT8_ARRAY_templ::InstanceMethod("setitem",
        &_exports_UINT8_ARRAY_templ::_wrap_UINT8_ARRAY_setitem,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  // jsnapi_member_function_descriptor
  members.erase("cast");
  members.insert({
    "cast",
      _exports_UINT8_ARRAY_templ::InstanceMethod("cast",
        &_exports_UINT8_ARRAY_templ::_wrap_UINT8_ARRAY_cast,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  
  /* add static class functions and variables */
  // jsnapi_register_static_function
  staticMembers.erase("frompointer");
  staticMembers.insert({
    "frompointer",
      StaticMethod("frompointer",
        &_exports_UINT8_ARRAY_templ::_wrap_UINT8_ARRAY_frompointer,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  
}
// jsnapi_class_prologue_template
template <typename SWIG_OBJ_WRAP>
class _exports_INT8_ARRAY_templ : public SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP> {
public:
  _exports_INT8_ARRAY_templ(const Napi::CallbackInfo &);
_exports_INT8_ARRAY_templ(bool, const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_new_INT8_ARRAY(const Napi::CallbackInfo &);
virtual ~_exports_INT8_ARRAY_templ();
// jsnapi_class_method_declaration
Napi::Value _wrap_INT8_ARRAY_getitem(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_INT8_ARRAY_setitem(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_INT8_ARRAY_cast(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
static Napi::Value _wrap_INT8_ARRAY_frompointer(const Napi::CallbackInfo &);
// jsnapi_class_epilogue_template
static void JS_veto_set_static_variable(const Napi::CallbackInfo &, const Napi::Value &);
void JS_veto_set_variable(const Napi::CallbackInfo &, const Napi::Value &);
};

template <typename SWIG_OBJ_WRAP>
void _exports_INT8_ARRAY_templ<SWIG_OBJ_WRAP>::JS_veto_set_static_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}

template <typename SWIG_OBJ_WRAP>
void _exports_INT8_ARRAY_templ<SWIG_OBJ_WRAP>::JS_veto_set_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}
// jsnapi_class_instance
class _exports_INT8_ARRAY_inst : public _exports_INT8_ARRAY_templ<_exports_INT8_ARRAY_inst> {
public:
  using _exports_INT8_ARRAY_templ::_exports_INT8_ARRAY_templ;
  virtual ~_exports_INT8_ARRAY_inst() {
    
  };
  static void GetMembers(
    Napi::Env,
    std::map<std::string, _exports_INT8_ARRAY_templ::PropertyDescriptor> &,
    std::map<std::string, _exports_INT8_ARRAY_templ::PropertyDescriptor> &
    );
  static Napi::Function GetClass(Napi::Env);
};
/* Class: INT8_ARRAY (_exports_INT8_ARRAY) */
// jsnapi_getclass
Napi::Function _exports_INT8_ARRAY_inst::GetClass(Napi::Env env) {
  std::map<std::string, _exports_INT8_ARRAY_templ::PropertyDescriptor> members, staticMembers;
  GetMembers(env, members, staticMembers);
  
  std::vector<_exports_INT8_ARRAY_inst::PropertyDescriptor> symbolTable;
  for (auto it = members.begin(); it != members.end(); it++)
  symbolTable.push_back(it->second);
  for (auto it = staticMembers.begin(); it != staticMembers.end(); it++)
  symbolTable.push_back(it->second);
  
  return Napi::ObjectWrap<_exports_INT8_ARRAY_inst>::DefineClass(env, "INT8_ARRAY", symbolTable);
}

void _exports_INT8_ARRAY_inst::GetMembers(
  Napi::Env env,
  std::map<std::string, _exports_INT8_ARRAY_templ::PropertyDescriptor> &members,
  std::map<std::string, _exports_INT8_ARRAY_templ::PropertyDescriptor> &staticMembers
  ) {
  std::map<std::string, SWIG_NAPI_ObjectWrap_templ<SWIG_NAPI_ObjectWrap_inst>::PropertyDescriptor> baseMembers, baseStaticMembers;
  SWIG_NAPI_ObjectWrap_inst::GetMembers(env, baseMembers, baseStaticMembers);
  members.insert(baseMembers.begin(), baseMembers.end());
  staticMembers.insert(staticMembers.begin(), staticMembers.end());
  
  /* register wrapper functions */
  // jsnapi_member_function_descriptor
  members.erase("getitem");
  members.insert({
    "getitem",
      _exports_INT8_ARRAY_templ::InstanceMethod("getitem",
        &_exports_INT8_ARRAY_templ::_wrap_INT8_ARRAY_getitem,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  // jsnapi_member_function_descriptor
  members.erase("setitem");
  members.insert({
    "setitem",
      _exports_INT8_ARRAY_templ::InstanceMethod("setitem",
        &_exports_INT8_ARRAY_templ::_wrap_INT8_ARRAY_setitem,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  // jsnapi_member_function_descriptor
  members.erase("cast");
  members.insert({
    "cast",
      _exports_INT8_ARRAY_templ::InstanceMethod("cast",
        &_exports_INT8_ARRAY_templ::_wrap_INT8_ARRAY_cast,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  
  /* add static class functions and variables */
  // jsnapi_register_static_function
  staticMembers.erase("frompointer");
  staticMembers.insert({
    "frompointer",
      StaticMethod("frompointer",
        &_exports_INT8_ARRAY_templ::_wrap_INT8_ARRAY_frompointer,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  
}
// jsnapi_class_prologue_template
template <typename SWIG_OBJ_WRAP>
class _exports_SeedKey_templ : public SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP> {
public:
  _exports_SeedKey_templ(const Napi::CallbackInfo &);
_exports_SeedKey_templ(bool, const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_new_SeedKey(const Napi::CallbackInfo &);
virtual ~_exports_SeedKey_templ();
// jsnapi_class_method_declaration
Napi::Value _wrap_SeedKey_LoadDLL(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_SeedKey_Unload(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_SeedKey_GenerateKeyExOpt(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_SeedKey_GenerateKeyEx(const Napi::CallbackInfo &);
// jsnapi_class_method_declaration
Napi::Value _wrap_SeedKey_IsLoaded(const Napi::CallbackInfo &);
// jsnapi_class_epilogue_template
static void JS_veto_set_static_variable(const Napi::CallbackInfo &, const Napi::Value &);
void JS_veto_set_variable(const Napi::CallbackInfo &, const Napi::Value &);
};

template <typename SWIG_OBJ_WRAP>
void _exports_SeedKey_templ<SWIG_OBJ_WRAP>::JS_veto_set_static_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}

template <typename SWIG_OBJ_WRAP>
void _exports_SeedKey_templ<SWIG_OBJ_WRAP>::JS_veto_set_variable(const Napi::CallbackInfo &info, const Napi::Value &value) {
SWIG_NAPI_Raise(info.Env(), "Tried to write read-only variable.");
}
// jsnapi_class_instance
class _exports_SeedKey_inst : public _exports_SeedKey_templ<_exports_SeedKey_inst> {
public:
  using _exports_SeedKey_templ::_exports_SeedKey_templ;
  virtual ~_exports_SeedKey_inst() {
    
  };
  static void GetMembers(
    Napi::Env,
    std::map<std::string, _exports_SeedKey_templ::PropertyDescriptor> &,
    std::map<std::string, _exports_SeedKey_templ::PropertyDescriptor> &
    );
  static Napi::Function GetClass(Napi::Env);
};
/* Class: SeedKey (_exports_SeedKey) */
// jsnapi_getclass
Napi::Function _exports_SeedKey_inst::GetClass(Napi::Env env) {
  std::map<std::string, _exports_SeedKey_templ::PropertyDescriptor> members, staticMembers;
  GetMembers(env, members, staticMembers);
  
  std::vector<_exports_SeedKey_inst::PropertyDescriptor> symbolTable;
  for (auto it = members.begin(); it != members.end(); it++)
  symbolTable.push_back(it->second);
  for (auto it = staticMembers.begin(); it != staticMembers.end(); it++)
  symbolTable.push_back(it->second);
  
  return Napi::ObjectWrap<_exports_SeedKey_inst>::DefineClass(env, "SeedKey", symbolTable);
}

void _exports_SeedKey_inst::GetMembers(
  Napi::Env env,
  std::map<std::string, _exports_SeedKey_templ::PropertyDescriptor> &members,
  std::map<std::string, _exports_SeedKey_templ::PropertyDescriptor> &staticMembers
  ) {
  std::map<std::string, SWIG_NAPI_ObjectWrap_templ<SWIG_NAPI_ObjectWrap_inst>::PropertyDescriptor> baseMembers, baseStaticMembers;
  SWIG_NAPI_ObjectWrap_inst::GetMembers(env, baseMembers, baseStaticMembers);
  members.insert(baseMembers.begin(), baseMembers.end());
  staticMembers.insert(staticMembers.begin(), staticMembers.end());
  
  /* register wrapper functions */
  // jsnapi_member_function_descriptor
  members.erase("LoadDLL");
  members.insert({
    "LoadDLL",
      _exports_SeedKey_templ::InstanceMethod("LoadDLL",
        &_exports_SeedKey_templ::_wrap_SeedKey_LoadDLL,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  // jsnapi_member_function_descriptor
  members.erase("Unload");
  members.insert({
    "Unload",
      _exports_SeedKey_templ::InstanceMethod("Unload",
        &_exports_SeedKey_templ::_wrap_SeedKey_Unload,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  // jsnapi_member_function_descriptor
  members.erase("GenerateKeyExOpt");
  members.insert({
    "GenerateKeyExOpt",
      _exports_SeedKey_templ::InstanceMethod("GenerateKeyExOpt",
        &_exports_SeedKey_templ::_wrap_SeedKey_GenerateKeyExOpt,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  // jsnapi_member_function_descriptor
  members.erase("GenerateKeyEx");
  members.insert({
    "GenerateKeyEx",
      _exports_SeedKey_templ::InstanceMethod("GenerateKeyEx",
        &_exports_SeedKey_templ::_wrap_SeedKey_GenerateKeyEx,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  // jsnapi_member_function_descriptor
  members.erase("IsLoaded");
  members.insert({
    "IsLoaded",
      _exports_SeedKey_templ::InstanceMethod("IsLoaded",
        &_exports_SeedKey_templ::_wrap_SeedKey_IsLoaded,
        static_cast<napi_property_attributes>(napi_writable | napi_configurable))
    });
  
  /* add static class functions and variables */
  
}





template <typename SWIG_OBJ_WRAP>
// js_ctor
// This is the main constructor
_exports_UINT32_PTR_templ<SWIG_OBJ_WRAP>::_exports_UINT32_PTR_templ(const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  Napi::Env env = info.Env();
  
  this->info = SWIGTYPE_p_UINT32_PTR;
  if (info.Length() == 1 && info[0].IsExternal()) {
    // This constructor has been called internally from C++/SWIG
    // to wrap an already existing C++ object in JS
    this->self = info[0].As<Napi::External<void>>().Data();
    this->owned = false;
    return;
  }
  this->owned = true;
  
  UINT32_PTR *result;
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_new_UINT32_PTR.");
  }
  result = (UINT32_PTR *)new_UINT32_PTR();
  
  
  this->self = result;
  return;
  goto fail;
fail:
  return;
}

// This is the bypass constructor to be used from child classes
template <typename SWIG_OBJ_WRAP>
_exports_UINT32_PTR_templ<SWIG_OBJ_WRAP>::_exports_UINT32_PTR_templ(bool, const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  
}

SWIGINTERN void delete_UINT32_PTR(UINT32_PTR *self){
  delete self;
}

// js_dtoroverride
template <typename SWIG_OBJ_WRAP>
_exports_UINT32_PTR_templ<SWIG_OBJ_WRAP>::~_exports_UINT32_PTR_templ() {
  auto arg1 = reinterpret_cast<UINT32_PTR *>(this->self);
  if (this->owned && arg1) {
    delete_UINT32_PTR(arg1);
    this->self = nullptr;
  }
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_UINT32_PTR_templ<SWIG_OBJ_WRAP>::_wrap_UINT32_PTR_assign(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  UINT32_PTR *arg1 = (UINT32_PTR *) 0 ;
  unsigned int arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned int val2 ;
  int ecode2 = 0 ;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_UINT32_PTR_assign.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_UINT32_PTR, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "UINT32_PTR_assign" "', argument " "1"" of type '" "UINT32_PTR *""'"); 
  }
  arg1 = reinterpret_cast< UINT32_PTR * >(argp1);ecode2 = SWIG_AsVal_unsigned_SS_int(info[0], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "UINT32_PTR_assign" "', argument " "2"" of type '" "unsigned int""'");
  } 
  arg2 = static_cast< unsigned int >(val2);UINT32_PTR_assign(arg1,arg2);
  jsresult = env.Undefined();
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_UINT32_PTR_templ<SWIG_OBJ_WRAP>::_wrap_UINT32_PTR_value(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  UINT32_PTR *arg1 = (UINT32_PTR *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned int result;
  
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_UINT32_PTR_value.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_UINT32_PTR, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "UINT32_PTR_value" "', argument " "1"" of type '" "UINT32_PTR *""'"); 
  }
  arg1 = reinterpret_cast< UINT32_PTR * >(argp1);result = (unsigned int)UINT32_PTR_value(arg1);
  jsresult = SWIG_From_unsigned_SS_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned int >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_UINT32_PTR_templ<SWIG_OBJ_WRAP>::_wrap_UINT32_PTR_cast(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  UINT32_PTR *arg1 = (UINT32_PTR *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned int *result = 0 ;
  
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_UINT32_PTR_cast.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_UINT32_PTR, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "UINT32_PTR_cast" "', argument " "1"" of type '" "UINT32_PTR *""'"); 
  }
  arg1 = reinterpret_cast< UINT32_PTR * >(argp1);result = (unsigned int *)UINT32_PTR_cast(arg1);
  jsresult = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_unsigned_int, 0 |  0 );
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_UINT32_PTR_templ<SWIG_OBJ_WRAP>::_wrap_UINT32_PTR_frompointer(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  unsigned int *arg1 = (unsigned int *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  UINT32_PTR *result = 0 ;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_UINT32_PTR_frompointer.");
  }
  
  res1 = SWIG_ConvertPtr(info[0], &argp1,SWIGTYPE_p_unsigned_int, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "UINT32_PTR_frompointer" "', argument " "1"" of type '" "unsigned int *""'"); 
  }
  arg1 = reinterpret_cast< unsigned int * >(argp1);result = (UINT32_PTR *)UINT32_PTR_frompointer(arg1);
  jsresult = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_UINT32_PTR, 0 |  0 );
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


template <typename SWIG_OBJ_WRAP>
// js_ctor
// This is the main constructor
_exports_UINT8_ARRAY_templ<SWIG_OBJ_WRAP>::_exports_UINT8_ARRAY_templ(const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  Napi::Env env = info.Env();
  
  this->info = SWIGTYPE_p_UINT8_ARRAY;
  if (info.Length() == 1 && info[0].IsExternal()) {
    // This constructor has been called internally from C++/SWIG
    // to wrap an already existing C++ object in JS
    this->self = info[0].As<Napi::External<void>>().Data();
    this->owned = false;
    return;
  }
  this->owned = true;
  
  size_t arg1 ;
  size_t val1 ;
  int ecode1 = 0 ;
  UINT8_ARRAY *result;
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_new_UINT8_ARRAY.");
  }
  ecode1 = SWIG_AsVal_size_t(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "new_UINT8_ARRAY" "', argument " "1"" of type '" "size_t""'");
  } 
  arg1 = static_cast< size_t >(val1);result = (UINT8_ARRAY *)new_UINT8_ARRAY(SWIG_STD_MOVE(arg1));
  
  
  
  this->self = result;
  return;
  goto fail;
fail:
  return;
}

// This is the bypass constructor to be used from child classes
template <typename SWIG_OBJ_WRAP>
_exports_UINT8_ARRAY_templ<SWIG_OBJ_WRAP>::_exports_UINT8_ARRAY_templ(bool, const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  
}

SWIGINTERN void delete_UINT8_ARRAY(UINT8_ARRAY *self){
  delete [] self;
}

// js_dtoroverride
template <typename SWIG_OBJ_WRAP>
_exports_UINT8_ARRAY_templ<SWIG_OBJ_WRAP>::~_exports_UINT8_ARRAY_templ() {
  auto arg1 = reinterpret_cast<UINT8_ARRAY *>(this->self);
  if (this->owned && arg1) {
    delete_UINT8_ARRAY(arg1);
    this->self = nullptr;
  }
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_UINT8_ARRAY_templ<SWIG_OBJ_WRAP>::_wrap_UINT8_ARRAY_getitem(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  UINT8_ARRAY *arg1 = (UINT8_ARRAY *) 0 ;
  size_t arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  size_t val2 ;
  int ecode2 = 0 ;
  unsigned char result;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_UINT8_ARRAY_getitem.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_UINT8_ARRAY, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "UINT8_ARRAY_getitem" "', argument " "1"" of type '" "UINT8_ARRAY *""'"); 
  }
  arg1 = reinterpret_cast< UINT8_ARRAY * >(argp1);ecode2 = SWIG_AsVal_size_t(info[0], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "UINT8_ARRAY_getitem" "', argument " "2"" of type '" "size_t""'");
  } 
  arg2 = static_cast< size_t >(val2);result = (unsigned char)UINT8_ARRAY_getitem(arg1,SWIG_STD_MOVE(arg2));
  jsresult = SWIG_From_unsigned_SS_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< unsigned char >(result));
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_UINT8_ARRAY_templ<SWIG_OBJ_WRAP>::_wrap_UINT8_ARRAY_setitem(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  UINT8_ARRAY *arg1 = (UINT8_ARRAY *) 0 ;
  size_t arg2 ;
  unsigned char arg3 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  size_t val2 ;
  int ecode2 = 0 ;
  unsigned char val3 ;
  int ecode3 = 0 ;
  
  if(static_cast<int>(info.Length()) < 2 || static_cast<int>(info.Length()) > 2) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_UINT8_ARRAY_setitem.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_UINT8_ARRAY, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "UINT8_ARRAY_setitem" "', argument " "1"" of type '" "UINT8_ARRAY *""'"); 
  }
  arg1 = reinterpret_cast< UINT8_ARRAY * >(argp1);ecode2 = SWIG_AsVal_size_t(info[0], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "UINT8_ARRAY_setitem" "', argument " "2"" of type '" "size_t""'");
  } 
  arg2 = static_cast< size_t >(val2);ecode3 = SWIG_AsVal_unsigned_SS_char(info[1], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "UINT8_ARRAY_setitem" "', argument " "3"" of type '" "unsigned char""'");
  } 
  arg3 = static_cast< unsigned char >(val3);UINT8_ARRAY_setitem(arg1,SWIG_STD_MOVE(arg2),arg3);
  jsresult = env.Undefined();
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_UINT8_ARRAY_templ<SWIG_OBJ_WRAP>::_wrap_UINT8_ARRAY_cast(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  UINT8_ARRAY *arg1 = (UINT8_ARRAY *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  unsigned char *result = 0 ;
  
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_UINT8_ARRAY_cast.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_UINT8_ARRAY, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "UINT8_ARRAY_cast" "', argument " "1"" of type '" "UINT8_ARRAY *""'"); 
  }
  arg1 = reinterpret_cast< UINT8_ARRAY * >(argp1);result = (unsigned char *)UINT8_ARRAY_cast(arg1);
  jsresult = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_unsigned_char, 0 |  0 );
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_UINT8_ARRAY_templ<SWIG_OBJ_WRAP>::_wrap_UINT8_ARRAY_frompointer(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  unsigned char *arg1 = (unsigned char *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  UINT8_ARRAY *result = 0 ;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_UINT8_ARRAY_frompointer.");
  }
  
  res1 = SWIG_ConvertPtr(info[0], &argp1,SWIGTYPE_p_unsigned_char, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "UINT8_ARRAY_frompointer" "', argument " "1"" of type '" "unsigned char *""'"); 
  }
  arg1 = reinterpret_cast< unsigned char * >(argp1);result = (UINT8_ARRAY *)UINT8_ARRAY_frompointer(arg1);
  jsresult = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_UINT8_ARRAY, 0 |  0 );
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


template <typename SWIG_OBJ_WRAP>
// js_ctor
// This is the main constructor
_exports_INT8_ARRAY_templ<SWIG_OBJ_WRAP>::_exports_INT8_ARRAY_templ(const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  Napi::Env env = info.Env();
  
  this->info = SWIGTYPE_p_INT8_ARRAY;
  if (info.Length() == 1 && info[0].IsExternal()) {
    // This constructor has been called internally from C++/SWIG
    // to wrap an already existing C++ object in JS
    this->self = info[0].As<Napi::External<void>>().Data();
    this->owned = false;
    return;
  }
  this->owned = true;
  
  size_t arg1 ;
  size_t val1 ;
  int ecode1 = 0 ;
  INT8_ARRAY *result;
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_new_INT8_ARRAY.");
  }
  ecode1 = SWIG_AsVal_size_t(info[0], &val1);
  if (!SWIG_IsOK(ecode1)) {
    SWIG_exception_fail(SWIG_ArgError(ecode1), "in method '" "new_INT8_ARRAY" "', argument " "1"" of type '" "size_t""'");
  } 
  arg1 = static_cast< size_t >(val1);result = (INT8_ARRAY *)new_INT8_ARRAY(SWIG_STD_MOVE(arg1));
  
  
  
  this->self = result;
  return;
  goto fail;
fail:
  return;
}

// This is the bypass constructor to be used from child classes
template <typename SWIG_OBJ_WRAP>
_exports_INT8_ARRAY_templ<SWIG_OBJ_WRAP>::_exports_INT8_ARRAY_templ(bool, const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  
}

SWIGINTERN void delete_INT8_ARRAY(INT8_ARRAY *self){
  delete [] self;
}

// js_dtoroverride
template <typename SWIG_OBJ_WRAP>
_exports_INT8_ARRAY_templ<SWIG_OBJ_WRAP>::~_exports_INT8_ARRAY_templ() {
  auto arg1 = reinterpret_cast<INT8_ARRAY *>(this->self);
  if (this->owned && arg1) {
    delete_INT8_ARRAY(arg1);
    this->self = nullptr;
  }
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_INT8_ARRAY_templ<SWIG_OBJ_WRAP>::_wrap_INT8_ARRAY_getitem(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  INT8_ARRAY *arg1 = (INT8_ARRAY *) 0 ;
  size_t arg2 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  size_t val2 ;
  int ecode2 = 0 ;
  char result;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_INT8_ARRAY_getitem.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_INT8_ARRAY, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "INT8_ARRAY_getitem" "', argument " "1"" of type '" "INT8_ARRAY *""'"); 
  }
  arg1 = reinterpret_cast< INT8_ARRAY * >(argp1);ecode2 = SWIG_AsVal_size_t(info[0], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "INT8_ARRAY_getitem" "', argument " "2"" of type '" "size_t""'");
  } 
  arg2 = static_cast< size_t >(val2);result = (char)INT8_ARRAY_getitem(arg1,SWIG_STD_MOVE(arg2));
  jsresult = SWIG_From_char  SWIG_NAPI_FROM_CALL_ARGS(static_cast< char >(result));
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_INT8_ARRAY_templ<SWIG_OBJ_WRAP>::_wrap_INT8_ARRAY_setitem(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  INT8_ARRAY *arg1 = (INT8_ARRAY *) 0 ;
  size_t arg2 ;
  char arg3 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  size_t val2 ;
  int ecode2 = 0 ;
  char val3 ;
  int ecode3 = 0 ;
  
  if(static_cast<int>(info.Length()) < 2 || static_cast<int>(info.Length()) > 2) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_INT8_ARRAY_setitem.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_INT8_ARRAY, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "INT8_ARRAY_setitem" "', argument " "1"" of type '" "INT8_ARRAY *""'"); 
  }
  arg1 = reinterpret_cast< INT8_ARRAY * >(argp1);ecode2 = SWIG_AsVal_size_t(info[0], &val2);
  if (!SWIG_IsOK(ecode2)) {
    SWIG_exception_fail(SWIG_ArgError(ecode2), "in method '" "INT8_ARRAY_setitem" "', argument " "2"" of type '" "size_t""'");
  } 
  arg2 = static_cast< size_t >(val2);ecode3 = SWIG_AsVal_char(info[1], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "INT8_ARRAY_setitem" "', argument " "3"" of type '" "char""'");
  } 
  arg3 = static_cast< char >(val3);INT8_ARRAY_setitem(arg1,SWIG_STD_MOVE(arg2),arg3);
  jsresult = env.Undefined();
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_INT8_ARRAY_templ<SWIG_OBJ_WRAP>::_wrap_INT8_ARRAY_cast(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  INT8_ARRAY *arg1 = (INT8_ARRAY *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  char *result = 0 ;
  
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_INT8_ARRAY_cast.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_INT8_ARRAY, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "INT8_ARRAY_cast" "', argument " "1"" of type '" "INT8_ARRAY *""'"); 
  }
  arg1 = reinterpret_cast< INT8_ARRAY * >(argp1);result = (char *)INT8_ARRAY_cast(arg1);
  jsresult = SWIG_FromCharPtr((const char *)result);
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_INT8_ARRAY_templ<SWIG_OBJ_WRAP>::_wrap_INT8_ARRAY_frompointer(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  char *arg1 = (char *) 0 ;
  int res1 ;
  char *buf1 = 0 ;
  int alloc1 = 0 ;
  INT8_ARRAY *result = 0 ;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_INT8_ARRAY_frompointer.");
  }
  
  res1 = SWIG_AsCharPtrAndSize(info[0], &buf1, NULL, &alloc1);
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "INT8_ARRAY_frompointer" "', argument " "1"" of type '" "char *""'");
  }
  arg1 = reinterpret_cast< char * >(buf1);result = (INT8_ARRAY *)INT8_ARRAY_frompointer(arg1);
  jsresult = SWIG_NewPointerObj(SWIG_as_voidptr(result), SWIGTYPE_p_INT8_ARRAY, 0 |  0 );
  if (alloc1 == SWIG_NEWOBJ) delete[] buf1;
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


template <typename SWIG_OBJ_WRAP>
// js_ctor
// This is the main constructor
_exports_SeedKey_templ<SWIG_OBJ_WRAP>::_exports_SeedKey_templ(const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  Napi::Env env = info.Env();
  
  this->info = SWIGTYPE_p_SeedKey;
  if (info.Length() == 1 && info[0].IsExternal()) {
    // This constructor has been called internally from C++/SWIG
    // to wrap an already existing C++ object in JS
    this->self = info[0].As<Napi::External<void>>().Data();
    this->owned = false;
    return;
  }
  this->owned = true;
  
  SeedKey *result;
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_new_SeedKey.");
  }
  result = (SeedKey *)new SeedKey();
  
  
  this->self = result;
  return;
  goto fail;
fail:
  return;
}

// This is the bypass constructor to be used from child classes
template <typename SWIG_OBJ_WRAP>
_exports_SeedKey_templ<SWIG_OBJ_WRAP>::_exports_SeedKey_templ(bool, const Napi::CallbackInfo &info)
:SWIG_NAPI_ObjectWrap_templ<SWIG_OBJ_WRAP>(true, info) {
  
}


// js_dtoroverride
template <typename SWIG_OBJ_WRAP>
_exports_SeedKey_templ<SWIG_OBJ_WRAP>::~_exports_SeedKey_templ() {
  auto arg1 = reinterpret_cast<SeedKey *>(this->self);
  if (this->owned && arg1) {
    delete arg1;
    this->self = nullptr;
  }
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_SeedKey_templ<SWIG_OBJ_WRAP>::_wrap_SeedKey_LoadDLL(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  SeedKey *arg1 = (SeedKey *) 0 ;
  char *arg2 = (char *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  int res2 ;
  char *buf2 = 0 ;
  int alloc2 = 0 ;
  
  if(static_cast<int>(info.Length()) < 1 || static_cast<int>(info.Length()) > 1) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_SeedKey_LoadDLL.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_SeedKey, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "SeedKey_LoadDLL" "', argument " "1"" of type '" "SeedKey *""'"); 
  }
  arg1 = reinterpret_cast< SeedKey * >(argp1);res2 = SWIG_AsCharPtrAndSize(info[0], &buf2, NULL, &alloc2);
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "SeedKey_LoadDLL" "', argument " "2"" of type '" "char const *""'");
  }
  arg2 = reinterpret_cast< char * >(buf2);(arg1)->LoadDLL((char const *)arg2);
  jsresult = env.Undefined();
  
  if (alloc2 == SWIG_NEWOBJ) delete[] buf2;
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_SeedKey_templ<SWIG_OBJ_WRAP>::_wrap_SeedKey_Unload(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  SeedKey *arg1 = (SeedKey *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_SeedKey_Unload.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_SeedKey, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "SeedKey_Unload" "', argument " "1"" of type '" "SeedKey *""'"); 
  }
  arg1 = reinterpret_cast< SeedKey * >(argp1);(arg1)->Unload();
  jsresult = env.Undefined();
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_SeedKey_templ<SWIG_OBJ_WRAP>::_wrap_SeedKey_GenerateKeyExOpt(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  SeedKey *arg1 = (SeedKey *) 0 ;
  unsigned char *arg2 = (unsigned char *) 0 ;
  unsigned int arg3 ;
  unsigned int arg4 ;
  char *arg5 = (char *) 0 ;
  char *arg6 = (char *) 0 ;
  unsigned char *arg7 = (unsigned char *) 0 ;
  unsigned int arg8 ;
  unsigned int *arg9 = 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  void *argp2 = 0 ;
  int res2 = 0 ;
  unsigned int val3 ;
  int ecode3 = 0 ;
  unsigned int val4 ;
  int ecode4 = 0 ;
  int res5 ;
  char *buf5 = 0 ;
  int alloc5 = 0 ;
  int res6 ;
  char *buf6 = 0 ;
  int alloc6 = 0 ;
  void *argp7 = 0 ;
  int res7 = 0 ;
  unsigned int val8 ;
  int ecode8 = 0 ;
  void *argp9 = 0 ;
  int res9 = 0 ;
  int result;
  
  if(static_cast<int>(info.Length()) < 8 || static_cast<int>(info.Length()) > 8) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_SeedKey_GenerateKeyExOpt.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_SeedKey, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "SeedKey_GenerateKeyExOpt" "', argument " "1"" of type '" "SeedKey *""'"); 
  }
  arg1 = reinterpret_cast< SeedKey * >(argp1);res2 = SWIG_ConvertPtr(info[0], &argp2,SWIGTYPE_p_unsigned_char, 0 |  0 );
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "SeedKey_GenerateKeyExOpt" "', argument " "2"" of type '" "unsigned char const *""'"); 
  }
  arg2 = reinterpret_cast< unsigned char * >(argp2);ecode3 = SWIG_AsVal_unsigned_SS_int(info[1], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "SeedKey_GenerateKeyExOpt" "', argument " "3"" of type '" "unsigned int""'");
  } 
  arg3 = static_cast< unsigned int >(val3);ecode4 = SWIG_AsVal_unsigned_SS_int(info[2], &val4);
  if (!SWIG_IsOK(ecode4)) {
    SWIG_exception_fail(SWIG_ArgError(ecode4), "in method '" "SeedKey_GenerateKeyExOpt" "', argument " "4"" of type '" "unsigned int""'");
  } 
  arg4 = static_cast< unsigned int >(val4);res5 = SWIG_AsCharPtrAndSize(info[3], &buf5, NULL, &alloc5);
  if (!SWIG_IsOK(res5)) {
    SWIG_exception_fail(SWIG_ArgError(res5), "in method '" "SeedKey_GenerateKeyExOpt" "', argument " "5"" of type '" "char const *""'");
  }
  arg5 = reinterpret_cast< char * >(buf5);res6 = SWIG_AsCharPtrAndSize(info[4], &buf6, NULL, &alloc6);
  if (!SWIG_IsOK(res6)) {
    SWIG_exception_fail(SWIG_ArgError(res6), "in method '" "SeedKey_GenerateKeyExOpt" "', argument " "6"" of type '" "char const *""'");
  }
  arg6 = reinterpret_cast< char * >(buf6);res7 = SWIG_ConvertPtr(info[5], &argp7,SWIGTYPE_p_unsigned_char, 0 |  0 );
  if (!SWIG_IsOK(res7)) {
    SWIG_exception_fail(SWIG_ArgError(res7), "in method '" "SeedKey_GenerateKeyExOpt" "', argument " "7"" of type '" "unsigned char *""'"); 
  }
  arg7 = reinterpret_cast< unsigned char * >(argp7);ecode8 = SWIG_AsVal_unsigned_SS_int(info[6], &val8);
  if (!SWIG_IsOK(ecode8)) {
    SWIG_exception_fail(SWIG_ArgError(ecode8), "in method '" "SeedKey_GenerateKeyExOpt" "', argument " "8"" of type '" "unsigned int""'");
  } 
  arg8 = static_cast< unsigned int >(val8);res9 = SWIG_ConvertPtr(info[7], &argp9, SWIGTYPE_p_unsigned_int,  0 );
  if (!SWIG_IsOK(res9)) {
    SWIG_exception_fail(SWIG_ArgError(res9), "in method '" "SeedKey_GenerateKeyExOpt" "', argument " "9"" of type '" "unsigned int &""'"); 
  }
  if (!argp9) {
    SWIG_exception_fail(SWIG_ValueError, "invalid null reference " "in method '" "SeedKey_GenerateKeyExOpt" "', argument " "9"" of type '" "unsigned int &""'"); 
  }
  arg9 = reinterpret_cast< unsigned int * >(argp9);result = (int)(arg1)->GenerateKeyExOpt((unsigned char const *)arg2,arg3,arg4,(char const *)arg5,(char const *)arg6,arg7,arg8,*arg9);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  
  
  if (alloc5 == SWIG_NEWOBJ) delete[] buf5;
  if (alloc6 == SWIG_NEWOBJ) delete[] buf6;
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_SeedKey_templ<SWIG_OBJ_WRAP>::_wrap_SeedKey_GenerateKeyEx(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  SeedKey *arg1 = (SeedKey *) 0 ;
  unsigned char *arg2 = (unsigned char *) 0 ;
  unsigned int arg3 ;
  unsigned int arg4 ;
  char *arg5 = (char *) 0 ;
  unsigned char *arg6 = (unsigned char *) 0 ;
  unsigned int arg7 ;
  unsigned int *arg8 = 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  void *argp2 = 0 ;
  int res2 = 0 ;
  unsigned int val3 ;
  int ecode3 = 0 ;
  unsigned int val4 ;
  int ecode4 = 0 ;
  int res5 ;
  char *buf5 = 0 ;
  int alloc5 = 0 ;
  void *argp6 = 0 ;
  int res6 = 0 ;
  unsigned int val7 ;
  int ecode7 = 0 ;
  void *argp8 = 0 ;
  int res8 = 0 ;
  int result;
  
  if(static_cast<int>(info.Length()) < 7 || static_cast<int>(info.Length()) > 7) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_SeedKey_GenerateKeyEx.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_SeedKey, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "SeedKey_GenerateKeyEx" "', argument " "1"" of type '" "SeedKey *""'"); 
  }
  arg1 = reinterpret_cast< SeedKey * >(argp1);res2 = SWIG_ConvertPtr(info[0], &argp2,SWIGTYPE_p_unsigned_char, 0 |  0 );
  if (!SWIG_IsOK(res2)) {
    SWIG_exception_fail(SWIG_ArgError(res2), "in method '" "SeedKey_GenerateKeyEx" "', argument " "2"" of type '" "unsigned char const *""'"); 
  }
  arg2 = reinterpret_cast< unsigned char * >(argp2);ecode3 = SWIG_AsVal_unsigned_SS_int(info[1], &val3);
  if (!SWIG_IsOK(ecode3)) {
    SWIG_exception_fail(SWIG_ArgError(ecode3), "in method '" "SeedKey_GenerateKeyEx" "', argument " "3"" of type '" "unsigned int""'");
  } 
  arg3 = static_cast< unsigned int >(val3);ecode4 = SWIG_AsVal_unsigned_SS_int(info[2], &val4);
  if (!SWIG_IsOK(ecode4)) {
    SWIG_exception_fail(SWIG_ArgError(ecode4), "in method '" "SeedKey_GenerateKeyEx" "', argument " "4"" of type '" "unsigned int""'");
  } 
  arg4 = static_cast< unsigned int >(val4);res5 = SWIG_AsCharPtrAndSize(info[3], &buf5, NULL, &alloc5);
  if (!SWIG_IsOK(res5)) {
    SWIG_exception_fail(SWIG_ArgError(res5), "in method '" "SeedKey_GenerateKeyEx" "', argument " "5"" of type '" "char const *""'");
  }
  arg5 = reinterpret_cast< char * >(buf5);res6 = SWIG_ConvertPtr(info[4], &argp6,SWIGTYPE_p_unsigned_char, 0 |  0 );
  if (!SWIG_IsOK(res6)) {
    SWIG_exception_fail(SWIG_ArgError(res6), "in method '" "SeedKey_GenerateKeyEx" "', argument " "6"" of type '" "unsigned char *""'"); 
  }
  arg6 = reinterpret_cast< unsigned char * >(argp6);ecode7 = SWIG_AsVal_unsigned_SS_int(info[5], &val7);
  if (!SWIG_IsOK(ecode7)) {
    SWIG_exception_fail(SWIG_ArgError(ecode7), "in method '" "SeedKey_GenerateKeyEx" "', argument " "7"" of type '" "unsigned int""'");
  } 
  arg7 = static_cast< unsigned int >(val7);res8 = SWIG_ConvertPtr(info[6], &argp8, SWIGTYPE_p_unsigned_int,  0 );
  if (!SWIG_IsOK(res8)) {
    SWIG_exception_fail(SWIG_ArgError(res8), "in method '" "SeedKey_GenerateKeyEx" "', argument " "8"" of type '" "unsigned int &""'"); 
  }
  if (!argp8) {
    SWIG_exception_fail(SWIG_ValueError, "invalid null reference " "in method '" "SeedKey_GenerateKeyEx" "', argument " "8"" of type '" "unsigned int &""'"); 
  }
  arg8 = reinterpret_cast< unsigned int * >(argp8);result = (int)(arg1)->GenerateKeyEx((unsigned char const *)arg2,arg3,arg4,(char const *)arg5,arg6,arg7,*arg8);
  jsresult = SWIG_From_int  SWIG_NAPI_FROM_CALL_ARGS(static_cast< int >(result));
  
  
  
  
  if (alloc5 == SWIG_NEWOBJ) delete[] buf5;
  
  
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


// js_function
template <typename SWIG_OBJ_WRAP>
Napi::Value _exports_SeedKey_templ<SWIG_OBJ_WRAP>::_wrap_SeedKey_IsLoaded(const Napi::CallbackInfo &info) {
  Napi::Env env = info.Env();
  Napi::Value jsresult;
  SeedKey *arg1 = (SeedKey *) 0 ;
  void *argp1 = 0 ;
  int res1 = 0 ;
  bool result;
  
  if(static_cast<int>(info.Length()) < 0 || static_cast<int>(info.Length()) > 0) {
    SWIG_Error(SWIG_ERROR, "Illegal number of arguments for _wrap_SeedKey_IsLoaded.");
  }
  
  res1 = SWIG_ConvertPtr(info.This(), &argp1,SWIGTYPE_p_SeedKey, 0 |  0 );
  if (!SWIG_IsOK(res1)) {
    SWIG_exception_fail(SWIG_ArgError(res1), "in method '" "SeedKey_IsLoaded" "', argument " "1"" of type '" "SeedKey const *""'"); 
  }
  arg1 = reinterpret_cast< SeedKey * >(argp1);result = (bool)((SeedKey const *)arg1)->IsLoaded();
  jsresult = SWIG_From_bool  SWIG_NAPI_FROM_CALL_ARGS(static_cast< bool >(result));
  
  
  return jsresult;
  
  goto fail;
fail:
  return Napi::Value();
}


/* -------- TYPE CONVERSION AND EQUIVALENCE RULES (BEGIN) -------- */

static void *_p_INT8_ARRAYTo_p_char(void *x, int *SWIGUNUSEDPARM(newmemory)) {
    return (void *)((char *)  ((INT8_ARRAY *) x));
}
static void *_p_UINT8_ARRAYTo_p_unsigned_char(void *x, int *SWIGUNUSEDPARM(newmemory)) {
    return (void *)((unsigned char *)  ((UINT8_ARRAY *) x));
}
static void *_p_UINT32_PTRTo_p_unsigned_int(void *x, int *SWIGUNUSEDPARM(newmemory)) {
    return (void *)((unsigned int *)  ((UINT32_PTR *) x));
}
static swig_type_info _swigt__p_INT8_ARRAY = {"_p_INT8_ARRAY", "p_INT8_ARRAY|INT8_ARRAY *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_SeedKey = {"_p_SeedKey", "p_SeedKey|SeedKey *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_UINT32_PTR = {"_p_UINT32_PTR", "p_UINT32_PTR|UINT32_PTR *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_UINT8_ARRAY = {"_p_UINT8_ARRAY", "p_UINT8_ARRAY|UINT8_ARRAY *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_char = {"_p_char", "char *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_unsigned_char = {"_p_unsigned_char", "unsigned char *", 0, 0, (void*)0, 0};
static swig_type_info _swigt__p_unsigned_int = {"_p_unsigned_int", "unsigned int *", 0, 0, (void*)0, 0};

static swig_type_info *swig_type_initial[] = {
  &_swigt__p_INT8_ARRAY,
  &_swigt__p_SeedKey,
  &_swigt__p_UINT32_PTR,
  &_swigt__p_UINT8_ARRAY,
  &_swigt__p_char,
  &_swigt__p_unsigned_char,
  &_swigt__p_unsigned_int,
};

static swig_cast_info _swigc__p_INT8_ARRAY[] = {  {&_swigt__p_INT8_ARRAY, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_SeedKey[] = {  {&_swigt__p_SeedKey, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_UINT32_PTR[] = {  {&_swigt__p_UINT32_PTR, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_UINT8_ARRAY[] = {  {&_swigt__p_UINT8_ARRAY, 0, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_char[] = {  {&_swigt__p_char, 0, 0, 0},  {&_swigt__p_INT8_ARRAY, _p_INT8_ARRAYTo_p_char, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_unsigned_char[] = {  {&_swigt__p_unsigned_char, 0, 0, 0},  {&_swigt__p_UINT8_ARRAY, _p_UINT8_ARRAYTo_p_unsigned_char, 0, 0},{0, 0, 0, 0}};
static swig_cast_info _swigc__p_unsigned_int[] = {  {&_swigt__p_unsigned_int, 0, 0, 0},  {&_swigt__p_UINT32_PTR, _p_UINT32_PTRTo_p_unsigned_int, 0, 0},{0, 0, 0, 0}};

static swig_cast_info *swig_cast_initial[] = {
  _swigc__p_INT8_ARRAY,
  _swigc__p_SeedKey,
  _swigc__p_UINT32_PTR,
  _swigc__p_UINT8_ARRAY,
  _swigc__p_char,
  _swigc__p_unsigned_char,
  _swigc__p_unsigned_int,
};


/* -------- TYPE CONVERSION AND EQUIVALENCE RULES (END) -------- */




EnvInstanceData::EnvInstanceData(Napi::Env env, swig_module_info *swig_module) :
env(env), SWIG_NAPI_ObjectWrapCtor(nullptr), ctor(nullptr), swig_module(swig_module) {
  ctor = new Napi::FunctionReference*[swig_module->size + 1];
  for (size_t i = 0; i <= swig_module->size; i++) {
    ctor[i] = nullptr;
  }
}

EnvInstanceData::~EnvInstanceData() {
  for (size_t i = 0; i <= swig_module->size; i++) {
    if (ctor[i] != nullptr)
      delete ctor[i];
    ctor[i] = nullptr;
  }
  delete [] ctor;
  delete SWIG_NAPI_ObjectWrapCtor;
}

SWIGRUNTIME void
SWIG_NAPI_SetModule(Napi::Env env, swig_module_info *swig_module) {
  auto data = new EnvInstanceData(env, swig_module);
  env.SetInstanceData(data);
}

SWIGRUNTIME swig_module_info *
SWIG_NAPI_GetModule(Napi::Env env) {
  auto data = env.GetInstanceData<EnvInstanceData>();
  if (data == nullptr) return nullptr;
  return data->swig_module;
}

#define SWIG_GetModule(clientdata)                SWIG_NAPI_GetModule(clientdata)
#define SWIG_SetModule(clientdata, pointer)       SWIG_NAPI_SetModule(clientdata, pointer)
#define SWIG_INIT_CLIENT_DATA_TYPE                Napi::Env


/* -----------------------------------------------------------------------------
 * Type initialization:
 * This problem is tough by the requirement that no dynamic
 * memory is used. Also, since swig_type_info structures store pointers to
 * swig_cast_info structures and swig_cast_info structures store pointers back
 * to swig_type_info structures, we need some lookup code at initialization.
 * The idea is that swig generates all the structures that are needed.
 * The runtime then collects these partially filled structures.
 * The SWIG_InitializeModule function takes these initial arrays out of
 * swig_module, and does all the lookup, filling in the swig_module.types
 * array with the correct data and linking the correct swig_cast_info
 * structures together.
 *
 * The generated swig_type_info structures are assigned statically to an initial
 * array. We just loop through that array, and handle each type individually.
 * First we lookup if this type has been already loaded, and if so, use the
 * loaded structure instead of the generated one. Then we have to fill in the
 * cast linked list. The cast data is initially stored in something like a
 * two-dimensional array. Each row corresponds to a type (there are the same
 * number of rows as there are in the swig_type_initial array). Each entry in
 * a column is one of the swig_cast_info structures for that type.
 * The cast_initial array is actually an array of arrays, because each row has
 * a variable number of columns. So to actually build the cast linked list,
 * we find the array of casts associated with the type, and loop through it
 * adding the casts to the list. The one last trick we need to do is making
 * sure the type pointer in the swig_cast_info struct is correct.
 *
 * First off, we lookup the cast->type name to see if it is already loaded.
 * There are three cases to handle:
 *  1) If the cast->type has already been loaded AND the type we are adding
 *     casting info to has not been loaded (it is in this module), THEN we
 *     replace the cast->type pointer with the type pointer that has already
 *     been loaded.
 *  2) If BOTH types (the one we are adding casting info to, and the
 *     cast->type) are loaded, THEN the cast info has already been loaded by
 *     the previous module so we just ignore it.
 *  3) Finally, if cast->type has not already been loaded, then we add that
 *     swig_cast_info to the linked list (because the cast->type) pointer will
 *     be correct.
 * ----------------------------------------------------------------------------- */

#ifdef __cplusplus
extern "C" {
#if 0
} /* c-mode */
#endif
#endif

#if 0
#define SWIGRUNTIME_DEBUG
#endif

#ifndef SWIG_INIT_CLIENT_DATA_TYPE
#define SWIG_INIT_CLIENT_DATA_TYPE void *
#endif

SWIGRUNTIME void
SWIG_InitializeModule(SWIG_INIT_CLIENT_DATA_TYPE clientdata) {
  size_t i;
  swig_module_info *module_head, *iter;
  int init;

  /* check to see if the circular list has been setup, if not, set it up */
  if (swig_module.next==0) {
    /* Initialize the swig_module */
    swig_module.type_initial = swig_type_initial;
    swig_module.cast_initial = swig_cast_initial;
    swig_module.next = &swig_module;
    init = 1;
  } else {
    init = 0;
  }

  /* Try and load any already created modules */
  module_head = SWIG_GetModule(clientdata);
  if (!module_head) {
    /* This is the first module loaded for this interpreter */
    /* so set the swig module into the interpreter */
    SWIG_SetModule(clientdata, &swig_module);
  } else {
    /* the interpreter has loaded a SWIG module, but has it loaded this one? */
    iter=module_head;
    do {
      if (iter==&swig_module) {
        /* Our module is already in the list, so there's nothing more to do. */
        return;
      }
      iter=iter->next;
    } while (iter!= module_head);

    /* otherwise we must add our module into the list */
    swig_module.next = module_head->next;
    module_head->next = &swig_module;
  }

  /* When multiple interpreters are used, a module could have already been initialized in
     a different interpreter, but not yet have a pointer in this interpreter.
     In this case, we do not want to continue adding types... everything should be
     set up already */
  if (init == 0) return;

  /* Now work on filling in swig_module.types */
#ifdef SWIGRUNTIME_DEBUG
  printf("SWIG_InitializeModule: size %lu\n", (unsigned long)swig_module.size);
#endif
  for (i = 0; i < swig_module.size; ++i) {
    swig_type_info *type = 0;
    swig_type_info *ret;
    swig_cast_info *cast;

#ifdef SWIGRUNTIME_DEBUG
    printf("SWIG_InitializeModule: type %lu %s\n", (unsigned long)i, swig_module.type_initial[i]->name);
#endif

    /* if there is another module already loaded */
    if (swig_module.next != &swig_module) {
      type = SWIG_MangledTypeQueryModule(swig_module.next, &swig_module, swig_module.type_initial[i]->name);
    }
    if (type) {
      /* Overwrite clientdata field */
#ifdef SWIGRUNTIME_DEBUG
      printf("SWIG_InitializeModule: found type %s\n", type->name);
#endif
      if (swig_module.type_initial[i]->clientdata) {
	type->clientdata = swig_module.type_initial[i]->clientdata;
#ifdef SWIGRUNTIME_DEBUG
      printf("SWIG_InitializeModule: found and overwrite type %s \n", type->name);
#endif
      }
    } else {
      type = swig_module.type_initial[i];
    }

    /* Insert casting types */
    cast = swig_module.cast_initial[i];
    while (cast->type) {

      /* Don't need to add information already in the list */
      ret = 0;
#ifdef SWIGRUNTIME_DEBUG
      printf("SWIG_InitializeModule: look cast %s\n", cast->type->name);
#endif
      if (swig_module.next != &swig_module) {
        ret = SWIG_MangledTypeQueryModule(swig_module.next, &swig_module, cast->type->name);
#ifdef SWIGRUNTIME_DEBUG
	if (ret) printf("SWIG_InitializeModule: found cast %s\n", ret->name);
#endif
      }
      if (ret) {
	if (type == swig_module.type_initial[i]) {
#ifdef SWIGRUNTIME_DEBUG
	  printf("SWIG_InitializeModule: skip old type %s\n", ret->name);
#endif
	  cast->type = ret;
	  ret = 0;
	} else {
	  /* Check for casting already in the list */
	  swig_cast_info *ocast = SWIG_TypeCheck(ret->name, type);
#ifdef SWIGRUNTIME_DEBUG
	  if (ocast) printf("SWIG_InitializeModule: skip old cast %s\n", ret->name);
#endif
	  if (!ocast) ret = 0;
	}
      }

      if (!ret) {
#ifdef SWIGRUNTIME_DEBUG
	printf("SWIG_InitializeModule: adding cast %s\n", cast->type->name);
#endif
        if (type->cast) {
          type->cast->prev = cast;
          cast->next = type->cast;
        }
        type->cast = cast;
      }
      cast++;
    }
    /* Set entry in modules->types array equal to the type */
    swig_module.types[i] = type;
  }
  swig_module.types[i] = 0;

#ifdef SWIGRUNTIME_DEBUG
  printf("**** SWIG_InitializeModule: Cast List ******\n");
  for (i = 0; i < swig_module.size; ++i) {
    int j = 0;
    swig_cast_info *cast = swig_module.cast_initial[i];
    printf("SWIG_InitializeModule: type %lu %s\n", (unsigned long)i, swig_module.type_initial[i]->name);
    while (cast->type) {
      printf("SWIG_InitializeModule: cast type %s\n", cast->type->name);
      cast++;
      ++j;
    }
  printf("---- Total casts: %d\n",j);
  }
  printf("**** SWIG_InitializeModule: Cast List ******\n");
#endif
}

/* This function will propagate the clientdata field of type to
* any new swig_type_info structures that have been added into the list
* of equivalent types.  It is like calling
* SWIG_TypeClientData(type, clientdata) a second time.
*/
SWIGRUNTIME void
SWIG_PropagateClientData(void) {
  size_t i;
  swig_cast_info *equiv;
  static int init_run = 0;

  if (init_run) return;
  init_run = 1;

  for (i = 0; i < swig_module.size; i++) {
    if (swig_module.types[i]->clientdata) {
      equiv = swig_module.types[i]->cast;
      while (equiv) {
        if (!equiv->converter) {
          if (equiv->type && !equiv->type->clientdata)
            SWIG_TypeClientData(equiv->type, swig_module.types[i]->clientdata);
        }
        equiv = equiv->next;
      }
    }
  }
}

#ifdef __cplusplus
#if 0
{ /* c-mode */
#endif
}
#endif


Napi::Object Init(Napi::Env env, Napi::Object exports) {
  SWIG_InitializeModule(env);


  Napi::Function SWIG_NAPI_ObjectWrap_ctor = SWIG_NAPI_ObjectWrap_inst::GetClass(env);
  Napi::FunctionReference *SWIG_NAPI_ObjectWrap_ctor_ref = new Napi::FunctionReference();
  *SWIG_NAPI_ObjectWrap_ctor_ref = Napi::Persistent(SWIG_NAPI_ObjectWrap_ctor);
  env.GetInstanceData<EnvInstanceData>()->SWIG_NAPI_ObjectWrapCtor = SWIG_NAPI_ObjectWrap_ctor_ref;

  /* create objects for namespaces */
  

  /* register classes */
  /* Class: UINT32_PTR (_exports_UINT32_PTR) */
// jsnapi_registerclass
Napi::Function _exports_UINT32_PTR_ctor = _exports_UINT32_PTR_inst::GetClass(env);
exports.Set("UINT32_PTR", _exports_UINT32_PTR_ctor);
if (SWIGTYPE_p_UINT32_PTR->clientdata == nullptr) {
  SWIGTYPE_p_UINT32_PTR->clientdata = new size_t(0);
}
Napi::FunctionReference *_exports_UINT32_PTR_ctor_ref = new Napi::FunctionReference();
*_exports_UINT32_PTR_ctor_ref = Napi::Persistent(_exports_UINT32_PTR_ctor);
env.GetInstanceData<EnvInstanceData>()->ctor[0] = _exports_UINT32_PTR_ctor_ref;
/* Class: UINT8_ARRAY (_exports_UINT8_ARRAY) */
// jsnapi_registerclass
Napi::Function _exports_UINT8_ARRAY_ctor = _exports_UINT8_ARRAY_inst::GetClass(env);
exports.Set("UINT8_ARRAY", _exports_UINT8_ARRAY_ctor);
if (SWIGTYPE_p_UINT8_ARRAY->clientdata == nullptr) {
  SWIGTYPE_p_UINT8_ARRAY->clientdata = new size_t(1);
}
Napi::FunctionReference *_exports_UINT8_ARRAY_ctor_ref = new Napi::FunctionReference();
*_exports_UINT8_ARRAY_ctor_ref = Napi::Persistent(_exports_UINT8_ARRAY_ctor);
env.GetInstanceData<EnvInstanceData>()->ctor[1] = _exports_UINT8_ARRAY_ctor_ref;
/* Class: INT8_ARRAY (_exports_INT8_ARRAY) */
// jsnapi_registerclass
Napi::Function _exports_INT8_ARRAY_ctor = _exports_INT8_ARRAY_inst::GetClass(env);
exports.Set("INT8_ARRAY", _exports_INT8_ARRAY_ctor);
if (SWIGTYPE_p_INT8_ARRAY->clientdata == nullptr) {
  SWIGTYPE_p_INT8_ARRAY->clientdata = new size_t(2);
}
Napi::FunctionReference *_exports_INT8_ARRAY_ctor_ref = new Napi::FunctionReference();
*_exports_INT8_ARRAY_ctor_ref = Napi::Persistent(_exports_INT8_ARRAY_ctor);
env.GetInstanceData<EnvInstanceData>()->ctor[2] = _exports_INT8_ARRAY_ctor_ref;
/* Class: SeedKey (_exports_SeedKey) */
// jsnapi_registerclass
Napi::Function _exports_SeedKey_ctor = _exports_SeedKey_inst::GetClass(env);
exports.Set("SeedKey", _exports_SeedKey_ctor);
if (SWIGTYPE_p_SeedKey->clientdata == nullptr) {
  SWIGTYPE_p_SeedKey->clientdata = new size_t(3);
}
Napi::FunctionReference *_exports_SeedKey_ctor_ref = new Napi::FunctionReference();
*_exports_SeedKey_ctor_ref = Napi::Persistent(_exports_SeedKey_ctor);
env.GetInstanceData<EnvInstanceData>()->ctor[3] = _exports_SeedKey_ctor_ref;


  /* enable inheritance */
  
Napi::Value jsObjectValue, jsSetProtoValue;
Napi::Object jsObject;
Napi::Function setProto;
NAPI_CHECK_RESULT(env.Global().Get("Object"), jsObjectValue);
NAPI_CHECK_RESULT(jsObjectValue.ToObject(), jsObject);
NAPI_CHECK_RESULT(jsObject.Get("setPrototypeOf"), jsSetProtoValue);
setProto = jsSetProtoValue.As<Napi::Function>();



  /* setup inheritances */
  
// Inheritance for _exports_UINT32_PTR (UINT32_PTR) <- SWIG_NAPI_ObjectWrap
// jsnapi_setup_inheritance
do {
  Napi::Value protoBase, protoSub;
  NAPI_CHECK_RESULT(_exports_UINT32_PTR_ctor.Get("prototype"), protoSub);
  NAPI_CHECK_RESULT(SWIG_NAPI_ObjectWrap_ctor.Get("prototype"), protoBase);
  NAPI_CHECK_MAYBE(setProto.Call({
    _exports_UINT32_PTR_ctor, SWIG_NAPI_ObjectWrap_ctor
  }));
  NAPI_CHECK_MAYBE(setProto.Call({
    protoSub, protoBase
  }));
} while (0);


// Inheritance for _exports_UINT8_ARRAY (UINT8_ARRAY) <- SWIG_NAPI_ObjectWrap
// jsnapi_setup_inheritance
do {
  Napi::Value protoBase, protoSub;
  NAPI_CHECK_RESULT(_exports_UINT8_ARRAY_ctor.Get("prototype"), protoSub);
  NAPI_CHECK_RESULT(SWIG_NAPI_ObjectWrap_ctor.Get("prototype"), protoBase);
  NAPI_CHECK_MAYBE(setProto.Call({
    _exports_UINT8_ARRAY_ctor, SWIG_NAPI_ObjectWrap_ctor
  }));
  NAPI_CHECK_MAYBE(setProto.Call({
    protoSub, protoBase
  }));
} while (0);


// Inheritance for _exports_INT8_ARRAY (INT8_ARRAY) <- SWIG_NAPI_ObjectWrap
// jsnapi_setup_inheritance
do {
  Napi::Value protoBase, protoSub;
  NAPI_CHECK_RESULT(_exports_INT8_ARRAY_ctor.Get("prototype"), protoSub);
  NAPI_CHECK_RESULT(SWIG_NAPI_ObjectWrap_ctor.Get("prototype"), protoBase);
  NAPI_CHECK_MAYBE(setProto.Call({
    _exports_INT8_ARRAY_ctor, SWIG_NAPI_ObjectWrap_ctor
  }));
  NAPI_CHECK_MAYBE(setProto.Call({
    protoSub, protoBase
  }));
} while (0);


// Inheritance for _exports_SeedKey (SeedKey) <- SWIG_NAPI_ObjectWrap
// jsnapi_setup_inheritance
do {
  Napi::Value protoBase, protoSub;
  NAPI_CHECK_RESULT(_exports_SeedKey_ctor.Get("prototype"), protoSub);
  NAPI_CHECK_RESULT(SWIG_NAPI_ObjectWrap_ctor.Get("prototype"), protoBase);
  NAPI_CHECK_MAYBE(setProto.Call({
    _exports_SeedKey_ctor, SWIG_NAPI_ObjectWrap_ctor
  }));
  NAPI_CHECK_MAYBE(setProto.Call({
    protoSub, protoBase
  }));
} while (0);



  /* create and register namespace objects */
  

  return exports;
  goto fail;
fail:
  return Napi::Object();
}

NODE_API_MODULE(xmlpp, Init)
