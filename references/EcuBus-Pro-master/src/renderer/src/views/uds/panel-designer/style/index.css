._fc-designer {
    height: 100%;
    min-height: 500px;
    overflow: hidden;
    cursor: default;
    position: relative;
    background-color: #FFF;
    --fc-drag-empty: "拖拽左侧列表中的组件到此处";
    --fc-child-empty: "点击右下角 \e789  按钮添加一列";
    --fc-tool-border-color: #2E73FF;
}

._fc-designer > .el-main {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 0;
}

._fc-l-menu {
    display: flex;
    flex-direction: column;
    align-items: center;
    border-top: 1px solid #ECECEC;
    border-right: 1px solid #ECECEC;
}

._fc-l-menu-item {
    display: flex;
    width: 100%;
    align-items: center;
    justify-content: center;
    height: 40px;
    cursor: pointer;
    box-sizing: border-box;
}

._fc-l-menu-item.active {
    color: #2E73FF;
}

._fc-l-menu-form {
    border-bottom: 1px solid #ECECEC;
}

._fc-l-menu-item i {
    font-size: 22px;
}

._fc-l-menu-item i:hover {
    color: #2E73FF;
}

._fc-l-menu-item .el-badge__content {
    --el-badge-size: 15px;
    --el-badge-padding: 4px;
    background-color: #2E73FF;
}

._fc-l-label {
    font-weight: 500;
    font-size: 14px;
    color: #262626;
    line-height: 17px;
    padding: 12px;
    margin-top: 5px;
}

._fc-l-info {
    font-weight: 400;
    font-size: 12px;
    color: #AAAAAA;
    line-height: 17px;
    text-align: left;
    font-style: normal;
    padding: 0 12px;
}

._fc-l > .el-container {
    height: 100%;
}

._fc-m .form-create ._fc-l-item {
    display: flex !important;
    align-items: center;
    justify-content: center;
    background: #f5f5f5;
    color: #000;
    width: 100%;
    margin: 5px 0;
    height: 30px;
    overflow: hidden;
    transition: all .3s ease;
    border: 1px dashed #000;
    border-radius: 4px;
    padding-bottom: 0;
}

._fc-m .form-create ._fc-l-item ._fc-l-icon {
    display: inline-block !important;
    padding: 0 4px;
}

._fc-m .form-create ._fc-l-item ._fc-l-name {
    display: inline-block !important;
    font-size: 12px;
}

._fc-l, ._fc-m, ._fc-r {
    border-top: 1px solid #ECECEC;
    box-sizing: border-box;
}

._fc-r {
    --el-color-primary: #2E73FF;
}

._fc-r-tab-props {
    padding: 0 20px;
    position: relative;
}

._fc-r-tools-close {
    position: absolute;
    right: 24px;
    top: 12px;
    transform: rotate(45deg);
    color: #666666;
    cursor: pointer;
}

._fc-r-title {
    font-size: 12px;
    color: #333333;
    margin: 15px 0 5px 0;
}

._fc-r-sub ._fc-r-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

._fc-r-sub .fc-icon {
    cursor: pointer;
}

._fc-r-sub ._fd-config-item + ._fd-config-item {
    margin-top: 8px;
}

._fc-r-sub > ._fd-config-item > ._fd-ci-head {
    position: relative;
    padding-left: 8px;
}

._fc-r-sub > ._fd-config-item > ._fd-ci-head:before {
    content: ' ';
    position: absolute;
    width: 5px;
    height: 5px;
    background-color: #333;
    border-radius: 25px;
    left: 0;
}

._fc-r-config {
    display: grid;
    grid-template-columns: repeat(1, 1fr);
    grid-template-areas: "base" "props" "style" "event" "validate";
}

._fc-r-name-input .el-input-group__append {
    width: 25px;
    padding: 0;
    margin: 0;
    color: #606266;
    cursor: pointer;
}

._fc-r-name-input .icon-group {
    cursor: pointer
}

._fc-r-name-input .icon-group:hover {
    color: #2E73FF;
}

._fc-r .el-main {
    padding-bottom: 100px;
}

._fc-l .el-main {
    padding: 0;
}

._fc-l .el-tree-node__label {
    padding: 3px;
    font-weight: 400;
    color: #333;
}

._fc-l .el-tree-node__content {
    height: 30px;
    margin-top: 5px;
}

._fc-l .el-tree-node__content > .el-tree-node__expand-icon {
    color: #333;
}

._fc-l .el-tree-node__expand-icon.is-leaf {
    color: transparent;
}

@keyframes rotating {
    0% {
        transform: rotate(0)
    }

    to {
        transform: rotate(360deg)
    }
}

._fc-loading {
    animation: rotating 2s linear infinite;
}

._fc-tree-node {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 26px;
    line-height: 26px;
    padding-right: 5px
}

._fc-tree-node.active, ._fc-tree-node.active .icon-more {
    color: #2E73FF;
}

._fc-tree-label {
    display: flex;
    align-items: center;
}

._fc-tree-label > i {
    margin-right: 5px;
    font-weight: 700;
}

._fc-tree-more {
    display: flex;
    align-items: center;
    padding: 0 15px;
    font-weight: 700;
}

._fc-l-tabs {
    display: flex;
    border-bottom: 1px solid #ECECEC;
    padding: 0;
}

._fc-l-tab {
    height: 40px;
    box-sizing: border-box;
    line-height: 40px;
    display: inline-block;
    list-style: none;
    font-size: 14px;
    font-weight: 600;
    color: #303133;
    position: relative;
    flex: 1;
    text-align: center;
}

._fc-l ._fc-l-tab.active {
    color: #409EFF;
    border-bottom: 2px solid #409EFF;
}

._fc-l-group {
    border: 1px solid #EEEEEE;
    padding: 0;
    margin: 12px;
    user-select: none;
}

._fc-l-group ._fc-l-list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    align-items: stretch;
}

._fc-l-title {
    display: flex;
    align-items: center;
    justify-content: space-between;
    cursor: pointer;
    font-weight: 600;
    font-size: 14px;
    padding: 12px;
    margin: 0;
}

._fc-l-title i {
    font-size: 14px;
}

._fc-l-title i.down {
    transform: rotate(90deg);
}

._fc-l-item {
    display: inline-block;
    background: #FFF;
    color: #000;
    line-height: 1;
    text-align: center;
    transition: all .2s ease;
    cursor: pointer;
    padding-bottom: 10px;
}

._fc-l-item i {
    font-size: 21px;
    display: inline-block;
}

._fc-l-item ._fc-l-name {
    font-size: 12px;
}

._fc-l-item ._fc-l-icon {
    padding: 10px 5px 12px;
}

._fc-l-item:hover {
    background: #2E73FF;
    color: #fff;
}

._fc-m-tools {
    height: 40px;
    align-items: center;
    display: flex;
    justify-content: space-between;
    border: 1px solid #ECECEC;
    border-top: 0 none;
    white-space: nowrap;
}

._fc-m-tools-l, ._fc-m-tools-r {
    display: flex;
    align-items: center;
}

._fc-m-tools-r {
    overflow: auto;
}


._fc-m-tools-l .devices .fc-icon {
    width: 18px;
    cursor: pointer;
}

._fc-m-tools-l .devices .fc-icon.active {
    color: #2E73FF;
}

._fc-m-tools-l .devices .fc-icon + .fc-icon {
    margin-left: 5px;
}

._fc-m-tools .line {
    width: 1px;
    height: 24px;
    background: #D8D8D8;
    margin: 0 10px;
}

._fc-m-tools .el-button {
    padding: 5px 10px;
    display: flex;
    align-items: center;
    border-radius: 5px;
}

._fc-m-tools .el-button > span {
    display: inline-flex;
    justify-content: center;
    align-items: center;
}

._fc-m-tools .el-dropdown, ._fc-m-tools .el-button + .el-button {
    margin-left: 10px;
}

._fc-m-tools ._fd-m-extend {
    color: #666;
    border-color: #ccc;
    background-color: #f1f1f1;
    border-radius: 5px;
    padding: 5px;
}

._fc-m-tools ._fd-m-extend .fc-icon {
    margin-right: 0;
}

._fc-m-tools ._fd-input-btn {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 12px;
}

._fc-m-tools ._fd-input-btn .el-switch__action {
    width: 18px;
    height: 18px;
    left: 2px;
}

._fc-m-tools ._fd-input-btn .is-checked .el-switch__action {
    --el-color-white: #2E73FF;
    left: calc(100% - 20px);
}

._fc-m-tools ._fd-input-btn .el-switch__core {
    --el-switch-on-color: var(--el-switch-off-color);
    border-radius: 16px;
    height: 24px;
    width: 46px;
}

._fc-m-tools ._fd-input-btn .is-checked .el-switch__core {
    --el-switch-on-color: var(--el-switch-off-color);
    border-radius: 16px;
    height: 24px;
    width: 46px;
}

._fc-m-tools-r .fc-icon {
    font-size: 14px;
}

._fc-m-tools-l .fc-icon {
    font-size: 18px;
    cursor: pointer;
}

._fc-m-tools-l .fc-icon + .fc-icon {
    margin-left: 10px;
}

._fc-m-tools-l .fc-icon.disabled {
    color: #999;
    cursor: not-allowed;
}

._fc-r .el-tabs__nav-wrap::after {
    height: 1px;
    background-color: #ECECEC;
}

._fc-r ._fc-r-tabs {
    display: flex;
    padding: 0;
    border-bottom: 1px solid #ECECEC;
}

._fc-r .el-table__cell .cell, ._fc-r .el-button, ._fc-r .el-radio-button__inner {
    font-weight: 400;
}

._fc-r ._fc-r-tab {
    height: 40px;
    box-sizing: border-box;
    line-height: 40px;
    display: inline-block;
    list-style: none;
    font-size: 14px;
    font-weight: 600;
    color: #303133;
    position: relative;
    flex: 1;
    text-align: center;
}

._fc-r ._fc-r-tab.active {
    color: #409EFF;
    border-bottom: 2px solid #409EFF;
}

._fc-m-con {
    position: relative;
    background: #F5F5F5;
    padding: 20px 20px 36px;
}

._fc-m-drag {
    margin: 0 auto;
    overflow: auto;
    padding: 2px;
    box-sizing: border-box;
}

._fc-m-input {
    padding: 5px 5px 80px;
}

._fc-m-input-handle {
    position: absolute;
    bottom: 17px;
    left: 0;
    right: 0;
    padding: 12px;
    background: #FFFFFF;
    box-shadow: 0 2px 10px 0 rgba(0, 0, 0, 0.05);
    text-align: center;
    margin: 20px;
    z-index: 99;
}

._fc-m-drag.mobile {
    width: 400px;
}

._fc-m-drag.pad {
    width: 770px;
}

._fc-m-drag, .draggable-drag {
    background: #fff;
    height: 100%;
    position: relative;
}

._fc-m-drag > form, ._fc-m-drag > form > .el-row {
    height: 100%;
}

._fc-m-drag .el-tree {
    width: 100%;
}

._fd-drag-box {
    width: 100%;
    height: 100%;
    min-height: 80px;
    transition: padding-bottom, padding-top .3s ease;
}

._fd-drag-box ._fd-drag-box {
    outline: 1px dashed #ECECEC;
}

._fd-drag-tool > ._fd-drag-box {
    outline: none;
}

._fd-drag-box > div[data-draggable] {
    margin-bottom: 1px;
}

._fc-r ._fc-group-container + ._fc-group-container {
    margin-top: 20px;
}

._fc-r ._fc-group-container {
    margin: 0;
    padding: 10px;
}

._fc-r ._fc-group-handle {
    right: 15px;
}

._fc-r .el-form-item {
    margin-bottom: 10px !important;
}

._fc-r .el-form-item__label {
    color: #333333;
}

._fc-upload-preview {
    display: inline-block;
    width: 120px;
    height: 120px;
    border-radius: 5px;
    overflow: hidden;
}

._fc-tabs .el-tabs__item {
    font-weight: 400;
}

._fc-tabs .el-tabs__content {
    overflow: auto;
}

._fc-tabs .el-tabs__nav-scroll {
    padding: 0 15px;
}

._fc-tabs .el-tab-pane {
    margin-right: 15px;
}

._fc-tabs .el-tabs__nav-wrap::after {
    height: 1px;
}

.form-create .fc-none {
    display: none;
}

._fd-draggable-drag.drag-holder, ._fd-tableFormColumn-drag.drag-holder, ._fd-elTabPane-drag.drag-holder, ._fd-group-drag.drag-holder, ._fd-subForm-drag.drag-holder, ._fd-elCard-drag.drag-holder, ._fd-elCollapseItem-drag.drag-holder {
    position: relative;
    background: #f5f5f5;
    background-size: 0;
    min-height: 90px;
}

._fc-child-empty:after, ._fd-draggable-drag.drag-holder:after, ._fd-tableFormColumn-drag.drag-holder:after, ._fd-elTabPane-drag.drag-holder:after, ._fd-group-drag.drag-holder:after, ._fd-subForm-drag.drag-holder:after, ._fd-elCard-drag.drag-holder:after, ._fd-elCollapseItem-drag.drag-holder:after {
    content: var(--fc-drag-empty);
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #aaa;
    font-size: 12px;
}

._fc-designer ._fc-m-drag ._fd-draggable-drag {
    overflow: auto;
    padding: 2px 2px 100px;
}

._fc-m-drag._fd-drop-hover ._fd-draggable-drag {
    padding-top: 20px;
}

._fd-draggable-drag.drag-holder {
    background-color: #ffffff;
}

._fd-draggable-drag.drag-holder:after {
    font-size: 16px;
}

._fc-child-empty:after {
    font-family: "fc-icon" !important;
    content: var(--fc-child-empty);
}

.fc-configured {
    color: #999;
    margin-left: 5px;
}

._fc-manage-text {
    cursor: pointer;
    color: #2f73ff;
    margin-left: 4px;
    font-size: 12px;
}

._fc-manage-text i {
    font-size: 12px;
}

._fc-message-error {
    top: 16px;
    z-index: 2116;
    border-radius: 8px;
    padding: 9px 13px;
    background-color: #fff;
    border-color: #fff;
    box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
}

._fc-message-error > .el-icon {
    font-size: 18px;
}

._fc-message-error .el-message__content {
    color: rgba(0, 0, 0, 0.88);
    font-size: 14px;
}

._fd-preview-copy{
    display: flex;
    position: absolute;
    right: 35px;
    top: 65px;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    background: #2E73FF33;
    border-radius: 10px;
    color: #2E73FF;
    cursor: pointer;
}

._fd-preview-dialog {
    border-radius: 6px;
    padding-top: 0;
}

._fd-preview-dialog .el-dialog__header {
    position: absolute;
    right: 0;
    top: 0;
    float: right;
    z-index: 9;
}

._fd-preview-code {
    margin-top: 0;
    max-height: 510px;
    overflow: auto;
}

._fd-preview-tabs .el-tabs__nav-wrap::after {
    height: 1px;
}

._fd-preview-tabs .el-tabs__item {
    height: 46px;
}

._fd-preview-code > code {
    white-space: pre-wrap;
}

._fd-row-line {
    width: 100%;
    height: 1px;
    margin: 10px 0;
    background: #D8D8D8;
}

.CodeMirror-hints {
    z-index: 999999;
}
