{
  "extends": "@electron-toolkit/tsconfig/tsconfig.web.json",
  "include": [
    "src/renderer/src/env.d.ts",
    "src/renderer/src/**/*",
    "src/renderer/src/**/*.vue",
    "src/preload/*.d.ts",
    "src/main/share/*",
  ],
  "files": ["package.json"],
  "compilerOptions": {
    "composite": true,
    "baseUrl": ".",
    "noUnusedLocals":false,
    "noUnusedParameters":false,
    "resolveJsonModule": true,
    "paths": {
      "@r/*": [
        "src/renderer/src/*"
      ],
      "nodeCan/*": [
        "src/main/share/*"
      ]
    }
  }
}
