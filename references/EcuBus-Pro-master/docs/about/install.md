# Install

You can download the latest version of `EcuBus-Pro` from the [github releases page](https://github.com/ecubus/EcuBus-Pro/releases)
Or from our CDN page if you are in 中国❤️:<CustomComponent/>

> [!TIP]
> Right now we only support Windows version of EcuBus-Pro, we will support Linux version in the future.

> [!TIP]
> The minimum version of Windows is Windows 10, but we recommend Windows 11.

## Windows Install Guide

![alt text](image.png)

### You can decide for everyone or just for yourself

![alt text](image-1.png)

### You can see the detail information when open the `EcuBus-Pro`

![alt text](image-2.png)

## Auto Update

`EcuBus-Pro` will check the latest version when you open it, if there is a new version, it will prompt you to update.
![alt text](update1.png)

- You can see the detail update information, click the `start to update` button to update.
  ![alt text](update2.png)

- Will display the update progress, please wait for a moment.
  ![alt text](update3.png)

- After the update is complete, you can click the `restart` button to restart the `EcuBus-Pro`.

<script setup>
import CustomComponent from './download.vue'
</script>
