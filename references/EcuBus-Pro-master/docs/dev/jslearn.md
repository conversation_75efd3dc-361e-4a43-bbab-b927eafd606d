# JavaScript/Typescript Learning Resources

## JavaScript

JavaScript is a programming language initially designed to interact with elements of web pages. Within web browsers, JavaScript consists of three main parts

- [JavaScript Tutorial 1](https://www.w3schools.com/js/)
- [JavaScript Tutorial 2](https://www.javascripttutorial.net/)

## Client-side vs. Server-side JavaScript

When JavaScript is used on a web page, it is executed in web browsers, serving as a client-side language.

JavaScript can run on both web browsers and servers. A popular JavaScript server-side environment is Node.js. Unlike client-side JavaScript, server-side JavaScript executes on the server and allows you to access databases, file systems, etc.

Node.js is not a programming language like Python, Java or C/C++. Node.js is a runtime, similar to Java virtual machine, that converts JavaScript code into machine code. It is , widely used by thousands of developers around the world to develop I/O intensive web applications like video streaming sites, single-page applications, and other web applications.

- [Node.js Tutorial 1](https://www.w3schools.com/nodejs/)
- [Node.js Tutorial 2](https://www.tutorialspoint.com/nodejs/index.htm)

## TypeScript

TypeScript is a superset of JavaScript that adds optional static types to the language. TypeScript is designed for the development of large applications and can be used to develop JavaScript applications for both client-side and server-side execution.

- [TypeScript Tutorial 1](https://www.typescriptlang.org/docs/handbook/typescript-in-5-minutes.html)
- [TypeScript Tutorial 2](https://www.tutorialspoint.com/typescript/index.htm)
