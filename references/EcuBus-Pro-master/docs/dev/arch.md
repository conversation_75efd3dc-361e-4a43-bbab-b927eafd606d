# Architecture

The architecture of the project is based on the `Electron` framework, which is a popular framework for building cross-platform desktop applications with JavaScript, HTML, and CSS.
More information about the `Electron` framework can be found [here](https://www.electronjs.org/).

## Technology Stack

- `Electron` - The core framework for building the desktop application.
- `Vue` - The front-end framework for building the user interface.
- `TypeScript` - The programming language for building the application.
- `Vite` - The build tool for the project.
- `Webpack` - The build tool for the project.
- `Electron-builder` - The build tool for packaging the application.
- `ElementPlus` - The UI library for building the user interface.
- `Vite-Test` - The test tool for the project.
- `node-addon-api` - The API for building the native node modules.
- `node-gyp` - The build tool for the native node modules.
- `swig` - The template engine for the project.

We use [Electron-Vite](https://electron-vite.org/) to combine these stack together, which is a template for building `Electron` applications with `Vite`.
