# Addon

`Node.js` access dll/so library need Addon, this document will show you how to write setup Addon.

> [!TIP]
> Make sure you have installed `node-gyp`

Also need `node-addon-api`

## Test Node-gyp

```bash
cd src/main/docan
npx node-gyp rebuild
```

you will see the following output:

```bash
gyp info ok
```

## SWIG

`SWIG` is a software development tool that connects programs written in C and C++ with a variety of high-level programming languages.
more information about `SWIG` [here](https://github.com/swig/swig)

`All warp code should generated by SWIG`

you can see the example code:

- src/main/docan/peak/swig/peak.i
- src/main/docan/kavser/swig/kvaser.i
