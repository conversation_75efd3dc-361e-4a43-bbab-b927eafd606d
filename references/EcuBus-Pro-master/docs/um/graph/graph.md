# Graph

The graph signal feature allows you to visualize the data flow between signals in a graphical format. This feature is especially useful for understanding the relationship between signals and for debugging complex signal interactions.
![alt text](image.png)

* **Line**
* **Gauge** 
* **Data**


## Adding Signals

![alt text](image-1.png)

Add signal from database, which depends on [database](./../database.md).

![alt text](image-2.png)

## Add Variabls
![alt text](image-7.png)

All valid variables from [`Variable Window`](./../var/var.md).

## Multi Signal In One Graph

![alt text](image-3.png)
![alt text](image-5.png)
![alt text](image-6.png)

## Edit Signal Property

![alt text](image-4.png)

## Drag/Zoom Line Graph

You you zoom in/out the graph by dragging the mouse wheel.
![alt text](graph.gif)
