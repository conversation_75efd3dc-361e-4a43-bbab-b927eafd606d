{
  "compilerOptions": {
    "composite": true,
    "target": "esnext",
    "module": "esnext",
    "sourceMap": false,
    "strict": true,
    "jsx": "preserve",
    "allowJs": true,
    "esModuleInterop": true,
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "forceConsistentCasingInFileNames": true,
    "skipLibCheck": true,
    "declaration": true,
    "types": ["node"],
    "baseUrl": ".",
    "outDir": "./dist"
  },
  "ts-node": {
    "compilerOptions": {
      "module": "commonjs"
    }
  },
  "include": [
    "src/main/share/*.ts",
    "src/main/worker/*.d.ts",
    "src/main/worker/*.ts",
  ]
}