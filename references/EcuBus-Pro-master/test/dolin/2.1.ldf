/*************************************************************************************/

//                                                                                     

// Description: LIN Description file created using <PERSON><PERSON>'s DaVinci Network Designer   

// Created:     07 Sep 2007 14:37:29

// Author:      Vector Informatik GmbH

// Version:     0.1                                                                    

//                                                                                     

/*************************************************************************************/

LIN_description_file;
LIN_protocol_version = "2.1";
LIN_language_version = "2.1";
LIN_speed = 19.2 kbps;

Nodes {
  Master: SeatECU, 5 ms, 0.1 ms ;
  Slaves: Motor1, Motor2 ;
}

Signals {
  Motor1ErrorCode: 8, 5, Motor1, SeatECU ;
  Motor1ErrorValue: 8, 1, Motor1, SeatECU ;
  Motor1LinError: 1, 0, Motor1, SeatECU ;
  Motor1Position: 32, {0, 0, 0, 0}, Motor1, SeatECU ;
  Motor1Selection: 2, 0, SeatECU, Motor1 ;
  Motor1Temp: 8, 5, Motor1, SeatECU ;
  Motor2ErrorCode: 8, 2, Motor2, SeatECU ;
  Motor2ErrorValue: 8, 4, Motor2, SeatECU ;
  Motor2LinError: 1, 0, Motor2, SeatECU ;
  Motor2Position: 32, {0, 0, 0, 0}, Motor2, SeatECU ;
  Motor2Selection: 2, 0, SeatECU, Motor2 ;
  Motor2Temp: 8, 0, Motor2, SeatECU ;
  MotorDirection: 2, 0, SeatECU, Motor1, Motor2 ;
  MotorSpeed: 10, 0, SeatECU, Motor1, Motor2 ;
}

Diagnostic_signals {
  MasterReqB0: 8, 0 ;
  MasterReqB1: 8, 0 ;
  MasterReqB2: 8, 0 ;
  MasterReqB3: 8, 0 ;
  MasterReqB4: 8, 0 ;
  MasterReqB5: 8, 0 ;
  MasterReqB6: 8, 0 ;
  MasterReqB7: 8, 0 ;
  SlaveRespB0: 8, 0 ;
  SlaveRespB1: 8, 0 ;
  SlaveRespB2: 8, 0 ;
  SlaveRespB3: 8, 0 ;
  SlaveRespB4: 8, 0 ;
  SlaveRespB5: 8, 0 ;
  SlaveRespB6: 8, 0 ;
  SlaveRespB7: 8, 0 ;
}



Frames {
  Motor1Control: 48, SeatECU, 1 {
    Motor1Selection, 0 ;
  }
  Motor1State_Cycl: 51, Motor1, 6 {
    Motor1Temp, 0 ;
    Motor1Position, 8 ;
    Motor1LinError, 40 ;
  }
  Motor1State_Event: 54, Motor1, 3 {
    Motor1ErrorCode, 8 ;
    Motor1ErrorValue, 16 ;
  }
  Motor2Control: 49, SeatECU, 1 {
    Motor2Selection, 0 ;
  }
  Motor2State_Cycl: 52, Motor2, 6 {
    Motor2Temp, 0 ;
    Motor2Position, 8 ;
    Motor2LinError, 40 ;
  }
  Motor2State_Event: 55, Motor2, 3 {
    Motor2ErrorCode, 8 ;
    Motor2ErrorValue, 16 ;
  }
  MotorsControl: 45, SeatECU, 2 {
    MotorDirection, 0 ;
    MotorSpeed, 2 ;
  }
}



Diagnostic_frames {
  MasterReq: 0x3c {
    MasterReqB0, 0 ;
    MasterReqB1, 8 ;
    MasterReqB2, 16 ;
    MasterReqB3, 24 ;
    MasterReqB4, 32 ;
    MasterReqB5, 40 ;
    MasterReqB6, 48 ;
    MasterReqB7, 56 ;
  }
  SlaveResp: 0x3d {
    SlaveRespB0, 0 ;
    SlaveRespB1, 8 ;
    SlaveRespB2, 16 ;
    SlaveRespB3, 24 ;
    SlaveRespB4, 32 ;
    SlaveRespB5, 40 ;
    SlaveRespB6, 48 ;
    SlaveRespB7, 56 ;
  }
}

Node_attributes {
  Motor1{
    LIN_protocol = "2.1" ;
    configured_NAD = 0x2 ;
    initial_NAD = 0xA ;
    product_id = 0x1E, 0x1, 0 ;
    response_error = Motor1LinError ;
    fault_state_signals = Motor1ErrorValue, Motor1ErrorCode ;
    P2_min = 100 ms ;
    ST_min = 20 ms ;
    N_As_timeout = 1000 ms ;
    N_Cr_timeout = 1000 ms ;
    configurable_frames {
      MotorsControl ;
      Motor1Control ;
      Motor1State_Cycl ;
      Motor1State_Event ;
    }
  }
  Motor2{
    LIN_protocol = "2.1" ;
    configured_NAD = 0x3 ;
    initial_NAD = 0xC ;
    product_id = 0x2E, 0xB, 1 ;
    response_error = Motor2LinError ;
    fault_state_signals = Motor2ErrorValue, Motor2ErrorCode ;
    P2_min = 50 ms ;
    ST_min = 0 ms ;
    N_As_timeout = 1000 ms ;
    N_Cr_timeout = 1000 ms ;
    configurable_frames {
      MotorsControl ;
      Motor2Control ;
      Motor2State_Cycl ;
      Motor2State_Event ;
    }
  }
}

Schedule_tables {
 NormalTable {
    MotorsControl delay 10 ms ;
    Motor1State_Cycl delay 10 ms ;
    Motor2State_Cycl delay 10 ms ;
  }
 Motor1Table {
    Motor1Control delay 5 ms ;
    Motor1State_Event delay 10 ms ;
  }
}


Signal_encoding_types {
  encTemperature {
    physical_value, 0, 80, 0.5, -20, "Degree" ;
  }
}

Signal_representation {
  encTemperature: Motor1Temp, Motor2Temp ;
}
